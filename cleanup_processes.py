#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程清理脚本
用于清理 app.py 产生的残留 multiprocessing 进程

使用方法：
    python cleanup_processes.py
    或者：
    ./cleanup_processes.py
"""

import os
import sys
import psutil
import logging
import argparse
from typing import List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def find_app_processes() -> List[psutil.Process]:
    """查找与 app.py 相关的进程"""
    processes = []
    current_user = os.getenv('USER', os.getenv('USERNAME', ''))
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'username']):
        try:
            # 只处理当前用户的进程
            if proc.info['username'] != current_user:
                continue
                
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            
            # 查找 app.py 主进程
            if 'python' in proc.info['name'] and 'app.py' in cmdline:
                processes.append(proc)
                logging.info(f"Found main process: PID {proc.info['pid']} - {cmdline[:100]}...")
            
            # 查找 multiprocessing 相关进程
            elif ('python' in proc.info['name'] and 
                  ('multiprocessing' in cmdline or 'spawn_main' in cmdline or 'resource_tracker' in cmdline)):
                processes.append(proc)
                logging.info(f"Found multiprocessing process: PID {proc.info['pid']} - {cmdline[:100]}...")
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    return processes

def cleanup_processes(force: bool = False) -> bool:
    """清理进程"""
    processes = find_app_processes()
    
    if not processes:
        logging.info("没有找到需要清理的进程")
        return True
    
    logging.info(f"找到 {len(processes)} 个需要清理的进程")
    
    # 首先尝试优雅终止
    if not force:
        logging.info("正在尝试优雅终止进程...")
        for proc in processes:
            try:
                proc.terminate()
                logging.info(f"发送TERM信号给进程 {proc.pid}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 等待进程终止
        gone, alive = psutil.wait_procs(processes, timeout=10)
        
        if alive:
            logging.warning(f"仍有 {len(alive)} 个进程在运行，将强制终止")
            processes = alive
        else:
            logging.info("所有进程已优雅终止")
            return True
    
    # 强制终止
    logging.info("正在强制终止进程...")
    for proc in processes:
        try:
            proc.kill()
            logging.info(f"强制终止进程 {proc.pid}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # 最终检查
    remaining = find_app_processes()
    if remaining:
        logging.error(f"仍有 {len(remaining)} 个进程无法清理")
        for proc in remaining:
            logging.error(f"  PID {proc.pid}: {' '.join(proc.cmdline())[:100]}...")
        return False
    else:
        logging.info("所有进程清理完成")
        return True

def main():
    parser = argparse.ArgumentParser(description='清理 app.py 相关的残留进程')
    parser.add_argument('--force', '-f', action='store_true', 
                       help='直接强制终止进程，不尝试优雅关闭')
    parser.add_argument('--list', '-l', action='store_true',
                       help='只列出相关进程，不进行清理')
    
    args = parser.parse_args()
    
    if args.list:
        processes = find_app_processes()
        if processes:
            print(f"找到 {len(processes)} 个相关进程:")
            for proc in processes:
                cmdline = ' '.join(proc.cmdline()) if proc.cmdline() else ''
                print(f"  PID {proc.pid}: {cmdline[:100]}...")
        else:
            print("没有找到相关进程")
        return
    
    success = cleanup_processes(force=args.force)
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main() 