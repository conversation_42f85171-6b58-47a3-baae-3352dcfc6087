# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/5/7 10:10
import hashlib
import time
from collections import defaultdict

import traceback
import fitz
from PIL import Image
import io
import base64
import logging
import re
from typing import Union, List

from commons.thread.multiprocess import MultiProcess
from modules.common import calculate_original_bbox, upload_image
from modules.entity.checkbox_entity import CheckBoxCorrect
from modules.entity.parse_entity import Image as ParseImage, ImageType
from commons.trace.tracer import async_trace_span
from modules.utils import ConnPool
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import PipelineContext, FileType
from modules.entity.dst_entity import BBox, DSTType
from conf import ConfHandlerName

_store_dir = "insight-ai/doc-parser"
OVER_SIZE = 10
DESIRED_DPI = 100

overlap = 3

def get_table_crop(pdf_document, dsts, file_info) -> list:
    table_pic_list = []
    for dst in dsts:
        logging.debug(f"Processing dst: {dst}")
        if dst.dst_type == DSTType.TABLE:
            bbox = dst.attributes.position.bbox
            page_num = dst.attributes.page
            page = pdf_document.load_page(page_num)
            logging.debug(f"Processing bbox: {bbox}")
            x0, y0, x1, y1 = calculate_original_bbox(bbox, file_info.width, file_info.height)
            # 将bbox的单位由kdc的单位缇转换为fitz的单位point
            x0 = x0 / 20 - overlap
            y0 = y0 / 20 - overlap
            x1 = x1 / 20 + overlap
            y1 = y1 / 20 + overlap
            clip = fitz.Rect(x0, y0, x1, y1)
            scale = DESIRED_DPI / 72
            pix = page.get_pixmap(matrix=fitz.Matrix(scale, scale), clip=clip)
            img = Image.frombytes("RGB", (pix.width, pix.height), pix.samples)
            # Convert the PIL Image to a base64 string

            if bbox.rotate and abs(bbox.rotate) != 0:
                img = img.rotate(-360 + bbox.rotate, expand=True)
            buffered = io.BytesIO()
            img.save(buffered, format="PNG")
            img_base64 = base64.b64encode(buffered.getvalue()).decode("utf-8")
            if img_base64:
                image_url = upload_image(img_base64)
                table_pic_list.append(
                    ParseImage(page_num=page_num, url=image_url, image_type=ImageType.TABLE_IMAGE,
                               chunk_ids=None, dst_ids=[dst.id]))
            else:
                continue

    return table_pic_list


def get_table_page_crop(pdf_document, dsts, file_info, rotate_map, captured_images) -> list:
    table_pic_list = []
    page_count = {}
    if file_info.is_scan:
        return table_pic_list
    for dst in dsts:
        if dst.dst_type == DSTType.TABLE:
            page_num = dst.attributes.page
            if page_num in page_count:
                page_count[page_num] += 1
            else:
                page_count[page_num] = 1

    unique_pages = list(page_count.keys())
    # 表格的页截图
    for page_num in unique_pages:
        image_url = cut_single_page(file_info.width, file_info.height, page_num, pdf_document,
                                    rotate_map.get(page_num, 0), captured_images)
        if image_url:
            table_pic_list.append(
                ParseImage(page_num=page_num, url=image_url, image_type=ImageType.SCAN_IMAGE, chunk_ids=[]))

    return table_pic_list


def get_pdf_scan_crop(pdf_document, file_info, rotate_map, captured_images) -> list:
    scan_pdf_list = []
    for index in range(file_info.page_size):
        image_url = cut_single_page(file_info.width, file_info.height, index, pdf_document, rotate_map.get(index, 0),
                                    captured_images)
        if image_url:
            scan_pdf_list.append(ParseImage(page_num=index, url=image_url, image_type=ImageType.SCAN_IMAGE))
        else:
            continue
    return scan_pdf_list


def cut_single_page(width, height, index, pdf_document, rotate, captured_images=None):
    if captured_images is not None:
        if index in captured_images:
            return captured_images[index]

    image_url = None
    bbox = BBox(x1=0, y1=0, x2=width, y2=height)
    x0 = bbox.x1
    y0 = bbox.y1
    x1 = bbox.x2
    y1 = bbox.y2
    img_base64 = cut_pic(index, pdf_document, rotate, x0, x1, y0, y1)
    if img_base64:
        image_url = upload_image(img_base64)
        # 缓存url
        if captured_images is not None and image_url:
            captured_images[index] = image_url

    return image_url


def cut_pic(index, pdf_document, rotate, x0, x1, y0, y1):
    page = pdf_document.load_page(index)
    # 将bbox的单位由kdc的单位缇转换为fitz的单位point
    x0 = x0 / 20
    y0 = y0 / 20
    x1 = x1 / 20
    y1 = y1 / 20
    clip = fitz.Rect(x0, y0, x1, y1)
    scale = DESIRED_DPI / 72
    pix = page.get_pixmap(matrix=fitz.Matrix(scale, scale), clip=clip)
    img = Image.frombytes("RGB", (pix.width, pix.height), pix.samples)
    if abs(rotate) != 0:
        img = img.rotate(-360 + rotate, expand=True)
    # Convert the PIL Image to a base64 string
    buffered = io.BytesIO()
    img.save(buffered, format="PNG")
    img_base64 = base64.b64encode(buffered.getvalue()).decode("utf-8")
    return img_base64


# 判断是否至少有两个匹配， 稍微耗时一些
def has_multiple_specific_chars(content: str) -> bool:
    pattern = re.compile(r'[☑凶☒冈区√图■図□口☐]|\(\s*\√\s*\)|\(\s*\s*\)')

    if not bool(pattern.search(content)):
        return False
    # 先快速检查是否有至少2个匹配（提前退出）
    if len(pattern.findall(content)) < 2:
        return False

    # 再用集合去重确认
    matches = set(pattern.findall(content))
    if matches is None or len(matches) == 0:
        return False
    # if "X" in matches and "x" in matches:
    #     return len(matches) >= 3  # 如果同时有大写和小写的X，认为是多选
    return len(matches) >= 2


def classify_and_filter_dsts(dsts):
    page_map = defaultdict(list)
    content = []
    # Group content and dst_ids by page number
    page_content_map = defaultdict(lambda: {"dst_ids": [], "content": ""})
    for dst in dsts:
        if dst.dst_type == DSTType.IMAGE or dst.mark is not None:
            continue
        page_num = dst.attributes.page
        page_content_map[page_num]["dst_ids"].append(dst.id)
        page_content_map[page_num]["content"] += "".join(dst.content)

    # Check for '□' or '☑' in content and populate page_map
    for page_num, data in page_content_map.items():
        if has_multiple_specific_chars(data["content"]):
            page_map[page_num] = data["dst_ids"]
            crop_correnct = CheckBoxCorrect(content=data["content"], page=page_num)
            content.append(crop_correnct)

    return page_map, content


def get_checkbox_crop(context: PipelineContext, file_info, dsts) -> list:
    if file_info.file_type not in [FileType.PDF]:
        logging.warning(f"Unsupported file type: {file_info.file_type}, skip CropNode.")
        return []
    # elif file_info.file_type in ["docx", "doc"]:
    #     with io.BytesIO(file_bytes) as docx_stream:
    #         document = Document(docx_stream)
    # if pdf_document is None:
    #     return []
    rotate_map = {}
    if context.file_info.rotate_page:
        for rotate, pages in context.file_info.rotate_page.items():
            for page in pages:
                rotate_map[page] = rotate
    file_bytes = load_pdf_bytes(context.kdc_input.file_url_or_bytes)

    pdf_document = fitz.open(stream=file_bytes, filetype="pdf")

    # 收集需要截图的页面
    page_map, checkboxs = classify_and_filter_dsts(dsts)
    # 处理页面截图
    for page_num, dst_ids in page_map.items():
        # 生成全页截图
        image_url = cut_single_page(file_info.width,
                                    file_info.height,
                                    page_num,
                                    pdf_document,
                                    rotate_map.get(page_num, 0),
                                    None)
        if image_url:
            image = ParseImage(page_num=page_num, url=image_url, dst_ids=dst_ids, image_type=ImageType.CHECK_BOX_IMAGE)
            for checkbox in checkboxs:
                if checkbox.page == page_num:
                    checkbox.images = [image]
    return checkboxs



def load_pdf_bytes(file_url_or_bytes: Union[str, bytes]) -> bytes:
    if isinstance(file_url_or_bytes, str):
        file_resp = ConnPool().get_file_req().get(file_url_or_bytes, verify=False)
        file_resp.raise_for_status()
        return file_resp.content
    return file_url_or_bytes

def crop_shot(context, res):
    if context.image:
        res = context.image
    else:
        rotate_map = {}
        if context.file_info.rotate_page:
            for rotate, pages in context.file_info.rotate_page.items():
                for page in pages:
                    rotate_map[page] = rotate
        if context.file_info.file_type != "pdf":
            logging.warning(f"Unsupported file type: {context.file_info.file_type}, skip CropNode.")
        else:
            captured_images = {}
            file_bytes = load_pdf_bytes(context.kdc_input.file_url_or_bytes)
            pdf_document = fitz.open(stream=file_bytes, filetype="pdf")

            if context.file_info.is_scan:
                scan_res = get_pdf_scan_crop(pdf_document, context.file_info, rotate_map, captured_images)
                res.extend(scan_res)
            # 处理表格的页截图
            table_res = get_table_page_crop(pdf_document, context.handler_results[ConfHandlerName.dst_handler],
                                            context.file_info, rotate_map,
                                            captured_images)
            if len(table_res) > 0:
                res.extend(table_res)

            table_pic_res = get_table_crop(pdf_document, context.handler_results[ConfHandlerName.dst_handler],
                                               context.file_info)
            if len(table_pic_res) > 0:
                res.extend(table_pic_res)
    return res


class CropNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        res = []
        try:
            # res = crop_shot(context, res)
            _t0 = time.perf_counter()

            res = await MultiProcess().arun(crop_shot, context, res)
            # 这个handler的结果应该把上面三个方法的结果抽出来一个结构体，将这个结构图作为这个handler的结果
            dur = time.perf_counter() - _t0
            context.business_log.debug(f"CropNode {self.name} executed in {dur:.2f} seconds")
            context.handler_results[self.name] = res
            return context

        except Exception as e:
            logging.error(f"Error in CropNode.process: {e}")
            raise e
            logging.exception(f"""
            !!! CropNode.process FAILED !!!
            Context Summary:
            - File type: {getattr(context.file_info, 'file_type', 'N/A')}
            - Is scan: {getattr(context.file_info, 'is_scan', 'N/A')}
            - Rotate pages: {getattr(context.file_info, 'rotate_page', 'N/A')}
            - Handler results keys: {list(getattr(context, 'handler_results', {}).keys())}
            - Input type: {type(getattr(getattr(context, 'kdc_input', None), 'file_url_or_bytes', None))}
            """)
            raise  # 重新抛出异常以保持原有错误处理流


