# Author: linqi
# Date: 2025/7/10
# Time: 10:52
import logging
from io import BytesIO
from PIL import Image
import fitz
import io
import base64
from dataclasses import dataclass, field
from typing import List, Union
from modules.entity.base import FileURL
from modules.entity.pre_check_entity import ParserConfig, ParserName
from modules.flows.pre_check.pre_check_template import PreCheckTemplate
from commons.trace.tracer import async_trace_span
from modules.common import _upload_image
from modules.pipeline.context import PipelineContext, FileInfo, FileType
import aiohttp
from commons.rpc.rpc_factory import RpcFactory
from commons.rpc.normal_model.rpc_interface import TableClsEnum

scan_text_limit = 10


async def extract_and_upload_full_page_images(pdf_document: fitz.Document) -> list[dict]:
    """
    提取PDF中与页面尺寸匹配的图像并进行上传

    :param pdf_document: 已打开的PDF文档对象
    :return: 包含页码和图片URL的字典列表 [{"page_num": int, "image_url": str}]
    """
    cover_image = []
    try:
        for page_num in range(pdf_document.page_count):
            p = pdf_document.load_page(page_num)
            page_width = p.rect.width
            page_height = p.rect.height

            for img in p.get_images(full=True):
                xref = img[0]
                # 提取图像数据
                image_data = p.parent.extract_image(xref)
                image_bytes = io.BytesIO(image_data["image"])
                image = Image.open(image_bytes)
                image_width, image_height = image.size  # 获取图像原始尺寸

                # 判断是否与页面尺寸匹配
                if (image_width - page_width > 0 and image_height - page_height > 0) or (
                        page_width - image_width < 0.1 * page_width and page_height - image_height < 0.1 * page_height):
                    # 如果图像尺寸与页面尺寸相差不大，或者图片尺寸远大于页面尺寸，则认为是封面图
                    # 这里的判断条件可以根据实际需求调整

                    # 将图像转换为base64字符串
                    buffer = io.BytesIO()
                    image.save(buffer, format="PNG")
                    img_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")
                    buffer.close()

                    # 上传图像并获取URL
                    image_url = await _upload_image('', img_base64)
                    cover_image.append({
                        "page_num": p.number,
                        "image_url": image_url,
                    })

        return cover_image
    except Exception as e:
        logging.error(f"Error extracting images from PDF: {e}")
        return []

class FileLoader:
    """
    PDF文件加载器
    """
    def __init__(self, url_or_bytes: Union[str, bytes], timeout: int = 30):
        self.url_or_bytes = url_or_bytes
        self.document = None
        self.timeout = timeout

    async def load_document(self) -> fitz.Document:
        """
        加载PDF文件
        """
        if self.document is None:
            if isinstance(self.url_or_bytes, str):
                # 验证URL
                FileURL(self.url_or_bytes).validate()
                # 超时控制
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                # 创建会话
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # 发送请求
                    async with session.get(self.url_or_bytes) as response:
                        response.raise_for_status()
                        # 读取响应内容
                        content = await response.read()
                        # 打开PDF文档
                        self.document = fitz.open(stream=BytesIO(content), filetype="pdf")
            else:
                # 读取二进制数据
                self.document = fitz.open(stream=self.url_or_bytes, filetype="pdf")

        return self.document

    def get_page_count(self) -> int:
        """
        获取PDF文件页数
        """
        return self.document.page_count

    def close_document(self) -> None:
        """
        关闭PDF文件
        """
        if self.document:
            self.document.close()
            self.document = None

@dataclass
class AnalyzeResult:
    """
    页面的信息
    """
    image_only_pages: List[int] = field(default_factory=list)
    pages_with_tables: List[int] = field(default_factory=list)
    full_text: List[str] = field(default_factory=list)
    file_info: FileInfo = None

class PageAnalyzer:
    """
    分析单个页面的图、文、表分布情况
    """
    def __init__(self, document : fitz.Document):
        self.document = document
        self.analyze_result = AnalyzeResult()
        self.analyze_result.file_info = FileInfo(
            page_size=0,
            file_type=FileType.PDF,
            width=0,
            height=0,
            rotate_page={},
        )

    def analyze(self, start_page: int = 0, end_page: int = None) -> AnalyzeResult:
        try:
            if end_page is None:
                end_page = self.document.page_count
            for page_index in range(start_page, end_page):
                # 加载页面
                page = self.document.load_page(page_index)
                # 分析页面
                self._analyze(page)
            return self.analyze_result
        except Exception as e:
            logging.error(f"Error in analyze_pages: {e}")
            raise e

    def _analyze(self, page: fitz.Page) -> None:
        """
        分析页面
        """
        try:
            # 获取页面尺寸
            width = page.rect.width
            height = page.rect.height
            # 处理页面旋转
            rotation = page.rotation
            if rotation in (90, 270):
                width, height = height, width
            # 更新文件信息
            file_info = self.analyze_result.file_info
            file_info.width = max(file_info.width, int(width))
            file_info.height = max(file_info.height, int(height))
            # 更新旋转页面
            if rotation != 0:
                file_info.rotate_page[rotation] = file_info.rotate_page.get(rotation, [])
                file_info.rotate_page[rotation].append(page.number)
            file_info.page_size += 1

            # 提取页面文本
            text = page.get_text().strip()
            if text:
                self.analyze_result.full_text.append(text)

            # 检查页面是否包含图像
            images = page.get_images()
            has_images = bool(images) # 列表判空

            # 检查页面是否包含表格
            tables = page.find_tables()
            has_tables = bool(tables.tables) # 列表判空
            if has_tables:
                self.analyze_result.pages_with_tables.append(page.number)

            # 没有文本，没有表格，只有图片
            # todo:这里有个问题，如果这一页有header，但是没有其他文本内容，那么这一页也会被识别为只有图片的页面
            if not text and not has_tables and has_images:
                self.analyze_result.image_only_pages.append(page.number)
        except Exception as e:
            logging.error(f"Error in analyze_page: {e}")
            raise e

    def pages_result(self) -> AnalyzeResult:
        return self.analyze_result


class PDFPreCheck(PreCheckTemplate):

    @async_trace_span
    async def precheck_process(self, context: PipelineContext):
        """
        预检查PDF文件并确定哪些页面需要进行后续处理

        :param context: 上下文
        :return: 包含预检查结果的字典
        """

        file_url_or_bytes = context.kdc_input.file_url_or_bytes
        max_page_count = context.page_count
        # 初始化结果
        kdc_config = ParserConfig(
            parser_name=ParserName.KdcParser,
            is_all=False,
            processing_pages=set(),
        )
        file_loader = FileLoader(file_url_or_bytes)
        document = await file_loader.load_document()
        # 获取页数
        page_size = file_loader.get_page_count()

        # 分析页面内容
        page_analyzer = PageAnalyzer(document)
        analyze_result = page_analyzer.analyze(0, page_size)

        # 检查页面数量
        file_page = analyze_result.file_info.page_size
        # 检查页面数量是否超过限制
        if max_page_count is not None and file_page > max_page_count > 0:
            raise ValueError(f"File page count ({file_page}) exceeds limit ({max_page_count})")
        context.file_info.page_size = file_page

        try:
            # 获取pdf的书签
            # 1. 识别只包含图片的页面
            image_only_pages, tables_pages,full_text = analyze_result.image_only_pages, analyze_result.pages_with_tables, analyze_result.full_text
            context.business_log.debug(f"Image-only pages: {image_only_pages}")

            file_info = analyze_result.file_info

            context.file_info.page_size = file_info.page_size
            context.file_info.word_count = file_info.word_count
            context.file_info.width = file_info.width
            context.file_info.height = file_info.height
            context.file_info.rotate_page = file_info.rotate_page
            context.file_info.word_count = len(full_text)

            ##代表为全图片的pdf，可能是扫描件，可能是全图片的标准件 ,或者文本内容过少
            if file_page == len(image_only_pages) or len(full_text)/file_page < scan_text_limit:
                kdc_config.is_all = True
                context.file_info.is_scan = True
                return [kdc_config]

            # toc = pdf_document.get_toc()
            # if toc is None or len(toc) == 0:
            #     kdc_config.is_all = True
            #     return [kdc_config]
            ##图片页走kdc
            if image_only_pages:
                kdc_config.processing_pages.update(image_only_pages)

            # 2. 识别包含表格的页面
            context.business_log.debug(f"Table pages: {tables_pages}")
            if tables_pages:
                kdc_config.processing_pages.update(tables_pages)

            # 3. 提取与页面尺寸匹配的图像并检测表格类型
            cover_images = await extract_and_upload_full_page_images(document)
            if cover_images:
                context.business_log.debug(f"Cover images: {cover_images}")
                for img_info in cover_images:
                    page_num = img_info["page_num"]
                    img_url = img_info["image_url"]

                    # 调用OCR服务进行表格分类
                    res = await RpcFactory().get_normal_model_rpc().arequest_table_cls(img_url)
                    if not res.is_success():
                        continue
                    else:
                        table_cls = res.data.table_type

                    # 如果检测到"wired"类型的表格，将该页标记为需要处理
                    if table_cls == TableClsEnum.wired:
                        kdc_config.processing_pages.add(page_num)
            mupdf_page = set()
            # 遍历kdc_config的page，将未在里面的页码放到mupdf_config里
            kdc_config.processing_pages = sorted(kdc_config.processing_pages)
            for page_num in range(0, file_page):
                if page_num not in kdc_config.processing_pages:
                    mupdf_page.add(page_num)
            has_kdc_pages = bool(kdc_config.processing_pages)
            has_mupdf_pages = bool(mupdf_page)

            mupdf_config = None
            if has_mupdf_pages:
                mupdf_config = ParserConfig(
                    parser_name=ParserName.MuPdfParser,
                    is_all=False,
                    processing_pages=mupdf_page
                )

            if has_kdc_pages and has_mupdf_pages:
                return [kdc_config, mupdf_config]
            elif has_kdc_pages:
                return [kdc_config]
            else:
                return [mupdf_config]
        except Exception as e:
            if isinstance(e, ValueError):
                raise e  # Re-raise the ValueError
            context.business_log.error(f"Error during PDF pre-check: {e}")
            return [kdc_config]
        finally:
            # 确保PDF文档被关闭
            file_loader.close_document()

