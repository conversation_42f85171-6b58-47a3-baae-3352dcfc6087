# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/4/21 17:55
import json
from collections import defaultdict, deque
from enum import Enum
from typing import List, Union, Optional, Dict
from typing import NamedTuple

from pydantic import BaseModel, Field, field_validator

"""
1. DST Node节点定义
{
    "type": str,        #节点类型
    "id": str,          #节点id
    "attributes": {     #节点属性字典
        "level": 0,         #节点级别
        "position": {},         #位置信息
        "page": 1,          #页码
        "hash": str,        #hash值
        },
    "content": [],      #节点内容，完整句子的数组
    "parent": str,      #父节点id
    "order":int,        #同级节点顺序
}

2. type 节点类型
    - root，根节点，一个文件只有一个，表示文件级的基础信息
    - title，标题节点
    - text，文本段落
    - table，表格段落
    - image，图片段落
    - code，代码段落
    - formula，公式段落

"""

class ImageContentIndex(NamedTuple):
    """图片内容在DST的content列表中的索引位置定义"""
    URL: int = 0    # 图片URL（第一个元素）
    OCR: int = 1    # 图片OCR结果（第二个元素）
    DESC: int = 2   # 图片描述（第三个元素）

# 创建实例
IMAGE_INDEX = ImageContentIndex()


class BBox(BaseModel):
    """定义block的bounding box"""
    x1: int
    y1: int
    x2: int
    y2: int
    rotate: Optional[int] = Field(None, description="旋转角度，默认0，顺时针方向")


class BlockCoordinate(BaseModel):
    """otl、txt、docx、doc文件的block位置信息"""
    block_id: Optional[str] = Field(None, description="文本块ID")
    gcp: Optional[int] = Field(None, description="全局字符位置")
    len: Optional[int] = Field(None, description="长度")


class PositionInfo(BaseModel):
    """统一的位置信息结构体，包含多种类型的位置描述"""
    bbox: Optional[BBox] = Field(None, description="边界框位置信息")
    block_coordinate: Optional[BlockCoordinate] = Field(None, description="文本块位置信息")


class ExcelCoordinate(BaseModel):
    """Excel格式文档节点的位置信息"""
    sheet: str
    top_left_span: str
    bottom_left_span: str
    top_right_span: str
    bottom_right_span: str


class DSTAttribute(BaseModel):
    """文档结构树(DST)节点的属性信息

    Attributes:
        level (int): 节点在文档树中的层级，从0开始计数
        position (Union[BBox, str]): 节点在文档中的位置信息
            - 对于doc/docx/pdf格式，使用BBox表示位置
            - 对于otl格式，使用block id表示位置
            - 对于excel格式，使用ExcelCoordinate表示位置
        page (int): 节点所在的页码，从1开始计数
        hash (str): 节点内容的哈希值，用于内容去重和版本控制
    """

    level: int = Field(..., ge=0, description="节点层级，从0开始计数")
    position: Union[PositionInfo, str, ExcelCoordinate] = Field(
        ...,
        description="节点位置信息，支持PositionInfo、str或ExcelCoordinate",
        json_schema_extra={
            "example": {
                "doc/docx/pdf": {"x1": 0, "y1": 0, "x2": 100, "y2": 100},
                "otl": "block_123",
                "xls/xlsx": {"sheet": "sheet1", "top_left_span": "A2", "bottom_left_span": "A11", "top_right_span": "E2",
                             "bottom_right_span": "E11"}
            }
        }
    )
    page: int = Field(..., ge=0, description="节点所在页码，从0开始计数")
    hash: str = Field(..., min_length=32, max_length=64, description="节点内容的哈希值")

    @field_validator('level')
    @classmethod
    def validate_level(cls, v):
        """验证level的有效性"""
        if v < 0:
            raise ValueError("level不能小于0")
        return v

    @field_validator('page')
    @classmethod
    def validate_page(cls, v):
        """验证page的有效性"""
        if v < 0:
            raise ValueError("page不能小于0")
        return v

    @field_validator('hash')
    @classmethod
    def validate_hash(cls, v):
        """验证hash的有效性"""
        if not v:
            raise ValueError("hash不能为空")
        return v

    @field_validator('position')
    @classmethod
    def validate_position(cls, v):
        """验证position的有效性"""
        if isinstance(v, PositionInfo):
            # 验证内部的bbox字段
            if v.bbox is not None:
                # ppt 的bbox会是负数，所以只检查相对位置关系
                if v.bbox.x2 < v.bbox.x1 or v.bbox.y2 < v.bbox.y1:
                    raise ValueError("PositionInfo中的BBox坐标值无效")
            # BlockCoordinate的校验可以在这里添加
            if v.block_coordinate is not None:
                # 后续补充校验
                pass
        elif isinstance(v, BBox):
            # 兼容直接使用BBox的情况
            if v.x2 < v.x1 or v.y2 < v.y1:
                raise ValueError("BBox坐标值无效")
        elif isinstance(v, BlockCoordinate):
            # 兼容直接使用BlockCoordinate的情况
            pass
        elif isinstance(v, ExcelCoordinate):
            # 后续补充校验
            pass
        elif not isinstance(v, str):
            raise ValueError("position必须是PositionInfo、BBox、BlockCoordinate对象或字符串")
        return v


class DSTType(str, Enum):
    """定义DST 节点的类型"""

    ROOT = "root"
    TITLE = "title"
    TEXT = "text"
    TABLE = "table"
    IMAGE = "image"
    CODE = "code"
    FORMULA = "formula"
    DBSHEET = "dbsheet"
    VIDEO = "video"
    AUDIO = "audio"
    CHART = "chart"
    OTHER = "other"
    MINDMAP = "mindmap"
    FLOWCHART = "flowchart"
    SPREADSHEET = "spreadsheet"


class MarkType(str, Enum):
    HEADER = "header"
    FOOTER = "footer"
    CATALOG = "catalog"


class DST(BaseModel):
    id: str
    parent: str
    order: int
    dst_type: DSTType
    attributes: DSTAttribute
    # 不同dst_type的content格式不同
    # dst_type=title/text/table/code/formula时，content放文本
    # dst_type=image时，content[0]放图片url， content[1]放ocr结果
    content: List[str]
    image_pixel: Optional[List[int]] = Field(None, description="图片像素的宽高")
    mark: Optional[str] = Field(None, description="页眉页脚标记")
    font_size: Optional[float] = Field(-1, description="字体大小")
    bold: Optional[bool] = Field(False, description="是否加粗")
    table_mark: Optional[str] = Field(None, description="表格标记")

    def ensure_image_content_length(self, index: int) -> None:
        """确保content列表长度足够存储指定索引的内容

        Args:
            index: 需要访问的索引位置
        """
        while len(self.content) <= index:
            self.content.append("")

    def set_image_content(self, index: int, value: str) -> None:
        """安全设置图片内容，自动处理数组扩展避免索引越界

        Args:
            index: 内容在列表中的索引位置
            value: 要设置的内容值
        """

        self.ensure_image_content_length(index)
        self.content[index] = value

    def set_image_url(self, url: str) -> None:
        """设置图片URL"""
        self.set_image_content(IMAGE_INDEX.URL, url)

    def set_image_ocr(self, ocr_text: str) -> None:
        """设置图片OCR结果"""
        self.set_image_content(IMAGE_INDEX.OCR, ocr_text)

    def set_image_description(self, description: str) -> None:
        """设置图片描述"""
        self.set_image_content(IMAGE_INDEX.DESC, description)

class Step(str, Enum):
    DST = "dst"
    CHUNK = "chunk"
    FAKE_TITLE = "fake_title"
    SUMMARY = "summary"
    KEYWORDS = "keywords"


class FileType(str, Enum):
    DOC = "doc"
    DOCX = "docx"
    PDF = "pdf"
    OTL = "otl"
    TXT = "txt"

    JPEG = "jpeg"
    JPG = "jpg"
    PNG = "png"
    WEBP = "webp"

    XLSX = "xlsx"
    XLS = "xls"

    PPT = "ppt"
    PPTX = "pptx"


def sort_dst_list(dst_list):
    # 构建 parent -> children 的映射
    parent_map = defaultdict(list)
    for dst in dst_list:
        parent_map[dst.parent].append(dst)

    # 根据 order 对每个 parent 的子节点排序
    for parent, children in parent_map.items():
        children.sort(key=lambda x: x.order)

    # 深度优先前序遍历
    def dfs(parent_id):
        sorted_list = []
        for child in parent_map.get(parent_id, []):
            sorted_list.append(child)
            sorted_list.extend(dfs(child.id))
        return sorted_list

    # 从根节点开始遍历 (假设根节点的 parent 为 "-1")
    return dfs("-1")


def breadth_first_sort(dst_list: List[DST]) -> List[DST]:
    # Step 1: Group DSTs by their parent
    parent_map = defaultdict(list)
    for dst in dst_list:
        parent_map[dst.parent].append(dst)

    # Step 2: Sort each group by 'order'
    for parent, children in parent_map.items():
        parent_map[parent] = sorted(children, key=lambda x: x.order)

    # Step 3: Perform breadth-first traversal
    sorted_dst_list = []
    queue = deque(parent_map.get("-1", []))  # Start with root nodes (parent == -1)

    while queue:
        current = queue.popleft()
        sorted_dst_list.append(current)
        # Add children of the current node to the queue
        queue.extend(parent_map.get(current.id, []))

    return sorted_dst_list


def print_dst_tree(dst_list: list):
    """
    Prints the DST tree structure based on parent-child relationships.
    Assumes the input list is already sorted.

    :param dst_list: List of DST objects
    """
    # Filter out DSTs with level == 10
    filtered_dst = [dst for dst in dst_list if dst.attributes.level != 10]

    # Track indentation for each parent
    parent_indent = {"-1": ""}

    for dst in filtered_dst:
        # Get the indentation for the current node
        indent = parent_indent.get(dst.parent, "")
        content = "".join(dst.content)  # Merge content
        print(f"{indent}├─ {content}")

        # Set the indentation for this node's children
        parent_indent[dst.id] = indent + "│  "


def print_dst_indent_tree(dst_list: list) -> str:
    """
    Prints the DST tree structure based on parent-child relationships.
    Assumes the input list is already breadth-first sorted.

    :param dst_list: List of DST objects
    :return: A string containing the DST tree structure
    """

    # Track indentation for each parent
    parent_indent = {"-1": ""}
    text = ""  # Initialize an empty string to accumulate the output

    for dst in dst_list:
        # Get the indentation for the current node
        indent = parent_indent.get(dst.parent, "")
        content = "".join(dst.content)  # Merge content
        text += f"{indent}  {content}\n"  # Append the formatted content to the text

        # Set the indentation for this node's children
        parent_indent[dst.id] = indent + "  "

    return text  # Return the accumulated text


def get_dst_indent_tree(dst_list: list) -> str:
    """
    Prints the DST tree structure based on parent-child relationships.
    Assumes the input list is already breadth-first sorted.

    :param dst_list: List of DST objects
    :return: A string containing the DST tree structure
    """

    # Track indentation for each parent
    parent_indent = {"-1": ""}
    text = ""  # Initialize an empty string to accumulate the output

    for dst in dst_list:
        # Get the indentation for the current node
        indent = parent_indent.get(dst.parent, "")
        content = "".join(dst.content)  # Merge content
        text += f"{indent}├─ {content}\n"  # Append the formatted content to the text

        # Set the indentation for this node's children
        parent_indent[dst.id] = indent + "│  "

    return text  # Return the accumulated text


# def get_dst_indent_tree(dst_list: list) -> str:
#     """
#     Prints the DST tree structure based on parent-child relationships.
#     Assumes the input list is already breadth-first sorted.
#
#     :param dst_list: List of DST objects
#     :return: A string containing the DST tree structure
#     """
#
#     # Track indentation for each parent
#     parent_indent = {"-1": ""}
#     text = ""  # Initialize an empty string to accumulate the output
#
#     for dst in dst_list:
#         # Get the indentation for the current node
#         indent = parent_indent.get(dst.parent, "")
#         content = "".join(dst.content)  # Merge content
#         text += f"{indent}├─ {content}\n"  # Append the formatted content to the text
#
#         # Set the indentation for this node's children
#         parent_indent[dst.id] = indent + "│  "
#
#     return text  # Return the accumulated text


def dst_to_json(dst_list: list):
    """
    Converts the DST list to JSON format and prints it.

    :param dst_list: List of DST objects
    """
    # Convert each DST object to a dictionary
    dst_dict_list = [dst.model_dump() for dst in dst_list]

    # Serialize to JSON with pretty printing
    json_output = json.dumps(dst_dict_list, indent=4, ensure_ascii=False)

    # Print the JSON output
    print(json_output)


def get_page_dst(dst_list: List[DST]) -> Dict[int, List[DST]]:
    page_map = {}
    for dst in dst_list:
        if dst.dst_type == DSTType.ROOT:
            continue
        page = dst.attributes.page
        if page not in page_map:
            page_map[page] = []
        page_map[page].append(dst)

        # Sort pages by page number
    return page_map

def assign_order_to_dst(dst_list):
    """
    Assign sequential order values to each DST object in the list.

    :param dst_list: List of DST objects.
    """
    for index, dst in enumerate(dst_list):
        dst.order = index + 1
    return dst_list