# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/4/22 10:46

import logging
import requests
from PIL import Image
from io import BytesIO
import base64
import re
import io
import uuid
from typing import Dict, List, Any, Union, Optional
import aiohttp
import asyncio
import fitz
from commons.db.storedao import StoreDao
from commons.prompt.ocrflux_prompt import OcrFlux, prompt_pic
from conf import MultipleParse
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from modules.entity.kdc_enttiy import Media, BlockType, ComponentType, HyperLink, Block
from modules.pipeline.context import PipelineContext
from commons.logger.business_log import logger

_store_dir = "insight-ai/doc-parser"
_public = True


async def table_entity2html(context:PipelineContext, table, id2url: dict, id2text: dict, depth=0) -> str:
    """
    将表格对象转换为HTML字符串，支持嵌套表格和多种内容类型

    :param table: 表格对象，需包含rows和cells结构
    :param id2url: 媒体ID到URL的映射字典
    :param depth: 当前嵌套深度（用于递归调用）
    :return: HTML表格字符串
    """
    if not table or not hasattr(table, 'rows') or not table.rows:
        return "<table></table>" if depth == 0 else ""

    html = ["<table>"]
    for row in table.rows:
        html.append("<tr>")
        for cell in row.cells if row.cells else []:
            # 处理单元格合并属性
            row_span = f' rowspan="{cell.row_span}"' if cell.row_span else ""
            col_span = f' colspan="{cell.col_span}"' if cell.col_span else ""

            # 递归处理单元格内容
            cell_content = await _process_cell_content(context, cell, id2url, id2text, depth)
            html.append(f'<td{row_span}{col_span}>{cell_content}</td>')
        html.append("</tr>")
    html.append("</table>")

    return "".join(html)


def _sort_textbox(blocks: List[Block]) -> List[List[Block]]:
    """
    对矩形区域进行排序，先按行分组，再在行内按x1坐标排序

    参数:
        rectangles: 待排序的矩形区域列表

    返回:
        排序后的矩形区域列表
    """
    if not blocks or len(blocks) == 0:
        return []
    if any(block.bounding_box is None for block in blocks):
        return []
    # 先按y1坐标排序
    # 先按y1坐标排序
    sorted_blocks = sorted(blocks, key=lambda b: b.bounding_box.y1)
    rows = []

    for block in sorted_blocks:
        added = False
        # 尝试将当前Block添加到已有的行中
        for row in rows:
            # 获取当前行中所有Block的最大y2坐标
            max_y2 = max(b.bounding_box.y2 for b in row)
            # 如果当前Block的y1大于当前行的最大y2，则不在同一行
            if block.bounding_box.y1 > max_y2:
                continue
            # 否则，认为在同一行（即使有部分交叉）
            row.append(block)
            added = True
            break

        # 如果无法添加到任何已有的行，则创建新行
        if not added:
            rows.append([block])

    # 对每行内的Block按x1坐标排序
    sorted_rows = [sorted(row, key=lambda b: b.bounding_box.x1) for row in rows]
    return sorted_rows
    # 展平为一维列表
    # return [block for row in sorted_rows for block in row]


async def _process_cell_content(context:PipelineContext, cell, id2url: dict, id2text: dict, depth: int) -> str:
    """处理单元格内的复杂内容"""
    if not hasattr(cell, 'blocks') or not cell.blocks:
        return ""

    content = []
    sorted_rows = _sort_textbox(cell.blocks)
    if len(sorted_rows) == 0:
        sorted_rows = [cell.blocks]

    for row in sorted_rows:
        row_content = []
        for block in row:
            if block.type == BlockType.textbox:
                row_content.append(_process_textbox(block.textbox, id2text))
            elif block.type == BlockType.para:
                row_content.append(_process_paragraph(block.para, id2text))
            elif block.type == BlockType.component:
                row_content.append(await _process_component(context, block.component, id2url))
            elif block.type == BlockType.table:
                row_content.append(await table_entity2html(context, block.table, id2url, id2text, depth + 1))
        content.append(" ".join(row_content))
    return " ".join(content)


def _process_textbox(textbox, id2text: dict) -> str:
    """处理文本框嵌套内容"""
    return "".join(
        _process_paragraph(block.para, id2text) if block.type == BlockType.para else ""
        for block in textbox.blocks
    )


def _process_paragraph(para, id2text: dict) -> str:
    """提取段落文本内容"""
    parts = []
    if para.prop and para.prop.list_string:
        parts.append(para.prop.list_string)

    if para.runs:
        parts.extend(
            run.text
            for run in para.runs
            if run and run.text and (run.id not in id2text or run.text in id2text[run.id])
        )

    return "".join(parts)


async def _process_component(context:PipelineContext, component, id2url: dict) -> str:
    """处理嵌入式组件"""
    if component.media_id not in id2url:
        return ""  # 如果 id2url 中没有对应的 media_id，直接跳过

    if component.type == ComponentType.image:
        # return f"<img src='{id2url.get(component.media_id, [])[0]}'/>"
        # ocr_data = await OCRModelClient().request_ocr(id2url.get(component.media_id, [])[0])
        ocr_data,_ = await OcrFlux(context.public_gateway_header).preprocess_ocrflux(id2url.get(component.media_id, [])[0],prompt_pic,MultipleParse.image_ocr_time_out)

        # ocr_data = await OCRModelClient().request_ocr(id2url.get(component.media_id, [])[0])
        # res_str = ocr_data
        return ocr_data
    return ""


def table2html(table):
    # 将元素样式为：[{'content': ['Min'], 'row_span': 1, 'col_span': 1}, {'content': ['Max'], 'row_span': 1, 'col_span': 1}]的列表转换为html表格
    if not table:
        return ""
    html = "<table>"
    for row in table:
        html += "<tr>"
        for cell in row:
            html += f"<td rowspan={cell['row_span']} colspan={cell['col_span']}>{''.join(cell['content'])}</td>"
        html += "</tr>"
    html += "</table>"
    return html


async def async_get_file(url: str) -> Image:
    """异步获取图片尺寸"""
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            response.raise_for_status()
            data = await response.read()
            image = Image.open(BytesIO(data))
            return image


async def _upload_image(idx: str, base64_str: str, public: bool = True) -> str:
    from PIL import Image
    from io import BytesIO
    try:
        image = base64.b64decode(base64_str + "==")
        pil = Image.open(BytesIO(image))
        buf = BytesIO()
        pil.save(buf, format="PNG")
        data = buf.getvalue()
        new_name = f"{uuid.uuid4().hex}_{idx}.png"
        url = ""
        if await StoreDao().async_upload_from_bytes(f"{_store_dir}/{new_name}", data):
            url = await StoreDao().async_generate_url(f"{_store_dir}/{new_name}", 1492073594)
            if public:
                url = re.sub(r"-internal", "", url, 1)
    except Exception as e:
        logger.error(f"Error uploading image {idx}: {e}")
        return ""
    return url


async def build_media_map(context: PipelineContext, medias: List[Media]) -> Dict[str, List[Any]]:
    id2url: Dict[str, List[Any]] = {}
    if not context.need_solve_picture:
        return id2url
    async def process_media(m: Media) -> tuple[str, List[Any]] | None:
        """处理单个媒体对象，返回 (id, [url, [width, height]]) 或 None"""
        try:
            if m.url:
                # 处理 URL 图片
                url = re.sub(r"-internal", "", m.url, 1)
                img = await async_get_file(url)
                width, height = img.size
                context.business_log.debug(f"Image size for media {m.id}: {width}x{height}")
                buffer = BytesIO()
                img.save(buffer, format="PNG")
                base64_str = base64.b64encode(buffer.getvalue()).decode()
                del img
                del buffer
                uploaded_url = await _upload_image(m.id, base64_str, public=_public)
                if uploaded_url:
                    return (m.id, [uploaded_url, [width, height]])
            elif m.data:
                image_data = base64.b64decode(m.data)
                image = Image.open(BytesIO(image_data))
                width, height = image.size
                del image
                uploaded_url = await _upload_image(m.id, m.data, public=_public)
                if uploaded_url:
                    return (m.id, [uploaded_url, [width, height]])
            return None
        except Exception as e:
            context.business_log.error(f"Error processing media {m.id}: {e}")
            return None

    # 并发处理所有媒体
    tasks = [process_media(m) for m in medias] if medias else []
    results = await asyncio.gather(*tasks)

    # 收集结果
    for result in results:
        if result:
            m_id, data = result
            id2url[m_id] = data

    return id2url


def build_hyperlink_map(hyperlinks: List[HyperLink]) -> Dict[str, str]:
    id2text: Dict[str, str] = {}
    for link in hyperlinks or []:
        if link.display_text and link.references:
            # 去除特殊符号和匹配 PAGEREF _Toc 格式的内容
            text = re.sub(r'[]', '', link.display_text)  # 去除特殊符号
            text = re.sub(r'PAGEREF _Toc\d+', '', text)  # 去除 PAGEREF _Toc 格式的内容
            for ref in link.references:
                id2text[ref.id] = text
    return id2text


def build_dst_id() -> str:
    return uuid.uuid4().hex


def build_root_dst():
    return DST(
        id=build_dst_id(),
        parent=str(-1),
        dst_type=DSTType.ROOT,
        attributes=DSTAttribute(
            level=0,
            position=PositionInfo(bbox=BBox(x1=1,
                          y1=2,
                          x2=3,
                          y2=4)),
            page=0,
            hash="roothashhashhashhashhashhashhashhashhashhash",

        ),
        content=['根节点'],
        order=0,
    )


def calculate_original_bbox(bbox: BBox, width, height):
    """
    Calculate the original bbox coordinates before rotation.
    :param bbox: BBox object with rotated coordinates.
    :param width: Width of the original coordinate system.
    :param height: Height of the original coordinate system.
    :param angle: Rotation angle (90, 180, or 270 degrees).
    :return: Tuple (x1', y1', x2', y2') of the original bbox.
    """
    logging.debug(f"Processing bbox: {bbox} x1: {bbox.x1}, y1: {bbox.y1}, x2: {bbox.x2}, y2: {bbox.y2}")
    x1, y1, x2, y2 = bbox.x1, bbox.y1, bbox.x2, bbox.y2
    angle = bbox.rotate
    if angle == 90:
        original_x1 = width-y2
        original_y1 = x1
        original_x2 = width-y1
        original_y2 = x2
    elif angle == 180:
        original_x1 = width - x2
        original_y1 = height - y2
        original_x2 = width - x1
        original_y2 = height - y1
    elif angle == 270:
        original_x1 = y1
        original_y1 = height - x2
        original_x2 = y2
        original_y2 = height - x1
    else:
        original_x1 = x1
        original_y1 = y1
        original_x2 = x2
        original_y2 = y2
    return original_x1, original_y1, original_x2, original_y2


def bytes_or_base64_image(image: Union[bytes, str]) -> Image.Image:
    image = image if isinstance(image, bytes) else base64.b64decode(image)
    image = Image.open(BytesIO(image))
    return image

def upload_image(base64_str, public=True):
    """
    upload image to ks3 store
    :param base64_str: str
    :param public: bool
    :return: url
    """
    pil_image = bytes_or_base64_image(base64_str)
    image_suffix = ".png"
    # 将pil_image转换为bytes
    img_byte_arr = io.BytesIO()
    pil_image.save(img_byte_arr, format='PNG')
    image_value = img_byte_arr.getvalue()
    new_file_name = f"{str(uuid.uuid4()).replace('-', '')}{image_suffix}"
    image_url = ""
    if StoreDao().upload_from_bytes(f"{_store_dir}/{new_file_name}", image_value):
        image_url = StoreDao().generate_url(f"{_store_dir}/{new_file_name}", 1492073594)
    if public:
        # 为了能够外网访问，如image_url中存在-internal，将其第一个匹配的去除
        image_url = re.sub(r'-internal', '', image_url, count=1)
    return image_url

async def _get_pdf_data(file_url_or_bytes: Union[str, bytes]) -> BytesIO:
    """
    异步获取PDF数据的辅助函数, 返回BytesIO对象包含PDF数据
    如果是走URL方式，默认30秒超时
    
    :param file_url_or_bytes: PDF文件的URL或二进制数据
    :return: BytesIO对象包含PDF数据
    :raises ValueError: 当输入格式无效时
    :raises aiohttp.ClientError: 当网络请求失败时
    """
    try:
        if isinstance(file_url_or_bytes, str):
            # 处理URL字符串
            timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(file_url_or_bytes) as response:
                    response.raise_for_status()
                    content = await response.read()
                    return BytesIO(content)
                    
        elif isinstance(file_url_or_bytes, bytes):
            # 处理二进制数据
            return BytesIO(file_url_or_bytes)
            
        else:
            raise ValueError(f"无效的文件格式，需要URL字符串或二进制数据，当前类型: {type(file_url_or_bytes)}")
            
    except aiohttp.ClientError as e:
        logger.error(f"网络请求失败: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"获取PDF数据失败: {str(e)}")
        raise

from dataclasses import dataclass
from typing import Optional

@dataclass
class PDFInfo:
    """PDF文件信息结构体"""
    page_count: int # 文件页数
    width: int # 文件宽度
    height: int # 文件高度
    rotate_page: Optional[Dict[int, List[int]]] = None # 旋转页面列表
    document: Optional[fitz.Document] = None
    
    def close_document(self):
        """关闭文档对象"""
        if self.document:
            self.document.close()
            self.document = None

# 使用 PDFInfo 结构体来统一返回PDF文件信息，包括页数和可选的文档对象，同时避免旧逻辑中多次打开文档
async def get_pdf_info(file_url_or_bytes: Union[str, bytes], keep_document: bool = False) -> PDFInfo:
    """
    获取PDF文件信息，包括页数、尺寸、旋转信息和可选的文档对象
    
    :param file_url_or_bytes: PDF文件的URL或二进制数据
    :param keep_document: 是否保留文档对象，True返回文档对象，False只返回基本信息
    :return: PDFInfo结构体，包含页数、尺寸、旋转信息和可选的文档对象
    :raises aiohttp.ClientError: 当网络请求失败时
    :raises Exception: 当PDF处理失败时
    """
    try:
        # 获取PDF文件数据
        pdf_data = await _get_pdf_data(file_url_or_bytes)
        
        # 打开PDF文档
        document = fitz.open(stream=pdf_data, filetype="pdf")
        page_count = len(document)
        
        # 计算最大宽高和旋转信息
        max_width = 0
        max_height = 0
        rotate_page = {}
        
        for page_index in range(page_count):
            try:
                page = document.load_page(page_index)
                
                # 获取页面尺寸
                width = page.rect.width
                height = page.rect.height
                
                # 处理页面旋转
                rotate = page.rotation
                if rotate in (90, 270):  # 90度和270度需要交换宽高
                    width, height = height, width
                
                # 记录有旋转的页面
                if rotate != 0:
                    if rotate not in rotate_page:
                        rotate_page[rotate] = []
                    rotate_page[rotate].append(page_index)
                
                # 更新最大宽高
                max_width = max(max_width, width)
                max_height = max(max_height, height)
                
            except Exception as e:
                logger.warning(f"处理页面 {page_index} 时出错: {str(e)}")
                continue
        
        # 创建PDFInfo对象
        pdf_info = PDFInfo(
            page_count=page_count,
            width=int(max_width),
            height=int(max_height),
            rotate_page=rotate_page if rotate_page else None,
            document=document if keep_document else None
        )
        
        # 如果不保留文档，关闭它
        if not keep_document:
            document.close()
        
        return pdf_info
            
    except Exception as e:
        logger.error(f"处理PDF文件失败: {str(e)}")
        raise


async def open_pdf(file_url_or_bytes: Union[str, bytes]) -> fitz.Document:
    """
    异步打开PDF文件并返回文档对象（向后兼容）
    
    :param file_url_or_bytes: PDF文件的URL或二进制数据
    :return: PDF文档对象
    """
    pdf_info = await get_pdf_info(file_url_or_bytes, keep_document=True)
    return pdf_info.document