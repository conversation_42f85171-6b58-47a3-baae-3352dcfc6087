import logging
from typing import Any
from concurrent.futures import Process<PERSON>oolExecutor
import asyncio
import psutil
import time


from commons.logger.logger import PassableLogContext

from commons.tools.utils import Singleton

def task_wrapper(fn, passable_log_context:PassableLogContext, *args, **kwargs):
    passable_log_context.export()
    return fn(*args, **kwargs)

class MultiProcess(object, metaclass=Singleton):
    """
    多进程工具，用来执行CPU密集型的任务
    """
    def __init__(self):
        self.process_pool: ProcessPoolExecutor = None

    def init(self, max_workers=None, mp_context=None, initializer=None, initargs=()):
        """
        初始化多进程
        :param max_workers: 最大进程数，None则为cpu核数
        :param mp_context: 进程上下文
        :param initializer: 进程初始化函数
        :param initargs: 进程初始化参数
        :return: None
        """
        self.process_pool = ProcessPoolExecutor(max_workers, mp_context, initializer, initargs)

    def close(self):
        logging.info("进程池close------------------")
        if self.process_pool is None:
            logging.info("Process pool is None, nothing to close")
            return
            
        try:
            # 收集所有子进程信息
            child_processes_data = []
            if hasattr(self.process_pool, '_processes'):
                for p in self.process_pool._processes:
                    try:
                        child_processes_data.append(psutil.Process(p))
                    except psutil.NoSuchProcess:
                        logging.warning(f"Process {p} no longer exists")
                        continue
            
            # 首先尝试优雅关闭，但是不等待太久
            logging.info("Attempting graceful shutdown of process pool...")
            self.process_pool.shutdown(wait=False)
            
            # 给进程一些时间来优雅退出（缩短到1秒）
            if child_processes_data:
                gone, alive = psutil.wait_procs(child_processes_data, timeout=1)
                if not alive:
                    logging.info("All processes shutdown gracefully")
                    return
                else:
                    logging.warning(f"Still {len(alive)} processes running after graceful shutdown attempt")
                    child_processes_data = alive
            else:
                logging.info("No child processes to clean up")
                return
            
        except Exception as e:
            logging.error(f"Error during graceful shutdown: {e}")
        
        # 强制终止所有子进程
        logging.info("Forcing termination of child processes...")
        for p_data in child_processes_data:
            try:
                if p_data.is_running():
                    logging.info(f"Terminating process {p_data.pid}")
                    p_data.terminate()
            except psutil.NoSuchProcess:
                continue
            except Exception as e:
                logging.error(f"Error terminating process {p_data.pid}: {e}")
        
        # 等待进程终止（缩短到0.5秒）
        if child_processes_data:
            try:
                gone, alive = psutil.wait_procs(child_processes_data, timeout=0.5)
                if alive:
                    logging.warning(f"Still {len(alive)} processes running after TERM signal")
                    child_processes_data = alive
                else:
                    logging.info("All processes terminated successfully")
                    return
            except Exception as e:
                logging.error(f"Error waiting for processes to terminate: {e}")
        
        # 强制杀死仍在运行的进程
        for p_data in child_processes_data:
            try:
                if p_data.is_running():
                    logging.warning(f"Force killing process {p_data.pid}")
                    p_data.kill()
            except psutil.NoSuchProcess:
                continue
            except Exception as e:
                logging.error(f"Error killing process {p_data.pid}: {e}")
        
        # 清理进程池引用
        self.process_pool = None
        logging.info("MultiProcess cleanup completed")

    def run(self, fn: Any, /, *args, **kwargs):
        log_context = PassableLogContext(**PassableLogContext.dump())
        future = self.process_pool.submit(task_wrapper,fn, log_context, *args, **kwargs)
        res = future.result()
        return res

    async def arun(self, fn: Any, /, *args):
        log_context = PassableLogContext(**PassableLogContext.dump())
        loop = asyncio.get_running_loop()
        res = await loop.run_in_executor(self.process_pool, task_wrapper, fn, log_context, *args)
        return res

    def submit(self, fn: Any, /, *args, **kwargs):
        log_context = PassableLogContext(**PassableLogContext.dump())
        self.process_pool.submit(task_wrapper, fn, log_context, *args, **kwargs)

    def map(self,  fn, *iterables, timeout=None, chunksize=1):
        results = self.process_pool.map(fn, *iterables, timeout=timeout, chunksize=chunksize)
        return results


# class MultiProcess(object, metaclass=Singleton):
#     """
#     多进程工具，用来执行CPU密集型的任务
#     """
#     def __init__(self):
#         self.process_pool: ProcessPoolExecutor = None
#         self.task_queue = None
#         self.worker_tasks = []
#
#     def init(self, max_workers=None, mp_context=None, initializer=None, initargs=(), queue_size: int = 20):
#         """
#         初始化多进程
#         :param max_workers: 最大进程数，None则为cpu核数
#         :param mp_context: 进程上下文
#         :param initializer: 进程初始化函数
#         :param initargs: 进程初始化参数
#         :param queue_size: queue任务队列大小
#         :return: None
#         """
#         self.process_pool = ProcessPoolExecutor(max_workers, mp_context, initializer, initargs)
#         self.task_queue = asyncio.Queue()
#         self.worker_tasks = [asyncio.create_task(self._worker()) for _ in range(queue_size)]
#
#     def close(self):
#         self.process_pool.shutdown()
#         # 取消所有worker任务
#         for task in self.worker_tasks:
#             task.cancel()
#
#     async def _worker(self):
#         while True:
#             try:
#                 # 从队列获取任务
#                 item = await self.task_queue.get()
#                 fn, args, future = item
#                 try:
#                     loop = asyncio.get_running_loop()
#                     res = await loop.run_in_executor(self.process_pool, fn, *args)
#                     future.set_result(res)
#                 except Exception as e:
#                     future.set_exception(e)
#                 finally:
#                     self.task_queue.task_done()
#             except asyncio.CancelledError:
#                 # 任务被取消，退出循环
#                 break
#
#     async def arun(self, fn: Any, /, *args):
#         if self.process_pool is None:
#             raise RuntimeError("MultiProcess not initialized. Call init() first.")
#         future = asyncio.Future()
#         await self.task_queue.put((fn, args, future))
#         return await future
#
#     def run(self, fn: Any, /, *args, **kwargs):
#         future = self.process_pool.submit(fn, *args, **kwargs)
#         res = future.result()
#         return res
#
#     def map(self,  fn, *iterables, timeout=None, chunksize=1):
#         results = self.process_pool.map(fn, *iterables, timeout=timeout, chunksize=chunksize)
#         return results