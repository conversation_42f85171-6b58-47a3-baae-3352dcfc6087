from typing import Callable
import uuid
import logging
from enum import Enum
from fastapi.routing import APIRoute
from fastapi import Request, HTTPException, Response, status
from starlette.requests import ClientDisconnect

from commons.auth.dmc_checkauth import DmcC<PERSON>ckAuth
from commons.auth.private_checkauth import Private<PERSON><PERSON>ckA<PERSON>
from commons.logger.logger import request_id_context, local_id_context, ip_context, uri_context

class AuthPlatform(str, Enum):
    dmc = "0"  # 公网dmc
    private = "1"  # 私有化基础服务

class AuthRoute(APIRoute):
    auth_platform = ""

    @classmethod
    def init(cls, platform: str, auth_host: str, auth_aksk_dict: dict):
        cls.auth_platform = platform
        if platform == AuthPlatform.dmc:
            aksk_tuple = list(auth_aksk_dict.items())[0]
            DmcCheckAuth().init(auth_host, aksk_tuple[0], aksk_tuple[1])
        elif platform == AuthPlatform.private:
            PrivateCheckAuth().init(auth_aksk_dict)

    def get_auth_platform(self):
        return self.auth_platform

    def get_route_handler(self) -> Callable:
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            request_id = request.headers.get("Client-Request-Id", "")
            if len(request_id) == 0:
                request_id = request.headers.get("X-Request-Id", str(uuid.uuid4()))
            request_id_context.set(request_id)
            local_id_context.set(str(uuid.uuid4()))
            ip_context.set(request.client.host)
            if "route" in request.scope:
                path = request.scope["route"].path
            else:
                path = request.url.path
            uri_context.set(path)
            logging.info(f"[request] url: {request.url.path}")
            isok = False
            auth_platform = self.get_auth_platform()

            try:
                if auth_platform == AuthPlatform.dmc:
                    isok = await DmcCheckAuth().authorization(request)
                elif auth_platform == AuthPlatform.private:
                    isok = await PrivateCheckAuth().authorization(request)
            except ClientDisconnect:
                logging.warning("Client disconnected during authorization in auth route")
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Client disconnected")
            
            if isok:
                resp = await original_route_handler(request)
                return resp
            else:
                logging.error("auth check fail!")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="authorization error")

        return custom_route_handler
