import json
from enum import Enum
import hashlib
import aiohttp
import hmac
import time
import requests
from requests_toolbelt import MultipartEncoder
from requests.adapters import HTT<PERSON><PERSON>pter
from typing import Op<PERSON>, <PERSON><PERSON>, AsyncGenerator

from commons.tools.utils import norm_http_params


class SigVerType(str, Enum):
    wps2 = "wps2"
    wps4 = "wps4"


def sig_wps2(uri: str, body: bytes, ak, sk, date, content_type=""):
    content = body if body else uri.encode("utf-8")
    if len(content_type) == 0:
        content_type = "application/json"
    # logging.debug(
    #     f"sig_wps2 data, content: {content}, sk: {sk[:3]}****{sk[-3:]}, content_type: {content_type}, date: {date}")
    content_md5 = hashlib.md5(content).hexdigest()
    sha1 = hashlib.sha1(sk.encode("utf-8"))
    sha1.update(content_md5.encode("utf-8"))
    sha1.update(content_type.encode("utf-8"))
    sha1.update(date.encode("utf-8"))

    auth = "WPS-2:%s:%s" % (ak, sha1.hexdigest())
    # logging.debug(f"sig_wps2 auth: {auth}")
    header = {
        "Content-Type": content_type,
        "Authorization": auth,
        "Date": date,
        "Content-Md5": content_md5,
    }
    return header


def sig_wps4(method, uri, body: bytes, ak, sk, date, content_type=""):
    if body:
        hash_sha256 = hashlib.sha256(body).hexdigest()
    else:
        hash_sha256 = ""

    if len(content_type) == 0:
        content_type = "application/json"
    if method == "GET":
        content_type = ""
    # logging.debug(
    #     f"sig_wps4 data, body: {body}, ak: {ak}, sk: {sk[:3]}****{sk[-3:]}, content_type: {content_type}, date: {date}")
    # date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
    message = f"WPS-4{method}{uri}{content_type}{date}{hash_sha256}".encode("utf-8")
    # logging.debug(f"sig_wps4 message: {message}")
    signature = hmac.new(sk.encode("utf-8"), message, hashlib.sha256).hexdigest()
    auth = f"WPS-4 {ak}:{signature}"
    # logging.debug(f"sig_wps4 auth: {auth}")
    header = {"Content-Type": content_type, "Wps-Docs-Date": date, "Wps-Docs-Authorization": auth}
    return header


class HttpRequest(object):
    def __init__(self, host: str, pool_max: Optional[int] = None):
        self._host = host
        self._pool_max = pool_max


    async def async_call(
            self,
            method,
            uri,
            body: dict = None,
            params: dict = None,
            cookies: dict = None,
            header: dict = None) -> Tuple[int, str]:
        url = f"{self._host}{uri}"

        if params:
            params = norm_http_params(params, True)
        if body:
            body = norm_http_params(body, False)

        new_header = {}
        if header:
            new_header.update(header)
        if "Content-Type" not in new_header:
            new_header["Content-Type"] = "application/json"
        conn = None
        if self._pool_max and self._pool_max > 0:
            conn = aiohttp.TCPConnector(limit=self._pool_max)
        async with aiohttp.ClientSession(connector=conn) as sess:
            async with sess.request(method, url, json=body, params=params, headers=new_header, cookies=cookies) as resp:
                text = await resp.text(encoding="utf-8")
                return resp.status, text

    async def async_call_sse(self,
                             method,
                             uri,
                             body: dict = None,
                             params: dict = None,
                             cookies: dict = None,
                             header: dict = None) -> AsyncGenerator[str, None]:
        url = f"{self._host}{uri}"

        if params:
            params = norm_http_params(params, True)
        if body:
            body = norm_http_params(body, False)

        new_header = {}
        if header:
            new_header.update(header)
        if "Accept" not in new_header:
            new_header["Accept"] = "text/event-stream"
        if body and "Content-Type" not in new_header:
            new_header["Content-Type"] = "application/json"
        conn = None
        if self._pool_max and self._pool_max > 0:
            conn = aiohttp.TCPConnector(limit=self._pool_max)
        async with aiohttp.ClientSession(connector=conn) as sess:
            async with sess.request(method, url, json=body, params=params, headers=new_header, cookies=cookies) as resp:
                # while data := await resp.content.readline():
                #     data = data.decode(encoding="utf-8").strip()
                #     if data:
                #         yield data
                buffer = b""
                async for data, end_of_http_chunk in resp.content.iter_chunks():
                    buffer += data
                    if end_of_http_chunk:
                        datas = buffer.decode(encoding="utf-8").split("\n")
                        for data in datas:
                            if not data.strip():
                                continue
                            yield data
                        buffer = b""


class AuthRequest(object):
    def __init__(self, host, ak, sk, sig_version: SigVerType, pool_max: Optional[int] = None):
        self._host = host
        self._ak = ak
        self._sk = sk
        self._sig_ver = sig_version
        self._pool_max = pool_max
        self._sess = None
        if pool_max and pool_max > 0:
            self._sess = requests.Session()
            self._sess.mount(self._host, HTTPAdapter(pool_connections=1, pool_maxsize=pool_max))

    async def async_call(
            self, method, uri, body: dict = None, cookies: dict = None, header: dict = None, timeout: float = 180.0) -> Tuple[int, str]:
        url, body, auth_header = self._prepare_datas(method, uri, body, header)

        conn = None
        if self._pool_max and self._pool_max > 0:
            conn = aiohttp.TCPConnector(limit=self._pool_max)
        async with aiohttp.ClientSession(connector=conn) as sess:
            async with sess.request(method, url, data=body, headers=auth_header, cookies=cookies, timeout=timeout, proxy="http://127.0.0.1:18899") as resp:
                text = await resp.text(encoding="utf-8")
                return resp.status, text

    def _prepare_datas(self, method, uri, body: Optional[dict], header: Optional[dict]):
        body = json.dumps(body).encode("utf-8") if body else None
        date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
        if self._sig_ver == SigVerType.wps2:
            auth_header = sig_wps2(uri, body, self._ak, self._sk, date)
        elif self._sig_ver == SigVerType.wps4:
            auth_header = sig_wps4(method, uri, body, self._ak, self._sk, date)
        else:
            raise Exception(f"sig ver error, sig_version={self._sig_ver}")

        if header:
            auth_header.update(header)

        url = f"{self._host}{uri}"
        return url, body, auth_header

    def call(self, method, uri, body: dict = None, cookies: dict = None, header: dict = None) -> (int, str):
        url, body, auth_header = self._prepare_datas(method, uri, body, header)
        if self._sess:
            r = self._sess.request(method, url, data=body, headers=auth_header, cookies=cookies)
        else:
            r = requests.request(method, url, data=body, headers=auth_header, cookies=cookies)
        return r.status_code, r.text

    def send_formdata(self, method: str, uri: str, data: MultipartEncoder, cookies: dict = None,
                      header: dict = None) -> (int, str):
        body = data.to_string()
        date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
        if self._sig_ver == SigVerType.wps2:
            auth_header = sig_wps2(uri, body, self._ak, self._sk, date, data.content_type)
        elif self._sig_ver == SigVerType.wps4:
            auth_header = sig_wps4(method, uri, body, self._ak, self._sk, date, data.content_type)
        else:
            raise Exception(f"sig ver error, sig_version={self._sig_ver}")
        if header:
            auth_header.update(header)
        url = f"{self._host}{uri}"
        r = requests.request("POST", url, data=body, headers=auth_header, cookies=cookies)
        return r.status_code, r.text
