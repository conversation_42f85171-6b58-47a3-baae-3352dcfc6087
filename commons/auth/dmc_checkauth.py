import json
from functools import lru_cache
from urllib.parse import quote_plus
import logging
from typing import Optional, List
from fastapi import Request
from starlette.requests import ClientDisconnect

from commons.auth.auth_rpc import AuthRequest, SigVerType, sig_wps2
from commons.tools.utils import Singleton

class DmcCheckAuth(object, metaclass=Singleton):
    def __init__(self):
        self._auth_rpc: Optional[AuthRequest] = None

    def init(self, host, ak, sk):
        logging.info(f"Initializing DMC auth with host: {host}, AK: {ak[:8]}***")
        self._auth_rpc = AuthRequest(host, ak, sk, SigVerType.wps2)
        try:
            code, text = self._auth_rpc.call("POST", f"/acl/v2/init?v=v2")
            logging.debug(f"DMC init response: code={code}, text={text}")
            if code != 200 or json.loads(text)["result"] != "ok":
                logging.error(f"DMC auth init failed: code={code}, response={text}")
                raise Exception("init auth failed!")
            logging.info("DMC auth initialization successful")
        except Exception as e:
            logging.error(f"DMC auth init exception: {e}")
            raise

    async def authorization(self, request: Request) -> bool:
        uri = request.url.path
        if len(request.url.query) > 0:
            uri += f"?{request.url.query}"
        header = request.headers
        content_type = header.get("Content-Type", "")
        if "multipart/form-data" in content_type:
            # 去除boundary
            content_type = "multipart/form-data"
        date = header.get("Date", "")
        if len(date) == 0:
            logging.warning(f"Authorization failed: Missing Date header for request {uri}")
            return False

        authstr = header.get("Authorization", "")
        if len(authstr) == 0:
            logging.warning(f"Authorization failed: Missing Authorization header for request {uri}")
            return False

        items = authstr.strip().split(":")
        if len(items) != 3:
            logging.warning(f"Authorization failed: Invalid Authorization format for request {uri}, got {len(items)} parts")
            return False

        check_ak = items[1]
        
        # 处理客户端断开连接的情况
        try:
            body = await request.body() if "POST" == request.method and "application/json" == content_type else None
        except ClientDisconnect:
            logging.warning("Client disconnected during authorization")
            return False
        
        isok, retry = self._check(authstr, uri, body, check_ak, date, content_type)
        if not isok and retry:
            logging.debug(f"Authorization check failed, clearing cache and retrying for AK: {check_ak}")
            self._get_check_sk_list.cache_clear()
            isok, _ = self._check(authstr, uri, body, check_ak, date, content_type)
        
        if not isok:
            logging.warning(f"Authorization failed: Signature verification failed for AK: {check_ak}, URI: {uri}")
        
        return isok

    def _check(self, authstr, uri, body, ak, date, content_type) -> (bool, bool):
        sk_list = self._get_check_sk_list(ak)
        if not sk_list:
            logging.warning(f"Authorization failed: Unable to get SK list for AK: {ak}")
            self._get_check_sk_list.cache_clear()
            return False, False

        logging.debug(f"Checking authorization for AK: {ak}, URI: {uri}, got {len(sk_list)} SKs")
        for sk in sk_list:
            header = sig_wps2(uri, body, ak, sk, date, content_type)
            if authstr == header["Authorization"]:
                logging.debug(f"Authorization successful for AK: {ak}")
                return True, False
        
        logging.debug(f"Authorization failed: None of {len(sk_list)} SKs matched for AK: {ak}")
        return False, True

    @lru_cache(maxsize=10)
    def _get_check_sk_list(self, ak) -> Optional[List[str]]:
        try:
            logging.debug(f"Fetching SK list for AK: {ak}")
            _, text = self._auth_rpc.call("GET", f"/acl/v2/app?app_id={quote_plus(ak)}")
            appkeys = json.loads(text)["object"]["app_keys"]
            sk_list = [item["app_key"] for item in appkeys]
            logging.debug(f"Successfully fetched {len(sk_list)} SKs for AK: {ak}")
            return sk_list
        except Exception as e:
            logging.error(f"Failed to fetch SK list for AK: {ak}, error: {e}")
            return None
