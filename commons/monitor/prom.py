# coding:utf-8

import platform

if platform.system().lower() == "linux":
    import socket
    import fcntl
    import struct
from prometheus_client import CollectorRegistry, Counter, pushadd_to_gateway, Histogram
from prometheus_client.exposition import basic_auth_handler, default_handler
import threading
import time
import logging
from urllib.error import HTT<PERSON>Error
from typing import List, Optional
from commons.logger.logger import uri_context


def getlocalip(ifname):
    if platform.system().lower() == "linux":
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        try:
            inet = fcntl.ioctl(s.fileno(), 0x8915, struct.pack("256s", bytes(ifname[:15], "utf-8")))
            return socket.inet_ntoa(inet[20:24])
        except Exception as e:
            logging.error(e)
            return "127.0.0.1"
    else:
        return "127.0.0.1"


class MCounter(threading.Thread):
    push_interval = 10
    push_timeout = 10

    def __init__(self, gateway: str, job_name_prefix: str, username: str = None, password: str = None,
                 white_endpoint: List[str] = None):
        threading.Thread.__init__(self)

        self.gateway = gateway
        self.job_name_prefix = job_name_prefix
        self.username = username
        self.password = password
        self.white_endpoint = white_endpoint
        if self.white_endpoint is None:
            self.white_endpoint = []
        self.registry = CollectorRegistry()
        self.app = self.job_name_prefix
        self.ip = getlocalip("enp5s0")
        self.job_name = f"{self.job_name_prefix}:{self.ip}"
        labels = ["machine", "app", "endpoint", "kafka_message_type"]
        token_labels = ["machine", "app", "uri", "endpoint", "kafka_message_type", "model", "provider", "version"]
        labels_parse = ["machine", "app", "file_type", "step"]
        step_labels_parse = ["machine", "app", "step", "func_str"]
        app_name = f"{self.job_name_prefix}".replace("-", "_")
        self.http_request_count = Counter(
            f"{app_name}_http_request_count",
            f"{app_name} http request count",
            labels,
            registry=self.registry,
        )
        self.http_request_duration = Histogram(
            f"{app_name}_http_request_duration",
            f"{app_name} http request duration",
            labels,
            registry=self.registry,
        )
        self.token_count = Counter(
            f"{app_name}_token_count",
            f"{app_name} token count",
            token_labels,
            registry=self.registry,
        )
        self.parse_duration = Histogram(
            f"{app_name}_parse_duration_seconds",
            f"{app_name} file-parse duration (s)",
            labels_parse,
            registry=self.registry,
        )
        self.parse_count = Counter(
            f"{app_name}_parse_count_seconds",
            f"{app_name} file-parse count (s)",
            labels_parse,
            registry=self.registry,
        )
        self.parse_step_duration = Histogram(
            f"{app_name}_parse_step_duration_seconds",
            f"{app_name} file-parse step duration (s)",
            step_labels_parse,
            registry=self.registry,
        )
        self.parse_step_count = Counter(
            f"{app_name}_parse_step_count_seconds",
            f"{app_name} file-parse step count ",
            step_labels_parse,
            registry=self.registry,
        )


    def _auth_handler(self, url, method, timeout, headers, data):
        if self.username and self.password:
            return basic_auth_handler(url, method, timeout, headers, data, self.username, self.password)
        else:
            return default_handler(url, method, timeout, headers, data)

    def record(self, endpoint, kafka_message_type: str = "no", total_time: float = 0.0):
        try:
            # 不在白名单的endpoint不上报监控，防止恶意刷接口导致堆积
            if self.white_endpoint and endpoint not in self.white_endpoint:
                return
            self.http_request_count.labels(self.ip, self.app, endpoint, kafka_message_type).inc()
            if total_time > 0:
                self.http_request_duration.labels(self.ip, self.app, endpoint, kafka_message_type).observe(total_time)
        except Exception as e:
            logging.error(e)

    def record_token(self, endpoint: str, completion_tokens: int, prompt_tokens: int, total_tokens: int, model: str, provider: Optional[str] = None, version: Optional[str] = None):
        uri = uri_context.get()
        try:
            # 不在白名单的endpoint不上报监控，防止恶意刷接口导致堆积
            if self.white_endpoint and endpoint not in self.white_endpoint:
                return
            self.token_count.labels(self.ip, self.app, uri, f"{endpoint}_completion_tokens", "no", model, provider, version).inc(completion_tokens)
            self.token_count.labels(self.ip, self.app, uri, f"{endpoint}_prompt_tokens", "no", model, provider, version).inc(prompt_tokens)
            self.token_count.labels(self.ip, self.app, uri, f"{endpoint}_total_tokens", "no", model, provider, version).inc(total_tokens)
        except Exception as e:
            logging.error(e)

    def record_parse(self, file_type: str, step: str, duration: float):
        try:
            self.parse_count.labels(self.ip, self.app, file_type, step).inc()
            self.parse_duration.labels(self.ip, self.app, file_type, step).observe(duration)
        except Exception as e:
            logging.error(e)

    def step_record_parse(self, step: str, func_str: str, duration: float):
        try:
            self.parse_step_count.labels(self.ip, self.app, step, func_str).inc()
            self.parse_step_duration.labels(self.ip, self.app,  step, func_str).observe(duration)
        except Exception as e:
            logging.error(e)
    def run(self):
        i = 0
        while True:
            try:
                if i < 10:
                    pushadd_to_gateway(
                        self.gateway,
                        job=self.job_name,
                        registry=self.registry,
                        timeout=MCounter.push_timeout,
                        handler=self._auth_handler
                    )
                    i = 0
            except HTTPError as e:
                if e.code == 401:
                    logging.error(f"prometheus pushgateway auth fail, code: {e.code}, msg: {e.msg}")
                    break
                else:
                    i += 1
                    logging.error(e)
                    time.sleep(60)
            except Exception as e:
                i += 1
                logging.error(e)
                time.sleep(60)
            time.sleep(MCounter.push_interval)


mcount: MCounter = None


def init_prometheus(gateway: str, job_name_prefix: str, username: str = None, password: str = None,
                    white_endpoint: List[str] = None):
    global mcount
    if not gateway:
        return
    mcount = MCounter(gateway, job_name_prefix, username, password, white_endpoint)
    mcount.start()


def get_mcount() -> MCounter:
    return mcount
