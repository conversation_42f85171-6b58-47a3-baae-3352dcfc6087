import os
import json
from enum import Enum
import logging

logger = logging.getLogger(__name__)

DEFAULT_SFT_CONFIG = {
    "default_chat_model":{
        "host": "sre",
        "model": "qwen1.5_14b",
        "chat_uri": "/v1/chat/completions",
        "completion_uri": "/v1/completions"
    },
    "default_multimodal":{
        "host": "kas",
        "model": "qwen2_vl_7b",
        "chat_uri": "/api/11300/kpfREg/v1/chat/completions",
        "completion_uri": "/api/11300/kpfREg/v1/completions"
    },
    "qwen1_5_14b":{
        "host": "sre",
        "model": "qwen1.5_14b",
        "chat_uri": "/v1/chat/completions",
        "completion_uri": "/v1/completions"
    },
    "qwen2_5_14b":{
        "host": "sre",
        "model": "qwen2.5_14b",
        "chat_uri": "/25_14b/v1/chat/completions",
        "completion_uri": "/25_14b/v1/completions"
    },
    "qwen2_vl_7b":{
        "host": "kas",
        "model": "qwen2_vl_7b",
        "chat_uri": "/api/11300/kpfREg/v1/chat/completions",
        "completion_uri": "/api/11300/kpfREg/v1/completions"
    },
    "qwen2_vl_72b":{
        "host": "sre",
        "model": "qwen2_vl_72b",
        "chat_uri": "/2_vl_72b/v1/chat/completions",
        "completion_uri": "/2_vl_72b/v1/completions"
    },
    "qwq_32b":{
        "host": "sre",
        "model": "qwq_32b",
        "chat_uri": "/qwq_32b/v1/chat/completions",
        "completion_uri": "/qwq_32b/v1/completions"
    },
    "qsearch_qwen2_5_14b":{
        "host": "sre",
        "model": "qsearch_qwen2.5_14b",
        "chat_uri": "/qsearch_25_14b/v1/chat/completions",
        "completion_uri": "/qsearch_25_14b/v1/completions"
    },
    "kas_qsearch_qwen2_5_14b":{
        "host": "kas",
        "model": "kas_qsearch_qwen2.5_14b",
        "chat_uri": "/api/11238/yAkqpd/v1/chat/completions",
        "completion_uri": "/api/11238/yAkqpd/v1/completions"
    },
    "kas_qsearch_qwen3_14b":{
        "host": "kas",
        "model": "kas_qsearch_qwen3_14b",
        "chat_uri": "/api/11334/kQUONi/v1/chat/completions",
        "completion_uri": "/api/11334/kQUONi/v1/completions"
    },
    "qwen25_vl_32b_awq":{
        "host": "kas",
        "model": "qwen25_vl_32b_awq",
        "chat_uri": "/api/11329/HgDolg/v1/chat/completions",
        "completion_uri": "/api/11329/HgDolg/v1/completions"
    },
    "qwen25_vl_7b_aidocs":{
        "host": "kas",
        "model": "qwen25_vl_7b_aidocs",
        "chat_uri": "/api/11459/FZIYP2/v1/chat/completions",
        "completion_uri": "/api/11459/FZIYP2/v1/completions"
    },
    "qwen25_14b_aidocs":{
        "host": "kas",
        "model": "qwen25_14b_aidocs",
        "chat_uri": "/api/11425/2IxBy2/v1/chat/completions",
        "completion_uri": "/api/11425/2IxBy2/v1/completions"
    },
   "ocrflux_qwen25_vl_3b":{
        "host": "kas",
        "model": "ocrflux_qwen25_vl_3b",
        "chat_uri": "/api/11551/2x1mnc/v1/chat/completions",
        "completion_uri": ""
    },
    "qwen25_vl_3b_aidocs": {
        "host": "kas",
        "model": "qwen25_vl_3b_aidocs",
        "chat_uri": "/api/11575/oVp7WC/v1/chat/completions",
        "completion_uri": "/api/11575/oVp7WC/v1/completions"
    },

}



def load_sft_model_config(class_name:str = "SftBaseModelType") -> tuple:
    json_config = os.getenv("SFT_MODEL_CONFIG", "{}")
    configs = DEFAULT_SFT_CONFIG
    try:
        env_configs = json.loads(json_config)
        for key, values in env_configs.items():
            if key in configs:
                configs[key].update(values)
            else:
                configs[key] = values
    except Exception as e:
        logger.error(f"Failed to load SFT model config from env variable: {e}")
    
    
    SftBaseModelType = Enum(class_name, 
                            {key: value["model"] for key, value in configs.items()},
                            type=str)
    chat_api = {
        sft_type: configs[sft_type.name]["chat_uri"] for sft_type in SftBaseModelType
    }
    completion_api = {
        sft_type: configs[sft_type.name]["completion_uri"] for sft_type in SftBaseModelType
    }
    host_map = {
        sft_type: configs[sft_type.name]["host"] for sft_type in SftBaseModelType
    }

    return SftBaseModelType, chat_api, completion_api, host_map

