# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/8/18 20:31

from commons.auth.auth_rpc import AuthRequest
from typing import Optional, List
from commons.rpc.rpc_entity import RpcParams, RpcResp
from commons.logger.business_log import logger


class RpcClient(object):
    """
    RPC Client，定义RPC客户端的基本结构和错误处理方法
    """

    def __init__(self, rpc_params: RpcParams):
        # 初始化RPC客户端，支持多个客户端，wps2、wps4等签名方式
        self._reqs: List[AuthRequest] = []
        for conn_info in rpc_params.conn_infos:
            self._reqs.append(AuthRequest(conn_info.host, conn_info.ak, conn_info.sk, conn_info.sig_type))

    @classmethod
    def resp_error(cls, func_name: str, uri: str, status: int, res_text: str) -> RpcResp:
        """
        处理RPC响应错误
        :param func_name: 方法名
        :param uri: uri
        :param status: HTTP状态码
        :param res_text: 响应文本
        :return: RpcResp
        """
        logger.error(f"RpcClient rpc fail, response error, class: {cls.__name__}, func: {func_name}, uri: {uri}, status: {status}, text: {res_text}")
        return RpcResp.error_response(f"API response error: {res_text}", status)

    @classmethod
    def http_error(cls, func_name: str, uri: str, status: int, res_text: str) -> RpcResp:
        """
        处理HTTP错误
        :param func_name: 方法名
        :param uri: uri
        :param status: HTTP状态码
        :param res_text: 响应文本
        :return: RpcResp
        """
        logger.error(f"RpcClient rpc fail, http error, class: {cls.__name__}, func: {func_name}, uri: {uri}, status: {status}, text: {res_text}")
        return RpcResp.error_response(f"HTTP error: status {status}", status)

    @classmethod
    def exception_error(cls, func_name: str, uri: str, error: str) -> RpcResp:
        """
        处理RPC异常
        :param func_name: 方法名
        :param uri: uri
        :param error: 异常信息
        :return: RpcResp
        """
        logger.error(f"RpcClient rpc fail, except exception, class: {cls.__name__}, func: {func_name}, uri: {uri}, error: {error}")
        return RpcResp.error_response(f"Request failed: {str(error)}")
