# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/01/29 10:00

from typing import Optional, Union, TypeVar, Generic, List
from pydantic import BaseModel, field_validator
from enum import Enum

from commons.auth.auth_rpc import SigVerType


class RpcName(str, Enum):
    """RPC类别"""
    yun = "yun"  # 云文档接口
    normal_model = "normal_model"  # 普通模型接口
    monkey_ocr = "monkey_ocr"  # monkey ocr接口


class ConnInfo(BaseModel):
    host: str  # RPC服务地址
    ak_env: Optional[str] = None  # Access Key环境变量名
    sk_env: Optional[str] = None  # Secret Key环境变量名
    ak: str = ""  # 鉴权ak，默认空字符串
    sk: str = ""  # 鉴权sk
    sig_type: Union[str, SigVerType]  # 签名类型，支持字符串和枚举

    @field_validator('sig_type')
    @classmethod
    def validate_sig_type(cls, v: Union[str, SigVerType]) -> SigVerType:
        """验证并转换签名类型"""
        if isinstance(v, SigVerType):
            return v
        if isinstance(v, str):
            if v.lower() == "wps2":
                return SigVerType.wps2
            elif v.lower() == "wps4":
                return SigVerType.wps4
            else:
                raise ValueError(f"Invalid sig_type: {v}. Must be 'wps2' or 'wps4'")
        raise ValueError(f"sig_type must be string or SigVerType, got {type(v)}")


class RpcParams(BaseModel):
    """
    RPC接口参数结构体
    """
    class_path: str  # 类路径，用于标识具体的Drive RPC实现
    enabled: bool = False  # 是否启用该实现
    conn_infos: List[ConnInfo]  # 连接信息，一个rpc可以有多个连接信息


# 采用泛型来定义RPC响应体
TRespData = TypeVar("TRespData")


class RpcResp(BaseModel, Generic[TRespData]):
    class ResultCode(str, Enum):
        """RPC响应结果枚举"""
        OK = "ok"
        ERROR = "error"

    """
    rpc 返回体
    用于统一RPC接口的返回结果
    """
    result: ResultCode  # "ok" | "error" 等
    data: Optional[TRespData] = None  # 响应数据，类型由具体实现决定
    error_message: Optional[str] = None  # 当result为error时的错误描述
    status_code: Optional[int] = None  # HTTP状态码

    @classmethod
    def success_response(cls, data: TRespData) -> 'RpcResp':
        """创建成功响应"""
        return cls(
            result=cls.ResultCode.OK,
            data=data,
        )

    @classmethod
    def error_response(cls, error_message: str, status_code: Optional[int] = None,
                       result: ResultCode = ResultCode.ERROR) -> 'RpcResp':
        """创建错误响应"""
        return cls(
            result=result,
            data=None,
            error_message=error_message,
            status_code=status_code
        )

    def is_success(self) -> bool:
        """判断是否成功"""
        return self.result == self.ResultCode.OK

    def get_data(self) -> Optional[TRespData]:
        """获取响应数据"""
        return self.data if self.data else None
