# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/07/29 09:20
from commons.logger.business_log import logger
from typing import Optional, Dict, Type
from commons.tools.utils import Singleton
from commons.rpc.rpc_entity import RpcParams, RpcName
from commons.rpc.rpc_client import RpcClient
from commons.rpc.yun.rpc_interface import YunRpcInterface
from commons.rpc.normal_model.rpc_interface import NormalRpcInterface
from commons.rpc.monkey_ocr.rpc_interface import MonkeyOcrRpcInterface


class RpcFactory(metaclass=Singleton):
    """
    RPC工厂类
    
    使用工厂模式管理不同类型的Drive RPC实例
    支持策略切换和扩展新类型
    """

    def __init__(self):
        self._rpc_config: Optional[Dict[str, RpcParams]] = None
        self._rpc_instances: Dict[str, RpcClient] = {}

    def init(self, rpc_config: Dict[str, RpcParams]):
        self._rpc_config = rpc_config
        self._register_from_config()

    def _register_from_config(self) -> bool:
        """从环境变量配置注册RPC实现
        
        Returns:
            bool: 是否注册成功
        """
        try:
            registered_count = 0
            for rpc_type_str, impl_config in self._rpc_config.items():
                if not impl_config.enabled:
                    logger.info(f"RPC implementation {rpc_type_str} is disabled in config")
                    continue

                try:
                    # 动态导入实现类
                    class_path = impl_config.class_path
                    module_name, class_name = class_path.rsplit('.', 1)
                    module = __import__(module_name, fromlist=[class_name])
                    rpc_class = getattr(module, class_name)

                    # 注册实现类 - 直接使用字符串类型
                    self._register_rpc(rpc_type_str, rpc_class, impl_config)
                    registered_count += 1

                    logger.info(f"Registered RPC from environment config: {rpc_type_str} -> {class_path}")

                except Exception as e:
                    logger.error(f"Failed to register RPC implementation {rpc_type_str}: {e}")

            return registered_count > 0

        except Exception as e:
            logger.error(f"Failed to load RPC config from environment: {e}")
            return False

    def _register_rpc(self, rpc_type: str, rpc_class: Type[RpcClient], impl_config: RpcParams):
        """
        注册新的RPC实现类
        
        Args:
            rpc_type: RPC类型字符串
            rpc_class: RPC实现类
            :param impl_config: RPC初始化参数
        """
        if not issubclass(rpc_class, RpcClient):
            raise ValueError(f"RPC class must implement RpcClient")

        self._rpc_instances[rpc_type] = rpc_class(impl_config)
        logger.info(f"Registered RPC {rpc_type}: {rpc_class.__name__}")

    def _get_rpc_instance(self, rpc_type: Optional[str] = None) -> RpcClient:
        """
        获取RPC实例（单例模式）
        
        Args:
            rpc_type: 指定类型字符串，不指定则使用默认类型
            
        Returns:
            DriveRpcInterface: RPC实例
            
        Raises:
            ValueError: 当指定类型未注册时
        """
        # 检查是否已注册
        if rpc_type not in self._rpc_instances:
            raise ValueError(f"RPC type {rpc_type} is not registered")
        return self._rpc_instances[rpc_type]

    def get_yun_rpc(self) -> YunRpcInterface:
        """
        获取云文档RPC实例
        """
        yun_client = self._get_rpc_instance(RpcName.yun)
        if isinstance(yun_client, YunRpcInterface):
            return yun_client
        else:
            raise TypeError(f"Expected YunRpcInterface, got {type(yun_client)} instead")

    def get_normal_model_rpc(self):
        """
        获取普通模型RPC实例
        """
        normal_model_client = self._get_rpc_instance(RpcName.normal_model)
        if isinstance(normal_model_client, NormalRpcInterface):
            return normal_model_client
        else:
            raise TypeError(f"Expected NormalRpcInterface, got {type(normal_model_client)} instead")

    def get_monkey_ocr_rpc(self):
        """
        获取monkey ocr RPC实例
        """
        client = self._get_rpc_instance(RpcName.monkey_ocr)
        if isinstance(client, MonkeyOcrRpcInterface):
            return client
        else:
            raise TypeError(f"Expected NormalRpcInterface, got {type(client)} instead")

if __name__ == '__main__':
    from conf import ConfRpcConfig

    RpcFactory().init(ConfRpcConfig.get_validated_config())
    c = RpcFactory().get_yun_rpc()
    print(c)
