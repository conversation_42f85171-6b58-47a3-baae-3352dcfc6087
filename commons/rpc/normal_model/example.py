# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/19 11:20
import unittest
from commons.rpc.rpc_factory import RpcFactory
from commons.logger.logger import init_logger
from conf import ConfRpcConfig


class ExampleNormalModelRpc(unittest.IsolatedAsyncioTestCase):
    async def asyncSetUp(self):
        from commons.logger.business_log import logger
        RpcFactory().init(ConfRpcConfig.get_validated_config())

    async def test_arequest_text_embedding(self):
        print()
        text = "测试文本"
        res = await RpcFactory().get_normal_model_rpc().arequest_text_embedding(text)
        print(res.is_success())
        if res.is_success():
            print(res.data)
        else:
            print(res.error_message)

    async def test_arequest_rerank(self):
        print()
        query = "文档1"
        texts = ["文档1", "文档2", "文档3"]
        res = await RpcFactory().get_normal_model_rpc().arequest_rerank(query, texts)
        print(res.is_success())
        if res.is_success():
            print(res.data)
        else:
            print(res.error_message)

    async def test_arequest_layout_model(self):
        print()
        pic_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/wzy_test/test.jpg?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=1773767263&Signature=g%2Bkpp1dzO%2BE5TDQGlPmxbxbXghs%3D"
        res = await RpcFactory().get_normal_model_rpc().arequest_layout_model(pic_url)
        print(res.is_success())
        if res.is_success():
            print(res.data)
        else:
            print(res.error_message)

    async def test_arequest_ocr(self):
        print()
        pic_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/wzy_test/test.jpg?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=1773767263&Signature=g%2Bkpp1dzO%2BE5TDQGlPmxbxbXghs%3D"
        res = await RpcFactory().get_normal_model_rpc().arequest_ocr(pic_url)
        print(res.is_success())
        if res.is_success():
            print(res.data)
        else:
            print(res.error_message)

    async def test_arequest_table_cls(self):
        print()
        pic_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/wzy_test/test.jpg?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=1773767263&Signature=g%2Bkpp1dzO%2BE5TDQGlPmxbxbXghs%3D"
        res = await RpcFactory().get_normal_model_rpc().arequest_table_cls(pic_url)
        print(res.is_success())
        if res.is_success():
            print(res.data)
        else:
            print(res.error_message)

    async def test_arequest_wired_table(self):
        print()
        pic_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/wzy_test/test.jpg?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=1773767263&Signature=g%2Bkpp1dzO%2BE5TDQGlPmxbxbXghs%3D"
        res = await RpcFactory().get_normal_model_rpc().arequest_wired_table(pic_url)
        print(res.is_success())
        if res.is_success():
            print(res.data)
        else:
            print(res.error_message)

    async def test_arequest_wireless_table(self):
        print()
        pic_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/wzy_test/test.jpg?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=1773767263&Signature=g%2Bkpp1dzO%2BE5TDQGlPmxbxbXghs%3D"
        res = await RpcFactory().get_normal_model_rpc().arequest_wireless_table(pic_url)
        print(res.is_success())
        if res.is_success():
            print(res.data)
        else:
            print(res.error_message)
