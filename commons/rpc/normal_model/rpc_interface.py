# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/07/29 09:20
from abc import ABC, abstractmethod

from commons.rpc.rpc_entity import RpcResp
from typing import Optional, List
from pydantic import BaseModel
from enum import Enum


class Box(BaseModel):
    cls_id: int
    label: str
    score: float
    coordinate: List[float]


class LayOutData(BaseModel):
    boxes: List[Box]


class OcrData(BaseModel):
    rec_texts: List[str]


class TableClsEnum(str, Enum):
    wired = "wired"
    wireless = "wireless"


class TableClsData(BaseModel):
    table_type: TableClsEnum


class WiredTableOcrData(BaseModel):
    table_html: str


class WirelessTableOcrData(BaseModel):
    table_html: str


class TextEmbeddingData(BaseModel):
    """
    向量接口返回数据
    """
    embedding: Optional[List[float]] = None


class RerankerItem(BaseModel):
    text_a: str  # 原比较文本
    text_b: str  # 原待排序文本
    score: float  # 得分
    idx: int  # 原待排序文本的索引


class RerankerData(BaseModel):
    reranker_list: List[RerankerItem]


class NormalRpcInterface(ABC):
    """
    普通模型RPC接口定义，包括板式识别、向量功能
    """

    @abstractmethod
    async def arequest_layout_model(self, pic_url: str) -> RpcResp[LayOutData]:
        """
        板式识别请求
        :param pic_url: 图片下载地址
        :return:
        """
        pass

    @abstractmethod
    async def arequest_ocr(self, pic_url: str) -> RpcResp[OcrData]:
        """
        OCR识别请求
        :param pic_url: 图片下载地址
        :return:
        """
        pass

    @abstractmethod
    async def arequest_table_cls(self, pic_url: str) -> RpcResp[TableClsData]:
        """
        表格类型分类请求
        :param pic_url: 图片下载地址
        :return:
        """
        pass

    @abstractmethod
    async def arequest_wired_table(self, pic_url: str) -> RpcResp[WiredTableOcrData]:
        """
        有线表格OCR识别请求
        :param pic_url: 图片下载地址
        :return:
        """
        pass

    @abstractmethod
    async def arequest_wireless_table(self, pic_url: str) -> RpcResp[WirelessTableOcrData]:
        """
        无线表格OCR识别请求
        :param pic_url: 图片下载地址
        :return:
        """
        pass

    @abstractmethod
    async def arequest_text_embedding(self, text: str) -> RpcResp[TextEmbeddingData]:
        """
        文本向量化请求
        :param text: 文本
        :return:
        """
        pass

    @abstractmethod
    async def arequest_rerank(self, query: str, texts: List[str], threshold: float = None) -> RpcResp[RerankerData]:
        """
        排序请求
        :param query: 排序比较的文本
        :param texts: 待排序的文本
        :param threshold: 可选的阈值，用于过滤低分文本
        :return:
        """
        pass
