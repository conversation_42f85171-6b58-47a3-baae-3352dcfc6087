# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/19 11:20
import json

from commons.rpc.normal_model.rpc_interface import NormalRpcInterface, TextEmbeddingData, LayOutData, OcrData, \
    TableClsData, WiredTableOcrData, WirelessTableOcrData, RerankerData, RerankerItem
from commons.rpc.rpc_client import RpcClient
from commons.rpc.rpc_entity import RpcResp
from commons.rpc.rpc_entity import RpcParams
from typing import List


class GongwenRpc(RpcClient, NormalRpcInterface):
    """
    公文实现的普通模型RPC接口，私有化用
    """

    def __init__(self, rpc_params: RpcParams):
        super().__init__(rpc_params)
        self._emb_req = self._reqs[0]
        self._rerank_req = self._reqs[1]
        self._text_embedding_uri = "/algorithm"
        self._text_reranker_uri = "/algorithm"

    async def arequest_layout_model(self, pic_url: str) -> RpcResp[TextEmbeddingData]:
        pass

    async def arequest_ocr(self, pic_url: str) -> RpcResp[OcrData]:
        pass

    async def arequest_table_cls(self, pic_url: str) -> RpcResp[TableClsData]:
        pass

    async def arequest_wired_table(self, pic_url: str) -> RpcResp[WiredTableOcrData]:
        pass

    async def arequest_wireless_table(self, pic_url: str) -> RpcResp[WirelessTableOcrData]:
        pass

    async def arequest_text_embedding(self, text: str) -> RpcResp[TextEmbeddingData]:
        uri = self._text_embedding_uri
        body = {
            "contents": [text],
            "algorithm_type": "embedding_bge",
            "is_search": False
        }
        try:
            status, text = await self._emb_req.async_call("POST", uri, body=body)
            if status == 200:
                res = json.loads(text)
                if res["code"] == 200 and "data" in res and res["data"] is not None:
                    return RpcResp[TextEmbeddingData].success_response(
                        TextEmbeddingData(embedding=res["data"]))
                else:
                    return self.resp_error(self.arequest_text_embedding.__name__, uri, status, text)
            else:
                return self.http_error(self.arequest_text_embedding.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.arequest_text_embedding.__name__, uri, str(e))

    async def arequest_rerank(self, query: str, texts: List[str], threshold: float = None) -> RpcResp[RerankerData]:
        uri = self._text_reranker_uri
        contents = [[query, text] for text in texts]
        body = {"contents": contents, "algorithm_type": "reranker"}
        try:
            status, text = await self._rerank_req.async_call("POST", uri, body=body)
            if status == 200:
                res = json.loads(text)
                if res["code"] == 200 and "data" in res and res["data"] is not None:
                    reranker_list = []
                    for index, score in enumerate(res["data"]):
                        reranker_list.append(
                            RerankerItem(
                                text_a=query,
                                text_b=texts[index],
                                score=score,
                                idx=index
                            )
                        )
                    reranker_list.sort(key=lambda x: x.score, reverse=True)
                    return RpcResp[RerankerData].success_response(RerankerData(reranker_list=reranker_list))
                else:
                    return self.resp_error(self.arequest_rerank.__name__, uri, status, text)
            else:
                return self.http_error(self.arequest_rerank.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.arequest_rerank.__name__, uri, str(e))
