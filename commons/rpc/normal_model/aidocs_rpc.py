# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/19 11:20
import json

from commons.rpc.normal_model.rpc_interface import NormalRpcInterface, TextEmbeddingData, LayOutData, OcrData, \
    TableClsData, WiredTableOcrData, WirelessTableOcrData, RerankerData
from commons.rpc.rpc_client import RpcClient
from commons.rpc.rpc_entity import RpcResp
from commons.rpc.rpc_entity import RpcParams
from typing import List


class AidocsNormalModelRpc(RpcClient, NormalRpcInterface):
    """
    aidocs 实现的普通模型 RPC 接口
    """

    def __init__(self, rpc_params: RpcParams):
        super().__init__(rpc_params)
        self._req = self._reqs[0]
        self._layout_uri = "/api/v1/ocr/doc_layout_3cls"
        self._orc_uri = "/api/v1/ocr/ocr_pipeline"
        self._table_cls_uri = "/api/v1/ocr/table_cls"
        self._wired_table_ocr_uri = "/api/v1/ocr/wired_ocr"
        self._wireless_table_ocr_uri = "/api/v1/ocr/wireless_table"
        self._text_embedding_uri = "/api/v1/text/vector"
        self._text_reranker_uri = "/api/v1/text/reranker"

    async def arequest_layout_model(self, pic_url: str) -> RpcResp[TextEmbeddingData]:
        uri = self._layout_uri
        body = {
            "image_url": pic_url
        }
        try:
            status, text = await self._req.async_call("POST", uri, body=body)
            if status == 200:
                res = json.loads(text)
                if res["code"] == 0 and "data" in res and res["data"] is not None:
                    data = LayOutData.model_validate(res["data"])
                    return RpcResp[LayOutData].success_response(data)
                else:
                    return self.resp_error(self.arequest_layout_model.__name__, uri, status, text)
            else:
                return self.http_error(self.arequest_layout_model.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.arequest_layout_model.__name__, uri, str(e))

    async def arequest_ocr(self, pic_url: str) -> RpcResp[OcrData]:
        uri = self._orc_uri
        body = {
            "image_url": pic_url
        }
        try:
            status, text = await self._req.async_call("POST", uri, body=body, timeout=30.0)
            if status == 200:
                res = json.loads(text)
                if res["code"] == 0 and "data" in res and res["data"] is not None:
                    data = OcrData.model_validate(res["data"])
                    return RpcResp[OcrData].success_response(data)
                else:
                    return self.resp_error(self.arequest_ocr.__name__, uri, status, text)
            else:
                return self.http_error(self.arequest_ocr.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.arequest_ocr.__name__, uri, str(e))

    async def arequest_table_cls(self, pic_url: str) -> RpcResp[TableClsData]:
        uri = self._table_cls_uri
        body = {
            "image_url": pic_url
        }
        try:
            status, text = await self._req.async_call("POST", uri, body=body)
            if status == 200:
                res = json.loads(text)
                if res["code"] == 0 and "data" in res and res["data"] is not None:
                    data = TableClsData.model_validate(res["data"])
                    return RpcResp[TableClsData].success_response(data)
                else:
                    return self.resp_error(self.arequest_table_cls.__name__, uri, status, text)
            else:
                return self.http_error(self.arequest_table_cls.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.arequest_table_cls.__name__, uri, str(e))

    async def arequest_wired_table(self, pic_url: str) -> RpcResp[WiredTableOcrData]:
        uri = self._wired_table_ocr_uri
        body = {
            "image_url": pic_url
        }
        try:
            status, text = await self._req.async_call("POST", uri, body=body)
            if status == 200:
                res = json.loads(text)
                if res["code"] == 0 and "data" in res and res["data"] is not None:
                    data = WiredTableOcrData.model_validate(res["data"])
                    return RpcResp[WiredTableOcrData].success_response(data)
                else:
                    return self.resp_error(self.arequest_wired_table.__name__, uri, status, text)
            else:
                return self.http_error(self.arequest_wired_table.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.arequest_wired_table.__name__, uri, str(e))

    async def arequest_wireless_table(self, pic_url: str) -> RpcResp[WirelessTableOcrData]:
        uri = self._wireless_table_ocr_uri
        body = {
            "image_url": pic_url
        }
        try:
            status, text = await self._req.async_call("POST", uri, body=body)
            if status == 200:
                res = json.loads(text)
                if res["code"] == 0 and "data" in res and res["data"] is not None:
                    data = WirelessTableOcrData.model_validate(res["data"])
                    return RpcResp[WirelessTableOcrData].success_response(data)
                else:
                    return self.resp_error(self.arequest_wireless_table.__name__, uri, status, text)
            else:
                return self.http_error(self.arequest_wireless_table.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.arequest_wireless_table.__name__, uri, str(e))

    async def arequest_text_embedding(self, text: str) -> RpcResp[TextEmbeddingData]:
        uri = self._text_embedding_uri
        body = {"text": text}
        try:
            status, text = await self._req.async_call("POST", uri, body=body)
            if status == 200:
                res = json.loads(text)
                if res["code"] == 0 and "data" in res and res["data"] is not None:
                    return RpcResp[TextEmbeddingData].success_response(
                        TextEmbeddingData(embedding=res["data"]["embedding"]))
                else:
                    return self.resp_error(self.arequest_text_embedding.__name__, uri, status, text)
            else:
                return self.http_error(self.arequest_text_embedding.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.arequest_text_embedding.__name__, uri, str(e))

    async def arequest_rerank(self, query: str, texts: List[str], threshold: float = None) -> RpcResp[RerankerData]:
        uri = self._text_reranker_uri
        body = {"query": query, "texts": texts}
        if threshold is not None:
            body["threshold"] = threshold
        try:
            status, text = await self._req.async_call("POST", uri, body=body)
            if status == 200:
                res = json.loads(text)
                if res["code"] == 0 and "data" in res and res["data"] is not None:
                    data = RerankerData.model_validate(res["data"])
                    return RpcResp[RerankerData].success_response(data)
                else:
                    return self.resp_error(self.arequest_rerank.__name__, uri, status, text)
            else:
                return self.http_error(self.arequest_rerank.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.arequest_rerank.__name__, uri, str(e))
