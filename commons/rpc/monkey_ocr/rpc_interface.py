# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/07/29 09:20
from abc import ABC, abstractmethod

from commons.rpc.rpc_entity import RpcResp
from typing import Optional
from pydantic import BaseModel

class MonkeyOcrData(BaseModel):
    success: bool = False
    task_type: Optional[str] = None
    content: Optional[str] = None
    message: Optional[str] = None

class MonkeyOcrRpcInterface(ABC):
    """
    Monkey OCR接口定义
    """

    @abstractmethod
    async def arequest_ocr(self, pic_url: str) -> RpcResp[MonkeyOcrData]:
        """
        异步请求OCR识别
        :param pic_url: 图片下载地址
        :return:
        """
        pass
