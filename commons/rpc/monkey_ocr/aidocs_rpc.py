# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/19 11:20
import json

from commons.rpc.monkey_ocr.rpc_interface import MonkeyOcrData, MonkeyOcrRpcInterface
from commons.rpc.rpc_client import RpcClient
from commons.rpc.rpc_entity import RpcResp
from commons.rpc.rpc_entity import RpcParams


class AidocsMonkeyRpc(RpcClient, MonkeyOcrRpcInterface):
    """
    aidocs 实现的monkey ocr 接口
    """
    def __init__(self, rpc_params: RpcParams):
        super().__init__(rpc_params)
        self._req = self._reqs[0]
        self._monkey_ocr_uri = "/ocr/table"

    async def arequest_ocr(self, pic_url: str) -> RpcResp[MonkeyOcrData]:
        uri = self._monkey_ocr_uri
        body = {
            "file": pic_url
        }
        try:
            status, text = await self._req.async_call("POST", uri, body=body)
            if status == 200:
                res = json.loads(text)
                if res["success"] == True and "content" in res and res["content"] is not None:
                    data = MonkeyOcrData.model_validate(res)
                    return RpcResp[MonkeyOcrData].success_response(data)
                else:
                    return self.resp_error(self.arequest_ocr.__name__, uri, status, text)
            else:
                return self.http_error(self.arequest_ocr.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.arequest_ocr.__name__, uri, str(e))
