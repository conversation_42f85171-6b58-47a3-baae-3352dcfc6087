# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/19 11:20
import unittest
from commons.rpc.rpc_factory import RpcFactory
from commons.logger.logger import init_logger
from conf import ConfRpcConfig


class ExampleNormalModelRpc(unittest.IsolatedAsyncioTestCase):
    async def asyncSetUp(self):
        from commons.logger.business_log import logger
        RpcFactory().init(ConfRpcConfig.get_validated_config())

    async def test_arequest_ocr(self):
        print()
        pic_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/wzy_test/test.jpg?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=1773767263&Signature=g%2Bkpp1dzO%2BE5TDQGlPmxbxbXghs%3D"
        res = await RpcFactory().get_monkey_ocr_rpc().arequest_ocr(pic_url)
        print(res.is_success())
        if res.is_success():
            print(res.data)
        else:
            print(res.error_message)
