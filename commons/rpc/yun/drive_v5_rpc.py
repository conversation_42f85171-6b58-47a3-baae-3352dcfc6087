# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/19 11:20
import json

from commons.rpc.yun.rpc_interface import YunRpcInterface, GetDownloadUrlData
from commons.rpc.rpc_client import RpcClient
from commons.rpc.rpc_entity import RpcResp
from commons.rpc.rpc_entity import RpcParams


class DriveV5Rpc(RpcClient, YunRpcInterface):
    # https://cloud-doc.wps.cn/qing_v5/developer.html?h=%2Fapi%2Fv5%2Fdeveloper%2Ffiles
    def __init__(self, rpc_params: RpcParams):
        super().__init__(rpc_params)
        self._req = self._reqs[0]
        self._download_uri = "/api/v5/developer/files/{v5_file_id}/download?slave=true"

    async def aget_download_url(self, v5_file_id: str, drive_id: str = None) -> RpcResp[GetDownloadUrlData]:
        uri = self._download_uri.format(v5_file_id=v5_file_id)
        try:
            status, text = await self._req.async_call("GET", uri)
            if status == 200:
                res = json.loads(text)
                if res["result"] == "ok" and "fileinfo" in res and res["fileinfo"] is not None:
                    return RpcResp[GetDownloadUrlData].success_response(
                        GetDownloadUrlData(url=res["fileinfo"]["url"]))
                else:
                    return self.resp_error(self.aget_download_url.__name__, uri, status, text)
            else:
                return self.http_error(self.aget_download_url.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.aget_download_url.__name__, uri, str(e))

    def get_download_url(self, v5_file_id: str, drive_id: str = None) -> RpcResp[GetDownloadUrlData]:
        uri = self._download_uri.format(v5_file_id=v5_file_id)
        try:
            status, text = self._req.call("GET", uri)
            if status == 200:
                res = json.loads(text)
                if res["result"] == "ok" and "fileinfo" in res and res["fileinfo"] is not None:
                    return RpcResp[GetDownloadUrlData].success_response(
                        GetDownloadUrlData(url=res["fileinfo"]["url"]))
                else:
                    return self.resp_error(self.get_download_url.__name__, uri, status, text)
            else:
                return self.http_error(self.get_download_url.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.get_download_url.__name__, uri, str(e))


if __name__ == '__main__':
    import asyncio
    from commons.logger.logger import init_logger

    # 依赖初始化
    init_logger("", "DEBUG", "err")
    DriveV5Rpc().init("http://drive.wps.cn", "AK20240110KTHTIQ", "84d4a3110edad06a20372da70c31889b")


    async def _async():
        # v5文件id
        v5_file_id = "1001624877391"
        res = await DriveV5Rpc().aget_download_url(v5_file_id)
        if res.is_success:
            print(f"Success: {res.url}")
        else:
            print(f"Error: {res.error_message} (status: {res.status_code})")


    loop = asyncio.get_event_loop()
    loop.run_until_complete(_async())
