# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/19 11:20
import json

from commons.rpc.yun.rpc_interface import YunRpcInterface, GetDownloadUrlData
from commons.rpc.rpc_client import RpcClient
from commons.rpc.rpc_entity import RpcResp
from commons.rpc.rpc_entity import RpcParams


class DocminiV7Rpc(RpcClient, YunRpcInterface):
    def __init__(self, rpc_params: RpcParams):
        super().__init__(rpc_params)
        self._req = self._reqs[0]
        self._download_uri = "/file/dev/v1/drives/{drive_id}/files/{v7_file_id}/download?with_skip_check_type=true"

    async def aget_download_url(self, v7_file_id: str, drive_id: str = None) -> RpcResp[GetDownloadUrlData]:
        uri = self._download_uri.format(v7_file_id=v7_file_id, drive_id=drive_id)
        try:
            status, text = await self._req.async_call("GET", uri)
            if status == 200:
                res = json.loads(text)
                if (res["code"] == 20000000
                        and res["msg"] == "ok"
                        and "data" in res
                        and res["data"] is not None
                ):
                    return RpcResp[GetDownloadUrlData].success_response(
                        GetDownloadUrlData(url=res["data"]["url"]))
                else:
                    return self.resp_error(self.aget_download_url.__name__, uri, status, text)
            else:
                return self.http_error(self.aget_download_url.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.aget_download_url.__name__, uri, str(e))

    def get_download_url(self, v7_file_id: str, drive_id: str = None) -> RpcResp[GetDownloadUrlData]:
        uri = self._download_uri.format(v7_file_id=v7_file_id, drive_id=drive_id)
        try:
            status, text = self._req.call("GET", uri)
            if status == 200:
                res = json.loads(text)
                if (res["code"] == 20000000
                        and res["msg"] == "ok"
                        and "data" in res
                        and res["data"] is not None
                ):
                    return RpcResp[GetDownloadUrlData].success_response(
                        GetDownloadUrlData(url=res["data"]["url"]))
                else:
                    return self.resp_error(self.get_download_url.__name__, uri, status, text)
            else:
                return self.http_error(self.get_download_url.__name__, uri, status, text)
        except Exception as e:
            return self.exception_error(self.get_download_url.__name__, uri, str(e))

