# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/11/19 11:20
import unittest
from commons.rpc.rpc_factory import RpcFactory
from commons.logger.logger import init_logger
from conf import ConfRpcConfig


class ExampleNormalModelRpc(unittest.IsolatedAsyncioTestCase):
    async def asyncSetUp(self):
        from commons.logger.business_log import logger
        RpcFactory().init(ConfRpcConfig.get_validated_config())

    async def test_aget_download_url(self):
        print()
        v5_file_id = "389694716249"
        res = await RpcFactory().get_yun_rpc().aget_download_url(v5_file_id)
        print(res.is_success())
        if res.is_success():
            print(res.data)
        else:
            print(res.error_message)

    async def test_get_download_url(self):
        print()
        v5_file_id = "389694716249"
        res = RpcFactory().get_yun_rpc().get_download_url(v5_file_id)
        print(res.is_success())
        if res.is_success():
            print(res.data)
        else:
            print(res.error_message)
