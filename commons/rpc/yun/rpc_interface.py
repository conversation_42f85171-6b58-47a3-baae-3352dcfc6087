# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/07/29 09:20
from abc import ABC, abstractmethod

from commons.rpc.rpc_entity import RpcResp
from typing import Optional
from pydantic import BaseModel


class GetDownloadUrlData(BaseModel):
    url: Optional[str] = None


class YunRpcInterface(ABC):
    """
    云文档rpc接口定义
    """

    @abstractmethod
    async def aget_download_url(self, file_id: str, drive_id: str = None) -> RpcResp[GetDownloadUrlData]:
        """
        异步获取文件下载链接
        :param file_id: 文件id，drive_v5用的是v5文件id, docmini_v7用的是v7文件id
        :param drive_id: drive id
        :return:
        """
        pass

    @abstractmethod
    def get_download_url(self, file_id: str, drive_id: str = None) -> RpcResp[GetDownloadUrlData]:
        """
        获取文件下载链接
        :param file_id: 文件id，drive_v5用的是v5文件id, docmini_v7用的是v7文件id
        :param drive_id: drive id
        :return:
        """
        pass
