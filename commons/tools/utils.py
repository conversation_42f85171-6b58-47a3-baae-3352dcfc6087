import logging
import sys
import traceback
import json
import functools
import time
import os
from typing import Type<PERSON><PERSON>
from pydantic import BaseModel
from abc import ABCMeta

BaseModelT = TypeVar("BaseModelT", bound=BaseModel)

class Singleton(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]

class SingletonABCMeta(ABCMeta):
    """
    组合元类：同时支持抽象基类(ABC)和单例模式(Singleton)
    
    解决元类冲突问题：当一个类需要同时继承ABC和使用Singleton时使用此元类
    """
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(SingletonABCMeta, cls).__call__(*args, **kwargs)
        return cls._instances[cls]

class WalkDir(object):
    def __init__(self, src_dir):
        self._src_dir = src_dir

    def __call__(self):
        for fdir, _, fnames in os.walk(self._src_dir):
            for fname in fnames:
                yield fname, fdir

def norm_http_params(http_params: dict, is_http_get: bool) -> dict:
    new_params = {}
    for name, value in http_params.items():
        if value is None:
            continue

        if isinstance(value, bool):
            if is_http_get:
                new_params[name] = "true" if value else "false"
            else:
                new_params[name] = value
        elif isinstance(value, dict):
            new_params[name] = norm_http_params(value, is_http_get)
        elif isinstance(value, list):
            new_value = []
            for item in value:
                if isinstance(item, dict):
                    new_value.append(norm_http_params(item, is_http_get))
                else:
                    new_value.append(item)
            new_params[name] = new_value
        else:
            new_params[name] = value
    return new_params

def error_trace():
    exc_type, exc_val, exc_tb = sys.exc_info()
    trace_back = traceback.extract_tb(exc_tb)
    trace_back = [{"file": f"{tb[0]}:{tb[1]}", "line": tb[1], "func": tb[2], "text": tb[3]} for tb in trace_back]
    message_dict = {
        "Error Type": str(exc_type),
        "Message": str(exc_val),
        "TraceBack": trace_back
    }
    logging.error(json.dumps(message_dict, indent=4))

def calc_time(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        time_begin = time.time()
        ret = func(*args, **kwargs)
        # logging.info(f"[{os.getpid()}] func:{func.__name__} cost time:{time.time() - time_begin}")
        # return ret
        # 提取类名和方法名
        qualname_parts = func.__qualname__.split('.')
        if len(qualname_parts) > 1:
            class_name = qualname_parts[-2]
        else:
            class_name = "NoClass"
        logging.info(f"[{os.getpid()}] class:{class_name} func:{func.__name__} cost time:{time.time() - time_begin}")
        return ret

    return wrapper

def async_calc_time(func):
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        time_begin = time.time()
        ret = await func(*args, **kwargs)
        # logging.debug(f"[{os.getpid()}] func:{func.__name__} cost time:{time.time() - time_begin}")
        # return ret
        # 提取类名和方法名
        qualname_parts = func.__qualname__.split('.')
        if len(qualname_parts) > 1:
            class_name = qualname_parts[-2]
        else:
            class_name = "NoClass"
        logging.info(f"[{os.getpid()}] class:{class_name} func:{func.__name__} cost time:{time.time() - time_begin}")
        return ret

    return async_wrapper