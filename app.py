# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/4/21 17:29

from fastapi.middleware.cors import CORSMiddleware
from fastapi import <PERSON><PERSON><PERSON>
from contextlib import asynccontextmanager
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.requests import ClientDisconnect
from contextlib import asynccontextmanager
import uvicorn
import shutil
import logging

from apscheduler.schedulers.asyncio import AsyncIOScheduler

from commons.middleware.request_middleware import RequestIDMiddleware
from commons.thread.multithread import MultiThread
from commons.trace.middleware import TraceMiddleware
from commons.llm_gateway.llm import LLModelRpc
from commons.trace.tracer import GlobalTracer
from commons.tools.kafka_metrics import KafkaConsumerMetrics
from services.kafka_service import fetch_and_store_throughput
from commons.llm_gateway.models.public_model_gateway import PublicModelGateway
from commons.llm_gateway.models.sft_model_gateway import SftModelGateway
from commons.llm_gateway.models.private_model_gateway import PrivateModelGateway
from commons.llm_gateway.ai_privilege import AIPrivilegeRpc
from modules.llm.chat_api import LMModel
from modules.rpc.parse_res_rpc import RecallChunkClient
from routers.router import router
from conf import (
    ConfService,
    auth_platform,
    ConfAuth,
    ConfAppKey,
    ConfWps365,
    ConfStore,
    ConfLogger,
    conf_env,
    DEV,
    ConfProcess,
    ConfKafka,
    ConfPrometheus,
    ConfLLMGateway,
    ConfRecallChunkRpc,
    ConfIPv6,
    ConfRpcConfig
)
from modules.flows.image_desc.image_preprocess import ImagePreprocess
from commons.auth.auth_route import AuthRoute
from commons.tools.wps365api import Wps365api
from commons.db.storedao import StoreDao
from services.datamodel import StoreType
from services.parse_consumer_kafka import ParseKafka, ParseReProduct
from modules.utils import ConnPool
from commons.logger.logger import init_logger
from commons.db.redis5dao import Redis5Dao
from commons.auth.auth_route import AuthPlatform
from commons.hook.hook import _hooked
from commons.thread.multiprocess import MultiProcess
from commons.monitor.prom import init_prometheus
from commons.db.mysqldao import MysqlDao
from commons.db.kafkadao import KafkaAdminClient, KafkaProducerDao
from commons.rpc.rpc_factory import RpcFactory
import multiprocessing
import asyncio
from conf import ConfTrace, ConfTraceRedis

SERVER_NAME = "zl-dst-server"

def init_llm_config():
    from commons.prompt.checkbox_prompt import CheckBox
    from commons.prompt.ocrflux_prompt import OcrFlux
    CheckBox.llm_config = ConfLLMGateway.llm_config.get("CheckBox", None)
    OcrFlux.llm_config = ConfLLMGateway.llm_config.get("OcrFlux", None)


async def init_llm_rpc():
    from conf import ConfLLM
    from conf import ConfSftLLM
    from conf import ConfPrivateLLM
    pub_conf = None
    sft_conf = None
    pri_conf = None
    # 公网AI网关
    if ConfLLM.activated:
        ConfLLM.load()
        pub_conf = PublicModelGateway.Conf(
            host=ConfLLM.host,
            token=ConfLLM.token,
            uid=ConfLLM.uid,
            product_name=ConfLLM.product_name,
            intention_code=ConfLLM.intention_code,
            provider=ConfLLM.provider,
            model=ConfLLM.model,
            sec_scene=ConfLLM.sec_scene,
            sec_from=ConfLLM.sec_from,
            prom_token=ConfLLM.prom_token
        )
        # 权益
        if ConfLLM.privilege_activated:
            AIPrivilegeRpc().init(ak=ConfLLM.privilege_ak,
                                  sk=ConfLLM.privilege_sk,
                                  host=ConfLLM.privilege_host,
                                  product_name=ConfLLM.product_name,
                                  intention_code=ConfLLM.intention_code)

    # 自部署模型网关
    if ConfSftLLM.activated:
        ConfSftLLM.load()
        sft_conf = SftModelGateway.Conf(
            host=ConfSftLLM.host,
            token=ConfSftLLM.token,
            prom_token=ConfSftLLM.prom_token)
    # 私有化AI网关开关
    if ConfPrivateLLM.activated:
        ConfPrivateLLM.load()
        pri_conf = PrivateModelGateway.Conf(
            host=ConfPrivateLLM.host,
            platform=ConfPrivateLLM.platform,
            prom_token=ConfPrivateLLM.prom_token,
            minimax_model=ConfPrivateLLM.minimax_model
        )
    LLModelRpc().create_models(pub_conf, sft_conf,
                               pri_conf, pool_max=ConfLLM.thread_num)
    MultiThread.set_cpu_count(ConfLLM.thread_num)


async def init_redis5_dao():
    from conf import ConfRedis
    ConfRedis.load()
    await Redis5Dao().init(ConfRedis.hosts, ConfRedis.password, ConfRedis.prefix, ConfRedis.cluster)


def init_DB():
    from conf import ConfDB
    ConfDB.load()
    MysqlDao().init(ConfDB.host, ConfDB.port, ConfDB.user,
                    ConfDB.pwd, ConfDB.db_name, ConfDB.pools, ConfDB.env)


async def init_kafka():
    # kafka 管理员，生产者初始化
    await KafkaAdminClient().init(ConfKafka.bootstrap_servers)
    level_topic_dict = {}
    for c in ConfKafka.parse_config:
        if not await KafkaAdminClient().create_topic(c["topic"], c["partition"],
                                                     topic_config={"retention.ms": "2592000000"}):
            raise Exception(f"init_kafka create topic {c['topic']} failed")
        level_topic_dict[c["level"]] = c["topic"]
    await KafkaProducerDao().init(level_topic_dict=level_topic_dict, bootstrap_servers=ConfKafka.bootstrap_servers)


async def init_services():
    RpcFactory().init(ConfRpcConfig.get_validated_config())
    RecallChunkClient().init(ConfRecallChunkRpc.host, ConfAppKey.ak, ConfAppKey.sk, ConfRecallChunkRpc.sig_type)
    ConfWps365.load()
    ImagePreprocess.gateway = ConfLLMGateway.image_preprocess_gateway
    if ImagePreprocess.gateway == LLModelRpc.Gateway.Public:
        ImagePreprocess.multimodal_selector = LLModelRpc.ModelSelector(
            model=ConfLLMGateway.image_preprocess_model,
            provider=ConfLLMGateway.image_preprocess_provider,
            version=ConfLLMGateway.image_preprocess_version)
    LMModel.gateway = ConfLLMGateway.summary_gateway
    if LMModel.gateway == LLModelRpc.Gateway.Public:
        LMModel.selector = LLModelRpc.ModelSelector(model=ConfLLMGateway.summary_model,
                                                    provider=ConfLLMGateway.summary_provider,
                                                    version=ConfLLMGateway.summary_version)
    Wps365api().init(
        host=ConfWps365.api_host,
        pool_max=ConfWps365.pool_max,
        openapi_host=ConfWps365.openapi_host,
        openapi_ak=ConfWps365.openapi_ak,
        openapi_sk=ConfWps365.openapi_sk,
        woa_custom_ak=ConfWps365.woa_custom_ak
    )
    ConfStore.load()
    StoreDao().init(ConfStore.host, ConfStore.ak,
                    ConfStore.sk, ConfStore.bucket, StoreType.Ks3)
    ConnPool().init()
    KafkaConsumerMetrics().init(ConfKafka.bootstrap_servers)
    await init_redis5_dao()
    # await init_bgem3()
    init_DB()
    await init_llm_rpc()
    init_llm_config()

def init_multi_process(name: str = ""):
    # 进程池初始化，公共类、单例类等需要在此初始化
    # if platform.system() == 'Windows':
    init_logger("", ConfLogger.log_level,
                ConfLogger.monitor_name, with_color=False)
    init_prometheus(ConfPrometheus.gateway, ConfPrometheus.job_name_prefix,
                    ConfPrometheus.username, ConfPrometheus.password)
    logging.info(
        f"{name} 进程初始化=================================================================")
    # 初始化消息循环
    asyncio.set_event_loop(asyncio.new_event_loop())
    asyncio.get_event_loop().run_until_complete(init_services())


def init_global_tracer():
    # 全局tracer初始化,私网默认不启用tracer
    if auth_platform == AuthPlatform.private:
        GlobalTracer.init_tracer(hosts="",
                                 password="",
                                 cluster=False,
                                 time_out=0,
                                 disable_tracing=True)
    else:
        ConfTraceRedis.load()
        GlobalTracer.init_tracer(hosts=ConfTraceRedis.hosts,
                                 password=ConfTraceRedis.password,
                                 cluster=ConfTraceRedis.cluster,
                                 time_out=ConfTraceRedis.timeout*3600*24,
                                 service_name=SERVER_NAME,
                                 verbose=ConfTrace.verbose,
                                 disable_tracing=False)

async def uninit():
    MultiProcess().close()
    await KafkaAdminClient().close()


async def init():
    init_global_tracer()
    # init_logger("", ConfLogger.log_level, ConfLogger.monitor_name)
    init_prometheus(ConfPrometheus.gateway, ConfPrometheus.job_name_prefix,
                    ConfPrometheus.username, ConfPrometheus.password)
    AuthRoute.init(auth_platform, ConfAuth.host, ConfAuth.aksk_dict)
    await init_kafka()
    await init_services()
    ParseReProduct().init(ConfKafka.re_product_dist_lock_key)
    ParseReProduct().re_product_start()


@asynccontextmanager
async def lifespan(_: FastAPI):
    await init()
    # 初始化调度器
    scheduler = AsyncIOScheduler()
    scheduler.add_job(
        fetch_and_store_throughput,
        trigger="cron",
        minute="*/6",  # 每6分钟触发
        second=0,  # 在每分钟的第0秒触发检查
        coalesce=True,  # 合并可能错过的执行
        misfire_grace_time=30  # 允许30秒内的延迟执行
    )

    scheduler.start()
    yield
    # 关闭调度器
    scheduler.shutdown()
    await uninit()

app = FastAPI(
    title=SERVER_NAME,
    lifespan=lifespan,
    openapi_url=None,
    docs_url=None,
    redoc_url=None)

# 添加全局异常处理器
@app.exception_handler(ClientDisconnect)
async def client_disconnect_handler(request: Request, exc: ClientDisconnect):
    logging.warning(f"Client disconnected for request: {request.url}")
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={"detail": "Client disconnected"}
    )

app.include_router(router)
app.add_middleware(RequestIDMiddleware)
app.add_middleware(TraceMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

if __name__ == "__main__":
    original_def = None
    hook = False
    if conf_env != DEV:
        # 线上python3.10 linux spawn模式下会出现多余的-B参数，导致启动失败，需打补丁
        original_def = _hooked()
        hook = True
    """
    进程模式：
    主进程
      |--fastapi主进程
      |--主进程进程池
                |---子进程1
                |--- ...
                |---kafka low消费进程
                         |---kafka消费任务子进程1
                         |--- ...
                         |---kafka消费任务子进程consumer_process_work_num
                |---kafka normal消费进程
                         |--- ...
                |---kafka high消费进程
                         |--- ...
                |--- ...
                |---子进程ConfProcess.process_work_num
    """
    # linux spawn多进程要在__main__中初始化
    # 多进程内存管理设置为spawn模式，避免linux fork模式下内存共享问题

    if not multiprocessing.get_start_method(allow_none=True):
        multiprocessing.set_start_method("spawn")

    # multiprocessing.set_start_method("spawn")
    MultiProcess().init(max_workers=ConfProcess.process_work_num, initializer=init_multi_process,
                        initargs=("主进程池",))
    # 进程池得实际执行时才会正式创建
    MultiProcess().map(lambda: print("multi_process_start"),
                       range(ConfProcess.process_work_num))
    # kafka多进程消费者初始化
    parse_kafka_list = []
    for c in ConfKafka.parse_config:
        pk = ParseKafka(ConfKafka.bootstrap_servers,
                        c["topic"], c["consumer_group_id"])
        pk.consumer_by_multiprocess(
            worker_num=c["consumer_process_work_num"],
            task_num=c["consumer_process_task_num"],
            hook=hook,
            initializer=init_multi_process,
            initargs=(f"{c['consumer_group_id']}消费任务",)
        )
        parse_kafka_list.append(pk)

    # 添加信号处理，确保优雅关闭
    import signal
    import sys
    import atexit
    
    def signal_handler(signum, frame):
        logging.info(f"Received signal {signum}, shutting down gracefully...")
        # 设置一个快速退出的标志
        import threading
        cleanup_thread = threading.Thread(target=cleanup_resources)
        cleanup_thread.daemon = True
        cleanup_thread.start()
        
        # 给清理线程最多5秒时间
        cleanup_thread.join(timeout=5)
        if cleanup_thread.is_alive():
            logging.warning("Cleanup timeout, forcing exit...")
        sys.exit(0)
    
    def cleanup_resources():
        """清理所有资源"""
        try:
            # 关闭调度器
            if 'scheduler' in locals() and scheduler.running:
                scheduler.shutdown(wait=False)
                logging.info("Scheduler shutdown completed")
        except Exception as e:
            logging.error(f"Error during scheduler shutdown: {e}")
            
        try:
            # 关闭多进程池
            MultiProcess().close()
            logging.info("MultiProcess pool shutdown completed")
        except Exception as e:
            logging.error(f"Error during MultiProcess shutdown: {e}")
            
        try:
            # 清理其他资源（如数据库连接、文件句柄等）
            # 这里可以添加其他需要清理的资源
            pass
        except Exception as e:
            logging.error(f"Error during resource cleanup: {e}")
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 注册程序退出时的清理函数
    atexit.register(cleanup_resources)

    try:
        host = "0.0.0.0"
        if ConfIPv6.is_ipv6:
            host = "::"
        uvicorn.run(
            app=app,
            host="0.0.0.0",
            port=ConfService.port,
            timeout_keep_alive=ConfService.timeout_keep_alive,
            log_config=None
        )
    except KeyboardInterrupt:
        logging.info("Received KeyboardInterrupt, shutting down...")
        cleanup_resources()
