[run]
omit =
    */__pycache__/*
    */tests/*
    */venv/*
    */.venv/*
    */env/*
    */.env/*
    */example.py
    */*/example.py
    */*/*/example.py
    */*/*/*/example.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
