"""
routers.router 模块的集成测试
重点测试：
1. 全面的参数覆盖（所有参数组合）
2. 正确的外部依赖mock（只mock外部依赖，保留内部业务逻辑）
3. 异步流程测试（background mode的完整流程）
"""
import pytest
import json
import os
from unittest.mock import patch, AsyncMock, MagicMock
from typing import Dict, Any
from fastapi import Request, BackgroundTasks
from io import BytesIO

# 设置测试环境变量
os.environ.setdefault("conf_env", "test")

# 导入要测试的router函数和数据模型
from routers.router import safe_service
from services.parse import general_parse_service, general_parse_res_service
from services.table_merge import merge_table
from services.version import get_version
from services.kafka_service import get_kafka_lag, get_kafka_throughput
from services.datamodel import (
    ReqMergeTable, ReqGeneralParseRes, ReqGeneralParse,
    FileType, ParseTarget, ReqGeneralParseType, GeneralParseReqLevel,
    RespGeneralParseRes, RespMergeTableRes, RespVersionRes
)
from routers.httpcode import HTTPCODE


@pytest.fixture
def sample_merge_table_request():
    """示例表格合并请求数据"""
    return ReqMergeTable(content=[
        "<table><tr><td>列1</td><td>列2</td></tr></table>",
        "<table><tr><td>数据1</td><td>数据2</td></tr></table>",
        "<table><tr><td>数据3</td><td>数据4</td></tr></table>"
    ])


@pytest.fixture
def sample_general_parse_res_request():
    """示例通用解析结果请求数据"""
    return ReqGeneralParseRes(
        token="test-token-12345",
        return_ks3_url=True,
        use_external_link=False,
        parse_target=[ParseTarget.chunk, ParseTarget.summary]
    )


@pytest.fixture
def mock_request():
    """创建模拟的FastAPI Request对象"""
    request = MagicMock(spec=Request)
    request.headers = {
        "content-type": "application/json",
        "AI-Gateway-Uid": "test-uid",
        "AI-Gateway-Company-Id": "test-company"
    }

    # 模拟body方法返回JSON字符串
    request_data = {
        "file_name": "test_document.pdf",
        "file_type": "pdf",
        "parse_target": ["chunk"],
        "return_ks3_url": True,
        "embed_enabled": True,
        "req_type": "normal",
        "req_level": "normal",
        "file_url": "http://example.com/test.pdf"
    }

    import json
    request.body = AsyncMock(return_value=json.dumps(request_data).encode('utf-8'))
    return request


@pytest.fixture
def mock_background_tasks():
    """创建模拟的BackgroundTasks对象"""
    return MagicMock(spec=BackgroundTasks)


class TestParseIntegration:
    """解析相关接口的集成测试 - 直接测试业务逻辑"""

    @pytest.mark.asyncio
    @patch('services.parse.PipelineFactory')
    @patch('services.parse.convert_handler_results_to_resp')
    async def test_parse_pipeline_normal_mode_success(self, mock_convert, mock_pipeline_factory,
                                                     mock_request, mock_background_tasks):
        """测试解析管道接口正常模式成功场景 - 直接调用业务逻辑"""
        # 设置pipeline mock返回值
        mock_pipeline = AsyncMock()
        mock_pipeline_factory.create_document_pipeline.return_value = mock_pipeline

        mock_result = MagicMock()
        mock_result.handler_results = {"chunk": [{"content": "test chunk", "page": 1}]}
        mock_pipeline.execute.return_value = mock_result

        # 设置convert函数mock
        from modules.entity.parse_entity import RespGeneralParseData, GeneralParseStatus, ParseRes
        mock_convert.return_value = RespGeneralParseData(
            status=GeneralParseStatus.ok,
            token="test-token",
            parse_res=ParseRes(chunks=[], summary="test summary")
        )

        # 直接调用业务逻辑函数
        result = await general_parse_service(mock_request, mock_background_tasks)

        # 验证结果
        assert isinstance(result, RespGeneralParseRes)
        assert result.code == HTTPCODE.OK
        assert result.data.status == GeneralParseStatus.ok

        # 验证业务逻辑被调用
        mock_pipeline_factory.create_document_pipeline.assert_called_once()
        mock_pipeline.execute.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse.Redis5Dao')
    async def test_parse_pipeline_background_mode(self, mock_redis, mock_background_tasks):
        """测试解析管道接口后台模式 - 直接测试后台任务添加逻辑"""
        # 设置Redis mock
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.aset = AsyncMock()

        # 创建后台模式的请求
        request = MagicMock(spec=Request)
        request.headers = {
            "content-type": "application/json",
            "AI-Gateway-Uid": "test-uid",
            "AI-Gateway-Company-Id": "test-company"
        }

        request_data = {
            "file_name": "test_document.pdf",
            "file_type": "pdf",
            "parse_target": ["chunk"],
            "req_type": "background",
            "req_level": "normal",
            "file_url": "http://example.com/test.pdf"
        }

        import json
        request.body = AsyncMock(return_value=json.dumps(request_data).encode('utf-8'))

        # 直接调用业务逻辑函数
        result = await general_parse_service(request, mock_background_tasks)

        # 验证结果
        assert isinstance(result, RespGeneralParseRes)
        assert result.code == HTTPCODE.OK
        assert result.data.status == "wait"  # 后台模式应该返回wait状态
        assert result.data.token is not None

        # 验证后台任务被添加
        mock_background_tasks.add_task.assert_called_once()
        # 验证Redis设置被调用
        mock_redis_instance.aset.assert_called_once()

    @pytest.mark.asyncio
    @patch('services.parse.Redis5Dao')
    @patch('services.parse.requests')
    async def test_general_parse_res_success(self, mock_requests, mock_redis, sample_general_parse_res_request):
        """测试获取解析结果接口成功场景 - 直接测试业务逻辑"""
        # 设置Redis mock
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance

        # 设置多次调用的返回值
        def redis_side_effect(key, default=None):
            if "parse_background_status" in key:
                return "ok"
            elif "parse_background_content_path" in key:
                return "http://ks3.com/chunk_result.json"
            else:
                return default

        mock_redis_instance.agetstring.side_effect = redis_side_effect

        # 设置HTTP请求mock - 返回正确的数据结构
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "chunks": [
                {
                    "chunk_id": "1",
                    "page_size": 10,
                    "content": "test chunk",
                    "label": "text",
                    "page_num": [1],
                    "block": ["block1"]
                }
            ]
        }
        mock_requests.get.return_value = mock_response

        # 直接调用业务逻辑函数
        result = await general_parse_res_service(sample_general_parse_res_request)

        # 验证结果
        assert isinstance(result, RespGeneralParseRes)
        assert result.code == HTTPCODE.OK
        assert result.data.status == "ok"
        # 当return_ks3_url为True时，返回res_ks3_url而不是parse_res
        assert result.data.res_ks3_url == "http://ks3.com/chunk_result.json"

        # 验证Redis查询被调用
        mock_redis_instance.agetstring.assert_called()

    @pytest.mark.asyncio
    @patch('services.parse.Redis5Dao')
    async def test_general_parse_res_invalid_token(self, mock_redis):
        """测试无效token的解析结果请求 - 直接测试业务逻辑"""
        # 设置Redis mock返回fail状态
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.agetstring.return_value = "fail"

        invalid_request = ReqGeneralParseRes(
            token="invalid-token",
            return_ks3_url=True,
            parse_target=[ParseTarget.chunk]
        )

        # 直接调用业务逻辑函数
        result = await general_parse_res_service(invalid_request)

        # 验证结果
        assert isinstance(result, RespGeneralParseRes)
        assert result.code == HTTPCODE.OK
        assert result.data.status == "fail"  # 无效token应该返回fail状态


class TestTableMergeIntegration:
    """表格合并接口的集成测试 - 直接测试业务逻辑"""

    @pytest.mark.asyncio
    async def test_merge_table_success(self, sample_merge_table_request):
        """测试表格合并成功场景 - 直接调用业务逻辑"""
        # 直接调用业务逻辑函数
        result = await merge_table(sample_merge_table_request)

        # 验证结果
        assert isinstance(result, RespMergeTableRes)
        assert result.code == HTTPCODE.OK
        assert result.data is not None
        assert result.data.content is not None
        assert len(result.data.content) > 0

        # 验证合并后的内容是HTML表格格式
        merged_content = result.data.content[0]
        assert "<table>" in merged_content
        assert "</table>" in merged_content

    @pytest.mark.asyncio
    async def test_merge_table_empty_content(self):
        """测试空内容的表格合并 - 直接测试业务逻辑"""
        empty_request = ReqMergeTable(content=[])

        # 直接调用业务逻辑函数
        result = await merge_table(empty_request)

        # 验证结果 - 空内容应该返回错误
        assert isinstance(result, RespMergeTableRes)
        assert result.code == HTTPCODE.ERROR
        assert "Invalid content provided for merging" in result.message

    @pytest.mark.asyncio
    async def test_merge_table_single_content(self):
        """测试单个内容的表格合并 - 直接测试业务逻辑"""
        single_request = ReqMergeTable(content=[
            "<table><tr><td>单个表格</td></tr></table>"
        ])

        # 直接调用业务逻辑函数
        result = await merge_table(single_request)

        # 验证结果 - 单个内容应该返回错误（需要至少2个内容才能合并）
        assert isinstance(result, RespMergeTableRes)
        assert result.code == HTTPCODE.ERROR
        assert "Invalid content provided for merging" in result.message

    @pytest.mark.asyncio
    @patch('services.table_merge.merge_table_by_force')
    async def test_merge_table_large_content(self, mock_merge_function):
        """测试大量内容的表格合并 - 直接测试业务逻辑"""
        # 设置mock返回值
        mock_merge_function.return_value = "<table><tr><td>合并后的大表格</td></tr></table>"

        large_request = ReqMergeTable(content=[
            f"<table><tr><td>表格{i}</td></tr></table>" for i in range(100)
        ])

        # 直接调用业务逻辑函数
        result = await merge_table(large_request)

        # 验证结果
        assert isinstance(result, RespMergeTableRes)
        assert result.code == HTTPCODE.OK
        assert result.data.content is not None

        # 验证merge_table_by_force被调用
        mock_merge_function.assert_called_once_with(large_request.content)

    @pytest.mark.asyncio
    @patch('services.table_merge.merge_table_by_force')
    async def test_merge_table_exception_handling(self, mock_merge_function):
        """测试表格合并异常处理 - 直接测试业务逻辑"""
        # 设置mock抛出异常
        mock_merge_function.side_effect = Exception("合并失败")

        request = ReqMergeTable(content=[
            "<table><tr><td>表格1</td></tr></table>",
            "<table><tr><td>表格2</td></tr></table>"
        ])

        # 直接调用业务逻辑函数
        result = await merge_table(request)

        # 验证结果 - 异常应该被捕获并返回错误
        assert isinstance(result, RespMergeTableRes)
        assert result.code == HTTPCODE.ERROR


class TestVersionIntegration:
    """版本接口的集成测试 - 直接测试业务逻辑"""

    @pytest.mark.asyncio
    @patch('conf.ConfParseVersion.version', 'v1.2.3-integration-test')
    async def test_parse_version_success(self):
        """测试获取版本信息成功场景 - 直接调用业务逻辑"""
        # 直接调用业务逻辑函数
        result = await get_version()

        # 验证结果
        assert isinstance(result, RespVersionRes)
        assert result.code == HTTPCODE.OK
        assert result.data is not None
        assert result.data.version == "v1.2.3-integration-test"

    @pytest.mark.asyncio
    @patch('conf.ConfParseVersion.version', 'v2.0.0-beta')
    async def test_parse_version_different_version(self):
        """测试不同版本字符串 - 直接测试业务逻辑"""
        # 直接调用业务逻辑函数
        result = await get_version()

        # 验证结果
        assert isinstance(result, RespVersionRes)
        assert result.code == HTTPCODE.OK
        assert result.data.version == "v2.0.0-beta"

    @pytest.mark.asyncio
    async def test_parse_version_exception_handling(self):
        """测试版本接口异常处理 - 直接测试业务逻辑"""
        with patch('conf.ConfParseVersion.version', side_effect=Exception("配置错误")):
            # 直接调用业务逻辑函数
            result = await get_version()

            # 验证结果 - 异常应该被捕获并返回错误
            assert isinstance(result, RespVersionRes)
            assert result.code == HTTPCODE.ERROR

    @pytest.mark.asyncio
    @patch('conf.ConfParseVersion.version', 'v1.0.0-stable')
    async def test_parse_version_multiple_calls(self):
        """测试多次调用版本接口 - 直接测试业务逻辑"""
        for _ in range(3):
            result = await get_version()

            assert isinstance(result, RespVersionRes)
            assert result.code == HTTPCODE.OK
            assert result.data.version == "v1.0.0-stable"


class TestKafkaIntegration:
    """Kafka监控接口的集成测试 - 直接测试业务逻辑"""

    @pytest.mark.asyncio
    @patch('conf.ConfKafka.parse_config', [
        {"topic": "test-topic-1", "consumer_group_id": "test-group-1"},
        {"topic": "test-topic-2", "consumer_group_id": "test-group-2"}
    ])
    @patch('services.kafka_service.KafkaConsumerMetrics')
    async def test_kafka_lag_success(self, mock_metrics):
        """测试获取Kafka延迟成功场景 - 直接调用业务逻辑"""
        # 设置mock返回值
        mock_instance = mock_metrics.return_value
        mock_instance.get_lag = AsyncMock(side_effect=[100, 200])

        # 直接调用业务逻辑函数
        result = await get_kafka_lag()

        # 验证结果
        assert result.code == HTTPCODE.OK
        assert result.data is not None
        assert len(result.data) == 2

        # 验证数据结构
        first_item = result.data[0]
        assert first_item.topic == "test-topic-1"
        assert first_item.group_id == "test-group-1"
        assert first_item.total_lag == 100

        second_item = result.data[1]
        assert second_item.topic == "test-topic-2"
        assert second_item.group_id == "test-group-2"
        assert second_item.total_lag == 200

        # 验证业务逻辑被调用
        assert mock_instance.get_lag.call_count == 2

    @pytest.mark.asyncio
    @patch('conf.ConfKafka.re_throughput_key', 'test_throughput_key')
    @patch('services.kafka_service.Redis5Dao')
    async def test_kafka_throughput_success(self, mock_redis):
        """测试获取Kafka吞吐量成功场景 - 直接调用业务逻辑"""
        # 设置Redis mock返回值
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance

        throughput_data = [
            {"topic": "test-topic-1", "group_id": "test-group-1", "throughput": 15.5},
            {"topic": "test-topic-2", "group_id": "test-group-2", "throughput": 25.3}
        ]
        mock_redis_instance.agetstring.return_value = json.dumps(throughput_data)

        # 直接调用业务逻辑函数
        result = await get_kafka_throughput()

        # 验证结果
        assert result.code == HTTPCODE.OK
        assert result.data is not None
        assert len(result.data) == 2

        # 验证数据结构
        first_item = result.data[0]
        assert first_item.topic == "test-topic-1"
        assert first_item.group_id == "test-group-1"
        assert first_item.throughput == 15.5

        second_item = result.data[1]
        assert second_item.topic == "test-topic-2"
        assert second_item.group_id == "test-group-2"
        assert second_item.throughput == 25.3

        # 验证Redis查询被调用
        mock_redis_instance.agetstring.assert_called_once_with('test_throughput_key', "")

    @pytest.mark.asyncio
    @patch('services.kafka_service.Redis5Dao')
    async def test_kafka_throughput_no_data(self, mock_redis):
        """测试Kafka吞吐量无数据场景 - 直接测试业务逻辑"""
        # 设置Redis mock返回空数据
        mock_redis_instance = AsyncMock()
        mock_redis.return_value = mock_redis_instance
        mock_redis_instance.agetstring.return_value = ""

        # 直接调用业务逻辑函数
        result = await get_kafka_throughput()

        # 验证结果 - 无数据应该返回错误
        assert result.code == HTTPCODE.ERROR
        assert "No throughput data found in Redis" in result.message

    @pytest.mark.asyncio
    @patch('conf.ConfKafka.parse_config', [{"topic": "test-topic", "consumer_group_id": "test-group"}])
    @patch('services.kafka_service.KafkaConsumerMetrics')
    async def test_kafka_lag_exception_handling(self, mock_metrics):
        """测试Kafka延迟接口异常处理 - 直接测试业务逻辑"""
        # 设置mock抛出异常
        mock_instance = mock_metrics.return_value
        mock_instance.get_lag = AsyncMock(side_effect=Exception("Kafka连接失败"))

        # 直接调用业务逻辑函数
        result = await get_kafka_lag()

        # 验证结果 - 异常应该被捕获并返回错误
        assert result.code == HTTPCODE.ERROR


class TestSafeServiceIntegration:
    """safe_service包装函数的集成测试"""

    @pytest.mark.asyncio
    async def test_safe_service_success(self):
        """测试safe_service成功场景"""
        from modules.entity.version_entity import RespVersionData

        async def mock_service():
            return RespVersionRes(code=HTTPCODE.OK, data=RespVersionData(version="v1.0.0"))

        result = await safe_service(mock_service)

        assert result.code == HTTPCODE.OK

    @pytest.mark.asyncio
    async def test_safe_service_exception_handling(self):
        """测试safe_service异常处理"""
        async def mock_service():
            raise Exception("服务异常")

        result = await safe_service(mock_service)

        assert result.code == HTTPCODE.ERROR
        assert result.message == "Service Error"
