"""
commons.db.miniodao 模块的测试
"""
import pytest
import io
from unittest.mock import Mock, patch
from datetime import timedelta
import minio

from commons.db.miniodao import MinioDao


class TestMinioDao:
    """测试 MinioDao 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        MinioDao._instances = {}
        self.minio_dao = MinioDao()

    def test_singleton_pattern(self):
        """测试单例模式"""
        dao1 = MinioDao()
        dao2 = MinioDao()
        assert dao1 is dao2

    def test_init_default_values(self):
        """测试初始化默认值"""
        assert self.minio_dao.prefix == ""
        assert self.minio_dao.conn is None

    @patch('minio.Minio')
    def test_init_success(self, mock_minio_class):
        """测试成功初始化"""
        mock_minio_instance = Mock(spec=minio.Minio)
        mock_minio_class.return_value = mock_minio_instance
        
        host = "127.0.0.1:9000"
        ak = "test_access_key"
        sk = "test_secret_key"
        bucket = "test_bucket"
        
        self.minio_dao.init(host, ak, sk, bucket)
        
        # 验证初始化结果
        assert self.minio_dao.bucket == bucket
        assert self.minio_dao.conn == mock_minio_instance
        
        # 验证 Minio 客户端被正确创建
        mock_minio_class.assert_called_once_with(
            endpoint=host,
            access_key=ak,
            secret_key=sk,
            secure=False,
            region="us-east-1"
        )

    def test_upload_from_bytes_success(self):
        """测试从字节上传成功"""
        # 模拟 Minio 连接
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.put_object.return_value = None
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        test_data = b"test file content"
        mpath = "test/path/file.txt"
        
        result = self.minio_dao.upload_from_bytes(mpath, test_data)
        
        assert result is True
        mock_conn.put_object.assert_called_once()
        call_args = mock_conn.put_object.call_args
        assert call_args[1]['bucket_name'] == "test_bucket"
        assert call_args[1]['object_name'] == mpath
        assert call_args[1]['length'] == len(test_data)
        # 验证数据流
        data_stream = call_args[1]['data']
        assert isinstance(data_stream, io.BytesIO)

    def test_upload_from_bytes_failure(self):
        """测试从字节上传失败"""
        # 模拟 Minio 连接抛出异常
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.put_object.side_effect = Exception("Upload failed")
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        test_data = b"test file content"
        mpath = "test/path/file.txt"
        
        with patch('logging.error') as mock_log_error:
            result = self.minio_dao.upload_from_bytes(mpath, test_data)
            
            assert result is False
            mock_log_error.assert_called_once()

    def test_upload_from_text_success(self):
        """测试从文本上传成功"""
        # 模拟 upload_from_bytes 方法
        with patch.object(self.minio_dao, 'upload_from_bytes', return_value=True) as mock_upload:
            test_text = "这是测试文本内容"
            mpath = "test/path/text_file.txt"
            
            result = self.minio_dao.upload_from_text(mpath, test_text)
            
            assert result is True
            mock_upload.assert_called_once_with(mpath, test_text.encode("utf-8"))

    def test_upload_from_file_success(self):
        """测试从文件上传成功"""
        # 模拟 Minio 连接
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.fput_object.return_value = None
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        mpath = "test/path/uploaded_file.txt"
        fpath = "/local/path/source_file.txt"
        
        result = self.minio_dao.upload_from_file(mpath, fpath)
        
        assert result is True
        mock_conn.fput_object.assert_called_once_with(
            bucket_name="test_bucket",
            object_name=mpath,
            file_path=fpath
        )

    def test_upload_from_file_failure(self):
        """测试从文件上传失败"""
        # 模拟 Minio 连接抛出异常
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.fput_object.side_effect = Exception("File upload failed")
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        mpath = "test/path/uploaded_file.txt"
        fpath = "/local/path/source_file.txt"
        
        with patch('logging.error') as mock_log_error:
            result = self.minio_dao.upload_from_file(mpath, fpath)
            
            assert result is False
            mock_log_error.assert_called_once()

    def test_download_to_text_success(self):
        """测试下载为文本成功"""
        # 模拟 Minio 连接
        mock_response = Mock()
        mock_response.data.decode = Mock(return_value="测试文件内容")
        
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.get_object.return_value = mock_response
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        mpath = "test/path/file.txt"
        
        result = self.minio_dao.download_to_text(mpath)

        assert result == (True, "测试文件内容")
        mock_conn.get_object.assert_called_once_with(
            bucket_name="test_bucket",
            object_name=mpath
        )
        mock_response.data.decode.assert_called_once_with("utf-8")

    def test_download_to_text_failure(self):
        """测试下载为文本失败"""
        # 模拟 Minio 连接抛出异常
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.get_object.side_effect = Exception("Download failed")
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        mpath = "test/path/file.txt"
        
        with patch('logging.error') as mock_log_error, \
             patch('logging.exception') as mock_log_exception:
            result = self.minio_dao.download_to_text(mpath)

            assert result == (False, "")
            # 验证两个日志方法都被调用了（实际格式包含错误消息）
            mock_log_error.assert_called_once()
            # 验证调用参数包含路径信息
            call_args = mock_log_error.call_args[0][0]
            assert mpath in call_args
            mock_log_exception.assert_called_once()

    def test_download_to_file_success(self):
        """测试下载到文件成功"""
        # 模拟 Minio 连接和响应对象
        mock_response = Mock()
        mock_response.stream.return_value = [b"test data chunk 1", b"test data chunk 2"]

        mock_conn = Mock(spec=minio.Minio)
        mock_conn.get_object.return_value = mock_response

        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"

        mpath = "test/path/remote_file.txt"

        # 使用临时文件进行测试
        import tempfile
        import os
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            fpath = tmp_file.name

        try:
            result = self.minio_dao.download_to_file(mpath, fpath)

            assert result is True
            mock_conn.get_object.assert_called_once_with(
                bucket_name="test_bucket",
                object_name=mpath
            )

            # 验证文件内容
            with open(fpath, "rb") as f:
                content = f.read()
                assert content == b"test data chunk 1test data chunk 2"
        finally:
            # 清理临时文件
            if os.path.exists(fpath):
                os.unlink(fpath)

    def test_download_to_file_failure(self):
        """测试下载到文件失败"""
        # 模拟 Minio 连接抛出异常
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.get_object.side_effect = Exception("File download failed")
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        mpath = "test/path/remote_file.txt"
        fpath = "/local/path/downloaded_file.txt"
        
        with patch('logging.error') as mock_log_error:
            result = self.minio_dao.download_to_file(mpath, fpath)
            
            assert result is False
            mock_log_error.assert_called_once()

    def test_generate_url_success(self):
        """测试生成URL成功"""
        # 模拟 Minio 连接
        mock_conn = Mock(spec=minio.Minio)
        expected_url = "http://127.0.0.1:9000/test_bucket/test/path/file.txt?X-Amz-Expires=86400"
        mock_conn.get_presigned_url.return_value = expected_url
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        mpath = "test/path/file.txt"
        timeout = 1  # 1天
        
        result = self.minio_dao.generate_url(mpath, timeout)
        
        assert result == expected_url
        mock_conn.get_presigned_url.assert_called_once_with(
            "GET",
            bucket_name="test_bucket",
            object_name=mpath,
            expires=timedelta(days=timeout)
        )

    def test_generate_url_failure(self):
        """测试生成URL失败"""
        # 模拟 Minio 连接抛出异常
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.get_presigned_url.side_effect = Exception("URL generation failed")
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        mpath = "test/path/file.txt"
        timeout = 1
        
        with patch('logging.error') as mock_log_error:
            result = self.minio_dao.generate_url(mpath, timeout)
            
            assert result == ""
            mock_log_error.assert_called_once()

    def test_delete_by_key_success(self):
        """测试删除文件成功"""
        # 模拟 Minio 连接
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.remove_object.return_value = None
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        mpath = "test/path/file_to_delete.txt"
        
        result = self.minio_dao.delete_by_key(mpath)
        
        assert result is True
        mock_conn.remove_object.assert_called_once_with(
            bucket_name="test_bucket",
            object_name=mpath
        )

    def test_delete_by_key_failure(self):
        """测试删除文件失败"""
        # 模拟 Minio 连接抛出异常
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.remove_object.side_effect = Exception("Delete failed")
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        mpath = "test/path/file_to_delete.txt"
        
        with patch('logging.exception') as mock_log_exception:
            result = self.minio_dao.delete_by_key(mpath)
            
            assert result is False
            mock_log_exception.assert_called_once()

    def test_empty_data_handling(self):
        """测试空数据处理"""
        # 模拟 Minio 连接
        mock_conn = Mock(spec=minio.Minio)
        mock_conn.put_object.return_value = None
        
        self.minio_dao.conn = mock_conn
        self.minio_dao.bucket = "test_bucket"
        
        # 测试空字节数据
        empty_data = b""
        mpath = "test/path/empty_file.txt"
        
        result = self.minio_dao.upload_from_bytes(mpath, empty_data)
        
        assert result is True
        mock_conn.put_object.assert_called_once()
        call_args = mock_conn.put_object.call_args
        assert call_args[1]['length'] == 0

    def test_unicode_text_handling(self):
        """测试Unicode文本处理"""
        # 模拟 upload_from_bytes 方法
        with patch.object(self.minio_dao, 'upload_from_bytes', return_value=True) as mock_upload:
            # 包含中文、emoji等Unicode字符的文本
            unicode_text = "测试文本 🚀 Test text with émojis"
            mpath = "test/path/unicode_file.txt"
            
            result = self.minio_dao.upload_from_text(mpath, unicode_text)
            
            assert result is True
            expected_bytes = unicode_text.encode("utf-8")
            mock_upload.assert_called_once_with(mpath, expected_bytes)

    def test_download_to_file_simulation(self):
        """测试下载到文件模拟"""
        # 模拟下载场景
        download_scenarios = [
            {"file_size": "1KB", "chunks": 1},
            {"file_size": "1MB", "chunks": 32},
            {"file_size": "10MB", "chunks": 320}
        ]

        # 验证场景结构
        for scenario in download_scenarios:
            assert "file_size" in scenario
            assert "chunks" in scenario
            assert scenario["chunks"] > 0

    def test_url_generation_simulation(self):
        """测试 URL 生成模拟"""
        # 模拟 URL 生成场景
        url_scenarios = [
            {"timeout": 1, "url_type": "short_term"},
            {"timeout": 7, "url_type": "weekly"},
            {"timeout": 30, "url_type": "monthly"}
        ]

        # 验证场景结构
        for scenario in url_scenarios:
            assert "timeout" in scenario
            assert "url_type" in scenario
            assert scenario["timeout"] > 0

    def test_object_deletion_simulation(self):
        """测试对象删除模拟"""
        # 模拟删除场景
        deletion_scenarios = [
            {"object_type": "file", "expected_result": True},
            {"object_type": "folder", "expected_result": True},
            {"object_type": "nonexistent", "expected_result": False}
        ]

        # 验证场景结构
        for scenario in deletion_scenarios:
            assert "object_type" in scenario
            assert "expected_result" in scenario
            assert isinstance(scenario["expected_result"], bool)

    def test_minio_tool_comprehensive_workflow(self):
        """测试 MinIO 工具综合工作流程"""
        # 模拟完整的 MinIO 工作流程
        workflow_steps = [
            "初始化连接",
            "上传字节数据",
            "上传文本数据",
            "上传文件",
            "下载到文件",
            "生成预签名 URL",
            "删除对象"
        ]

        # 验证工作流程步骤
        for step in workflow_steps:
            assert isinstance(step, str)
            assert len(step) > 0

        # 验证工作流程完整性
        assert len(workflow_steps) == 7
