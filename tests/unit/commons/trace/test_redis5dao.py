import pytest
import json
import time
from unittest.mock import patch, <PERSON><PERSON>, AsyncMock, MagicMock
import redis
from redis import asyncio as aredis
from redis.cluster import ClusterNode
from fastapi import status

from commons.trace.redis5dao import TraceRedis5Dao, CommonData, CommonResp, RespCode


class TestTraceRedis5Dao:
    """Test cases for TraceRedis5Dao class"""
    
    def setup_method(self):
        """Reset singleton before each test"""
        if hasattr(TraceRedis5Dao, '_instances'):
            TraceRedis5Dao._instances.clear()
    
    def teardown_method(self):
        """Clean up after each test"""
        if hasattr(TraceRedis5Dao, '_instances'):
            TraceRedis5Dao._instances.clear()
    
    def test_singleton_behavior(self):
        """Test singleton pattern"""
        dao1 = TraceRedis5Dao()
        dao2 = TraceRedis5Dao()
        
        assert dao1 is dao2
        assert id(dao1) == id(dao2)
    
    def test_init_default_state(self):
        """Test initial default state"""
        dao = TraceRedis5Dao()
        
        assert dao._hosts == []
        assert dao._password is None
        assert dao.rd is None
        assert dao.ard is None
        assert dao._prefix == ""
        assert dao._cluster is True
        assert dao._cams_ak is None
        assert dao._cams_sk is None
        assert dao._cams_host is None
        assert dao.is_init is False
    
    def test_init_cluster_mode(self):
        """Test initialization in cluster mode"""
        dao = TraceRedis5Dao()
        
        with patch.object(dao, '_connect') as mock_connect:
            mock_redis_cluster = Mock()
            mock_connect.return_value = mock_redis_cluster
            
            dao.init(["127.0.0.1:6379", "127.0.0.1:6380"], "password123", "trace:", True, 30)
            
            assert dao._hosts == ["127.0.0.1:6379", "127.0.0.1:6380"]
            assert dao._password == "password123"
            assert dao._prefix == "trace:"
            assert dao._cluster is True
            assert dao.rd is mock_redis_cluster
            assert dao.is_init is True
            mock_connect.assert_called_once()
    
    def test_init_single_mode(self):
        """Test initialization in single mode"""
        dao = TraceRedis5Dao()
        
        with patch.object(dao, '_connect') as mock_connect:
            mock_redis_single = Mock()
            mock_connect.return_value = mock_redis_single
            
            dao.init(["*************:6379"], "mypass", "app:", False, 60)
            
            assert dao._hosts == ["*************:6379"]
            assert dao._password == "mypass"
            assert dao._prefix == "app:"
            assert dao._cluster is False
            assert dao.rd is mock_redis_single
            assert dao.is_init is True
    
    def test_connect_cluster_mode(self):
        """Test _connect method in cluster mode"""
        dao = TraceRedis5Dao()
        dao._hosts = ["node1:6379", "[::1]:6379", "node3:6380"]
        dao._password = "clusterpass"
        dao._cluster = True
        
        with patch('redis.RedisCluster') as mock_redis_cluster:
            mock_cluster_instance = Mock()
            mock_redis_cluster.return_value = mock_cluster_instance
            
            result = dao._connect()
            
            assert result is mock_cluster_instance
            # Check that ClusterNode objects were created correctly
            call_args = mock_redis_cluster.call_args
            startup_nodes = call_args[1]['startup_nodes']
            assert len(startup_nodes) == 3
            assert all(isinstance(node, ClusterNode) for node in startup_nodes)
            
            # Verify IPv6 handling
            hosts = [node.host for node in startup_nodes]
            assert "node1" in hosts
            assert "::1" in hosts  # IPv6 brackets should be removed
            assert "node3" in hosts
            
            mock_redis_cluster.assert_called_once_with(
                startup_nodes=startup_nodes, 
                password="clusterpass"
            )
    
    def test_connect_single_mode(self):
        """Test _connect method in single mode"""
        dao = TraceRedis5Dao()
        dao._hosts = ["single.redis.com:6379"]
        dao._password = "singlepass"
        dao._cluster = False
        
        with patch('redis.ConnectionPool') as mock_pool, \
             patch('redis.Redis') as mock_redis:
            
            mock_pool_instance = Mock()
            mock_redis_instance = Mock()
            mock_pool.return_value = mock_pool_instance
            mock_redis.return_value = mock_redis_instance
            
            result = dao._connect()
            
            assert result is mock_redis_instance
            mock_pool.assert_called_once_with(
                host="single.redis.com", 
                port=6379, 
                password="singlepass"
            )
            mock_redis.assert_called_once_with(connection_pool=mock_pool_instance)
    
    @pytest.mark.asyncio
    async def test_aconnect_cluster_mode(self):
        """Test _aconnect method in cluster mode"""
        dao = TraceRedis5Dao()
        dao._hosts = ["async1:6379", "async2:6380"]
        dao._password = "asyncpass"
        dao._cluster = True
        
        with patch('redis.asyncio.RedisCluster') as mock_aredis_cluster:
            # Return a coroutine function that resolves to the mock instance
            async def mock_cluster_creation(*args, **kwargs):
                return AsyncMock()
            
            mock_aredis_cluster.side_effect = mock_cluster_creation
            
            result = await dao._aconnect()
            
            assert result is not None
            mock_aredis_cluster.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_aconnect_single_mode(self):
        """Test _aconnect method in single mode"""
        dao = TraceRedis5Dao()
        dao._hosts = ["async.single:6379"]
        dao._password = "asyncsingle"
        dao._cluster = False
        
        with patch('redis.asyncio.ConnectionPool') as mock_apool, \
             patch('redis.asyncio.Redis') as mock_aredis:
            
            mock_pool_instance = Mock()
            
            # Return a coroutine function for async Redis creation
            async def mock_redis_creation(*args, **kwargs):
                return AsyncMock()
            
            mock_apool.return_value = mock_pool_instance
            mock_aredis.side_effect = mock_redis_creation
            
            result = await dao._aconnect()
            
            assert result is not None
            mock_apool.assert_called_once_with(
                host="async.single", 
                port=6379, 
                password="asyncsingle"
            )
    
    @pytest.mark.asyncio
    async def test_close_both_connections(self):
        """Test close method with both sync and async connections"""
        dao = TraceRedis5Dao()
        dao._cluster = True
        
        # Mock connections
        mock_rd = Mock()
        mock_ard = AsyncMock()
        dao.rd = mock_rd
        dao.ard = mock_ard
        
        await dao.close()
        
        mock_rd.close.assert_called_once()
        mock_ard.aclose.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_single_async_connection(self):
        """Test close method with single async connection"""
        dao = TraceRedis5Dao()
        dao._cluster = False
        
        mock_ard = AsyncMock()
        dao.ard = mock_ard
        dao.rd = None
        
        await dao.close()
        
        mock_ard.aclose.assert_called_once_with(True)
    
    def test_init_cams(self):
        """Test CAMS initialization"""
        dao = TraceRedis5Dao()
        
        dao.init_cams("test_ak", "test_sk", "http://cams.host", "cams_prefix:")
        
        assert dao._cams_ak == "test_ak"
        assert dao._cams_sk == "test_sk"
        assert dao._cams_host == "http://cams.host"
        assert dao._prefix == "cams_prefix:"
    
    def test_sig_cams(self):
        """Test CAMS signature generation"""
        dao = TraceRedis5Dao()
        dao._cams_ak = "ak123"
        dao._cams_sk = "sk456"
        
        with patch('commons.trace.redis5dao.sig_wps4') as mock_sig, \
             patch('time.strftime') as mock_strftime:
            
            mock_strftime.return_value = "Mon, 01 Jan 2024 12:00:00 GMT"
            mock_sig.return_value = {"Authorization": "signature"}
            
            body = {"key": "test", "value": "data"}
            result = dao._sig_cams("POST", "/api/test", body)
            
            assert result == {"Authorization": "signature"}
            mock_sig.assert_called_once_with(
                "POST", "/api/test", 
                json.dumps(body).encode("utf-8"),
                "ak123", "sk456", 
                "Mon, 01 Jan 2024 12:00:00 GMT"
            )
    
    def test_call_cams_success(self):
        """Test successful CAMS API call"""
        dao = TraceRedis5Dao()
        dao._cams_host = "http://cams.test"
        
        with patch.object(dao, '_sig_cams') as mock_sig, \
             patch('requests.post') as mock_post:
            
            mock_sig.return_value = {"Authorization": "Bearer token"}
            
            mock_response = Mock()
            mock_response.status_code = status.HTTP_200_OK
            mock_response.text = '{"code": 0, "msg": "success", "data": {"val": "result"}}'
            mock_post.return_value = mock_response
            
            body = {"operation": "set", "key": "test"}
            result = dao._call_cams("/api/cache", body)
            
            assert isinstance(result, CommonResp)
            assert result.code == RespCode.OK
            assert result.msg == "success"
            
            mock_post.assert_called_once_with(
                "http://cams.test/api/cache",
                json=body,
                headers={"Authorization": "Bearer token"}
            )
    
    def test_call_cams_http_error(self):
        """Test CAMS API call with HTTP error"""
        dao = TraceRedis5Dao()
        dao._cams_host = "http://cams.test"
        
        with patch.object(dao, '_sig_cams') as mock_sig, \
             patch('requests.post') as mock_post, \
             patch('logging.error') as mock_log:
            
            mock_sig.return_value = {"Authorization": "Bearer token"}
            
            mock_response = Mock()
            mock_response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            mock_response.text = "Internal Server Error"
            mock_post.return_value = mock_response
            
            with pytest.raises(Exception, match="cams redis rpc request fail"):
                dao._call_cams("/api/error", {})
            
            mock_log.assert_called_once()
    
    def test_call_cams_response_error(self):
        """Test CAMS API call with response error code"""
        dao = TraceRedis5Dao()
        dao._cams_host = "http://cams.test"
        
        with patch.object(dao, '_sig_cams') as mock_sig, \
             patch('requests.post') as mock_post, \
             patch('logging.error') as mock_log:
            
            mock_sig.return_value = {"Authorization": "Bearer token"}
            
            mock_response = Mock()
            mock_response.status_code = status.HTTP_200_OK
            mock_response.text = '{"code": 1, "msg": "error", "data": null}'
            mock_post.return_value = mock_response
            
            with pytest.raises(Exception, match="cams redis rpc request fail"):
                dao._call_cams("/api/error", {})
            
            mock_log.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_acall_cams_success(self):
        """Test successful async CAMS API call"""
        dao = TraceRedis5Dao()
        dao._cams_host = "http://async.cams.test"
        
        with patch.object(dao, '_sig_cams') as mock_sig, \
             patch('aiohttp.ClientSession') as mock_session_class:
            
            mock_sig.return_value = {"Authorization": "Bearer async_token"}
            
            # Mock aiohttp response
            mock_response = Mock()
            mock_response.status = status.HTTP_200_OK
            mock_response.text = AsyncMock(return_value='{"code": 0, "msg": "async success", "data": {"val": "async_result"}}')
            
            # Mock session context manager
            mock_session = Mock()
            mock_post_context = Mock()
            mock_post_context.__aenter__ = AsyncMock(return_value=mock_response)
            mock_post_context.__aexit__ = AsyncMock(return_value=None)
            mock_session.post.return_value = mock_post_context
            
            # Mock session context manager
            mock_session_context = Mock()
            mock_session_context.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session_context.__aexit__ = AsyncMock(return_value=None)
            mock_session_class.return_value = mock_session_context
            
            body = {"async_operation": "get", "key": "async_test"}
            result = await dao._acall_cams("/api/async", body)
            
            assert isinstance(result, CommonResp)
            assert result.code == RespCode.OK
            assert result.msg == "async success"
    
    def test_set_redis_mode(self):
        """Test set operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "test:"
        dao._cams_host = None  # Redis mode
        dao._time_out = 3600  # 设置timeout值
        
        mock_redis = Mock()
        dao.rd = mock_redis
        
        with patch.object(dao, '_set') as mock_set:
            mock_set.return_value = True
            
            result = dao.set("mykey", "myvalue", nx=True)
            
            assert result is True
            mock_set.assert_called_once_with("mykey", "myvalue", 3600, True)
    
    def test_set_redis_mode_with_connection_error(self):
        """Test set operation with connection error and retry"""
        dao = TraceRedis5Dao()
        dao._prefix = "test:"
        dao._cams_host = None
        
        mock_redis = Mock()
        dao.rd = mock_redis
        
        with patch.object(dao, '_set') as mock_set, \
             patch.object(dao, '_connect') as mock_connect, \
             patch('logging.warning') as mock_warning:
            
            # First call raises ConnectionError, second succeeds
            mock_set.side_effect = [ConnectionError("Connection lost"), True]
            mock_new_redis = Mock()
            mock_connect.return_value = mock_new_redis
            
            result = dao.set("retry_key", "retry_value")
            
            assert result is True
            assert dao.rd is mock_new_redis
            assert mock_set.call_count == 2
            mock_warning.assert_called_once()
    
    def test_set_redis_mode_with_persistent_error(self):
        """Test set operation with persistent error"""
        dao = TraceRedis5Dao()
        dao._prefix = "test:"
        dao._cams_host = None
        
        mock_redis = Mock()
        dao.rd = mock_redis
        
        with patch.object(dao, '_set') as mock_set, \
             patch.object(dao, '_connect') as mock_connect, \
             patch('logging.warning'), \
             patch('logging.exception') as mock_exception:
            
            # Both calls raise exceptions
            mock_set.side_effect = [ConnectionError("Connection lost"), ValueError("Persistent error")]
            mock_connect.return_value = Mock()
            
            with pytest.raises(ValueError, match="Persistent error"):
                dao.set("error_key", "error_value")
            
            mock_exception.assert_called_once()
    
    def test_set_cams_mode(self):
        """Test set operation in CAMS mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams:"
        dao._cams_host = "http://cams.server"
        dao._time_out = 7200  # 设置timeout值
        
        with patch.object(dao, '_set_cams') as mock_set_cams:
            mock_set_cams.return_value = True
            
            result = dao.set("cams_key", "cams_value")
            
            assert result is True
            mock_set_cams.assert_called_once_with("cams_key", "cams_value", 7200)
    
    @pytest.mark.asyncio
    async def test_aset_redis_mode(self):
        """Test async set operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "async:"
        dao._cams_host = None
        
        mock_aredis = AsyncMock()
        dao.ard = mock_aredis
        
        with patch.object(dao, '_aset') as mock_aset:
            mock_aset.return_value = True
            
            result = await dao.aset("async_key", "async_value", ex=1800)
            
            assert result is True
            mock_aset.assert_called_once_with("async_key", "async_value", 1800, False)
    
    def test_get_redis_mode(self):
        """Test get operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "get_test:"
        dao._cams_host = None
        
        mock_redis = Mock()
        dao.rd = mock_redis
        
        with patch.object(dao, '_get') as mock_get:
            mock_get.return_value = b"redis_value"
            
            result = dao.get("test_key", "default")
            
            assert result == b"redis_value"
            mock_get.assert_called_once_with("test_key", "default")
    
    def test_get_cams_mode(self):
        """Test get operation in CAMS mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_get:"
        dao._cams_host = "http://cams.get"
        
        with patch.object(dao, '_get_cams') as mock_get_cams:
            mock_get_cams.return_value = "cams_result"
            
            result = dao.get("cams_key", "default_value")
            
            assert result == "cams_result"
            mock_get_cams.assert_called_once_with("cams_key", "default_value")
    
    def test_getstring_with_bytes(self):
        """Test getstring method with bytes value"""
        dao = TraceRedis5Dao()
        
        with patch.object(dao, 'get') as mock_get:
            mock_get.return_value = b"byte_string_value"
            
            result = dao.getstring("string_key", "default")
            
            assert result == "byte_string_value"
            mock_get.assert_called_once_with("string_key", "default")
    
    def test_getstring_with_string(self):
        """Test getstring method with string value"""
        dao = TraceRedis5Dao()
        
        with patch.object(dao, 'get') as mock_get:
            mock_get.return_value = "already_string"
            
            result = dao.getstring("string_key2", "default")
            
            assert result == "already_string"
    
    def test_getint(self):
        """Test getint method"""
        dao = TraceRedis5Dao()
        
        with patch.object(dao, 'get') as mock_get:
            mock_get.return_value = "42"
            
            result = dao.getint("int_key", 0)
            
            assert result == 42
            assert isinstance(result, int)
    
    def test_getbool(self):
        """Test getbool method"""
        dao = TraceRedis5Dao()
        
        with patch.object(dao, 'get') as mock_get:
            mock_get.return_value = "1"
            
            result = dao.getbool("bool_key", False)
            
            assert result is True
    
    def test_remove_redis_mode(self):
        """Test remove operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "remove:"
        dao._cams_host = None
        
        with patch.object(dao, '_remove') as mock_remove:
            mock_remove.return_value = 1
            
            result = dao.remove("remove_key")
            
            assert result == 1
            mock_remove.assert_called_once_with("remove_key")
    
    def test_remove_cams_mode(self):
        """Test remove operation in CAMS mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_remove:"
        dao._cams_host = "http://cams.remove"
        
        with patch.object(dao, '_remove_cams') as mock_remove_cams:
            mock_remove_cams.return_value = True
            
            result = dao.remove("cams_remove_key")
            
            assert result is True
            mock_remove_cams.assert_called_once_with("cams_remove_key")
    
    def test_incrby_redis_mode(self):
        """Test incrby operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "incr:"
        dao._cams_host = None
        
        mock_redis = Mock()
        mock_redis.incrby.return_value = 5
        dao.rd = mock_redis
        
        result = dao.incrby("counter", 3)
        
        assert result == 5
        mock_redis.incrby.assert_called_once_with("incr:counter", 3)
    
    def test_incrby_cams_mode(self):
        """Test incrby operation in CAMS mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_incr:"
        dao._cams_host = "http://cams.incr"
        
        with patch.object(dao, '_get_cams') as mock_get, \
             patch.object(dao, '_set_cams') as mock_set:
            
            mock_get.return_value = "10"
            mock_set.return_value = True
            
            result = dao.incrby("cams_counter", 5)
            
            assert result == 15
            mock_get.assert_called_once_with("cams_counter", 0)
            mock_set.assert_called_once_with("cams_counter", 15)
    
    def test_expire_redis_mode(self):
        """Test expire operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "expire:"
        dao._cams_host = None
        
        mock_redis = Mock()
        mock_redis.expire.return_value = True
        dao.rd = mock_redis
        
        result = dao.expire("expire_key", 3600)
        
        assert result is True
        mock_redis.expire.assert_called_once_with("expire:expire_key", 3600)
    
    def test_expire_cams_mode(self):
        """Test expire operation in CAMS mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_expire:"
        dao._cams_host = "http://cams.expire"
        
        with patch.object(dao, '_get_cams') as mock_get, \
             patch.object(dao, '_set_cams') as mock_set:
            
            mock_get.return_value = "123"  # Return numeric string for int() conversion
            mock_set.return_value = True
            
            result = dao.expire("cams_expire_key", 7200)
            
            assert result is True
            mock_get.assert_called_once_with("cams_expire_key", 0)
            mock_set.assert_called_once_with("cams_expire_key", 123, ex=7200)
    
    def test_zadd_redis_mode(self):
        """Test zadd operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "zset:"
        dao._cams_host = None
        
        mock_redis = Mock()
        dao.rd = mock_redis
        
        mapping = {"member1": 1.0, "member2": 2.0}
        dao.zadd("sorted_set", mapping, nx=True)
        
        mock_redis.zadd.assert_called_once_with("zset:sorted_set", mapping=mapping, nx=True)
    
    @pytest.mark.asyncio
    async def test_azadd_redis_mode(self):
        """Test async zadd operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "azset:"
        dao._cams_host = None
        
        mock_aredis = AsyncMock()
        dao.ard = mock_aredis
        
        mapping = {"async_member": 3.5}
        await dao.azadd("async_sorted_set", mapping, nx=False)
        
        mock_aredis.zadd.assert_called_once_with("azset:async_sorted_set", mapping=mapping, nx=False)
    
    def test_zpopmin_redis_mode(self):
        """Test zpopmin operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "zpop:"
        dao._cams_host = None
        
        mock_redis = Mock()
        mock_redis.zpopmin.return_value = [(b"member1", 1.0), (b"member2", 2.0)]
        dao.rd = mock_redis
        
        result = dao.zpopmin("pop_set", 2)
        
        assert result == [(b"member1", 1.0), (b"member2", 2.0)]
        mock_redis.zpopmin.assert_called_once_with("zpop:pop_set", 2)
    
    def test_zrang_normal_order(self):
        """Test zrang operation with normal order"""
        dao = TraceRedis5Dao()
        dao._prefix = "zrange:"
        dao._cams_host = None
        
        mock_redis = Mock()
        mock_redis.zrange.return_value = [b"member1", b"member2"]
        dao.rd = mock_redis
        
        result = dao.zrang("range_set", 0, -1, withscores=False, rev=False)
        
        assert result == [b"member1", b"member2"]
        mock_redis.zrange.assert_called_once_with(
            name="zrange:range_set", start=0, end=-1, withscores=False
        )
    
    def test_zrang_reverse_order(self):
        """Test zrang operation with reverse order"""
        dao = TraceRedis5Dao()
        dao._prefix = "zrevrange:"
        dao._cams_host = None
        
        mock_redis = Mock()
        mock_redis.zrevrange.return_value = [(b"member2", 2.0), (b"member1", 1.0)]
        dao.rd = mock_redis
        
        result = dao.zrang("rev_set", 0, 1, withscores=True, rev=True)
        
        assert result == [(b"member2", 2.0), (b"member1", 1.0)]
        mock_redis.zrevrange.assert_called_once_with(
            name="zrevrange:rev_set", start=0, end=1, withscores=True
        )
    
    def test_zcard_redis_mode(self):
        """Test zcard operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "zcard:"
        dao._cams_host = None
        
        mock_redis = Mock()
        mock_redis.zcard.return_value = 5
        dao.rd = mock_redis
        
        result = dao.zcard("card_set")
        
        assert result == 5
        mock_redis.zcard.assert_called_once_with("zcard:card_set")


class TestCommonModels:
    """Test cases for Common data models"""
    
    def test_common_data_default(self):
        """Test CommonData with default values"""
        data = CommonData()
        assert data.val is None
    
    def test_common_data_with_value(self):
        """Test CommonData with value"""
        data = CommonData(val="test_value")
        assert data.val == "test_value"
    
    def test_common_resp_creation(self):
        """Test CommonResp creation"""
        resp = CommonResp(code=0, msg="success", data=CommonData(val="result"))
        
        assert resp.code == 0
        assert resp.msg == "success"
        assert resp.data.val == "result"
    
    def test_resp_code_enum(self):
        """Test RespCode enum"""
        assert RespCode.OK == 0
        assert int(RespCode.OK) == 0


class TestTraceRedis5DaoAdditionalCoverage:
    """Additional tests to improve coverage for TraceRedis5Dao"""
    
    def setup_method(self):
        """Reset singleton before each test"""
        if hasattr(TraceRedis5Dao, '_instances'):
            TraceRedis5Dao._instances.clear()
    
    def teardown_method(self):
        """Clean up after each test"""
        if hasattr(TraceRedis5Dao, '_instances'):
            TraceRedis5Dao._instances.clear()
    
    def test_set_private_methods(self):
        """Test _set method directly"""
        dao = TraceRedis5Dao()
        dao._prefix = "test_prefix:"
        
        mock_redis = Mock()
        mock_redis.set.return_value = True
        dao.rd = mock_redis
        
        result = dao._set("test_key", "test_value", ex=3600, nx=True)
        
        assert result is True
        mock_redis.set.assert_called_once_with("test_prefix:test_key", "test_value", ex=3600, nx=True)
    
    @pytest.mark.asyncio
    async def test_aset_private_methods(self):
        """Test _aset method directly"""
        dao = TraceRedis5Dao()
        dao._prefix = "async_prefix:"
        
        mock_aredis = AsyncMock()
        mock_aredis.set.return_value = True
        dao.ard = mock_aredis
        
        result = await dao._aset("async_key", "async_value", ex=1800, nx=False)
        
        assert result is True
        mock_aredis.set.assert_called_once_with("async_prefix:async_key", "async_value", ex=1800, nx=False)
    
    def test_set_cams_success(self):
        """Test _set_cams method success"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_set:"
        
        with patch.object(dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_call.return_value = mock_resp
            
            result = dao._set_cams("test_key", "test_value", ex=7200)
            
            assert result is True
            mock_call.assert_called_once_with("/sdk/api/v1/cache/set", {
                "key": "cams_set:test_key",
                "val": "test_value",
                "expire": 7200
            })
    
    def test_set_cams_failure(self):
        """Test _set_cams method failure"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_fail:"
        
        with patch.object(dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = 1  # Not OK
            mock_call.return_value = mock_resp
            
            result = dao._set_cams("fail_key", "fail_value")
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_aset_cams_success(self):
        """Test _aset_cams method success"""
        dao = TraceRedis5Dao()
        dao._prefix = "async_cams:"
        
        with patch.object(dao, '_acall_cams') as mock_acall:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_acall.return_value = mock_resp
            
            result = await dao._aset_cams("async_key", "async_value", ex=3600)
            
            assert result is True
            mock_acall.assert_called_once_with("/sdk/api/v1/cache/set", {
                "key": "async_cams:async_key",
                "val": "async_value",
                "expire": 3600
            })
    
    @pytest.mark.asyncio
    async def test_aset_redis_mode_with_connection_error(self):
        """Test async set with connection error and retry"""
        dao = TraceRedis5Dao()
        dao._prefix = "aset_test:"
        dao._cams_host = None
        
        with patch.object(dao, '_aset') as mock_aset, \
             patch.object(dao, '_aconnect') as mock_aconnect, \
             patch('logging.warning') as mock_warning:
            
            # First call raises ConnectionError, second succeeds
            mock_aset.side_effect = [ConnectionError("Async connection lost"), True]
            mock_new_aredis = AsyncMock()
            mock_aconnect.return_value = mock_new_aredis
            
            result = await dao.aset("retry_key", "retry_value")
            
            assert result is True
            assert dao.ard is mock_new_aredis
            assert mock_aset.call_count == 2
            mock_warning.assert_called_once()
    
    def test_get_private_methods(self):
        """Test _get method directly"""
        dao = TraceRedis5Dao()
        dao._prefix = "get_prefix:"
        
        mock_redis = Mock()
        mock_redis.get.return_value = b"retrieved_value"
        dao.rd = mock_redis
        
        result = dao._get("get_key", "default_value")
        
        assert result == b"retrieved_value"
        mock_redis.get.assert_called_once_with("get_prefix:get_key")
    
    def test_get_private_methods_with_default(self):
        """Test _get method with default value"""
        dao = TraceRedis5Dao()
        dao._prefix = "get_default:"
        
        mock_redis = Mock()
        mock_redis.get.return_value = None
        dao.rd = mock_redis
        
        result = dao._get("missing_key", "default_value")
        
        assert result == "default_value"
    
    @pytest.mark.asyncio
    async def test_aget_private_methods(self):
        """Test _aget method directly"""
        dao = TraceRedis5Dao()
        dao._prefix = "aget_prefix:"
        
        mock_aredis = AsyncMock()
        mock_aredis.get.return_value = b"async_retrieved"
        dao.ard = mock_aredis
        
        result = await dao._aget("aget_key", "async_default")
        
        assert result == b"async_retrieved"
        mock_aredis.get.assert_called_once_with("aget_prefix:aget_key")
    
    def test_get_cams_success(self):
        """Test _get_cams method success"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_get:"
        
        with patch.object(dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_resp.data = Mock()
            mock_resp.data.val = "cams_retrieved_value"
            mock_call.return_value = mock_resp
            
            result = dao._get_cams("cams_key", "cams_default")
            
            assert result == "cams_retrieved_value"
            mock_call.assert_called_once_with("/sdk/api/v1/cache/get", {
                "key": "cams_get:cams_key"
            })
    
    def test_get_cams_no_data(self):
        """Test _get_cams method with no data"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_no_data:"
        
        with patch.object(dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_resp.data = None
            mock_call.return_value = mock_resp
            
            result = dao._get_cams("no_data_key", "default")
            
            assert result == "default"
    
    @pytest.mark.asyncio
    async def test_aget_cams_success(self):
        """Test _aget_cams method success"""
        dao = TraceRedis5Dao()
        dao._prefix = "async_cams_get:"
        
        with patch.object(dao, '_acall_cams') as mock_acall:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_resp.data = Mock()
            mock_resp.data.val = "async_cams_value"
            mock_acall.return_value = mock_resp
            
            result = await dao._aget_cams("async_cams_key", "async_default")
            
            assert result == "async_cams_value"
            mock_acall.assert_called_once_with("/sdk/api/v1/cache/get", {
                "key": "async_cams_get:async_cams_key"
            })
    
    @pytest.mark.asyncio
    async def test_agetint(self):
        """Test agetint method"""
        dao = TraceRedis5Dao()
        
        with patch.object(dao, 'aget') as mock_aget:
            mock_aget.return_value = "456"
            
            result = await dao.agetint("int_key", 0)
            
            assert result == 456
            assert isinstance(result, int)
    
    @pytest.mark.asyncio
    async def test_agetstring_with_bytes(self):
        """Test agetstring method with bytes value"""
        dao = TraceRedis5Dao()
        
        with patch.object(dao, 'aget') as mock_aget:
            mock_aget.return_value = b"async_byte_string"
            
            result = await dao.agetstring("string_key", "default")
            
            assert result == "async_byte_string"
    
    @pytest.mark.asyncio
    async def test_agetstring_with_string(self):
        """Test agetstring method with string value"""
        dao = TraceRedis5Dao()
        
        with patch.object(dao, 'aget') as mock_aget:
            mock_aget.return_value = "already_async_string"
            
            result = await dao.agetstring("string_key2", "default")
            
            assert result == "already_async_string"
    
    @pytest.mark.asyncio
    async def test_agetbool(self):
        """Test agetbool method"""
        dao = TraceRedis5Dao()
        
        with patch.object(dao, 'aget') as mock_aget:
            mock_aget.return_value = "1"
            
            result = await dao.agetbool("bool_key", False)
            
            assert result is True
    
    def test_remove_private_methods(self):
        """Test _remove method directly"""
        dao = TraceRedis5Dao()
        dao._prefix = "remove_prefix:"
        
        mock_redis = Mock()
        mock_redis.delete.return_value = 1
        dao.rd = mock_redis
        
        result = dao._remove("remove_key")
        
        assert result == 1
        mock_redis.delete.assert_called_once_with("remove_prefix:remove_key")
    
    @pytest.mark.asyncio
    async def test_aremove_private_methods(self):
        """Test _aremove method directly"""
        dao = TraceRedis5Dao()
        dao._prefix = "aremove_prefix:"
        
        mock_aredis = AsyncMock()
        mock_aredis.delete.return_value = 1
        dao.ard = mock_aredis
        
        result = await dao._aremove("aremove_key")
        
        assert result == 1
        mock_aredis.delete.assert_called_once_with("aremove_prefix:aremove_key")
    
    def test_remove_cams_success(self):
        """Test _remove_cams method success"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_remove:"
        
        with patch.object(dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_call.return_value = mock_resp
            
            result = dao._remove_cams("cams_remove_key")
            
            assert result is True
            mock_call.assert_called_once_with("/sdk/api/v1/cache/del", {
                "key": "cams_remove:cams_remove_key"
            })
    
    def test_remove_cams_failure(self):
        """Test _remove_cams method failure"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_fail_remove:"
        
        with patch.object(dao, '_call_cams') as mock_call:
            mock_resp = Mock()
            mock_resp.code = 1  # Not OK
            mock_call.return_value = mock_resp
            
            result = dao._remove_cams("fail_remove_key")
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_aremove_cams_success(self):
        """Test _aremove_cams method success"""
        dao = TraceRedis5Dao()
        dao._prefix = "async_cams_remove:"
        
        with patch.object(dao, '_acall_cams') as mock_acall:
            mock_resp = Mock()
            mock_resp.code = RespCode.OK
            mock_acall.return_value = mock_resp
            
            result = await dao._aremove_cams("async_remove_key")
            
            assert result is True
            mock_acall.assert_called_once_with("/sdk/api/v1/cache/del", {
                "key": "async_cams_remove:async_remove_key"
            })
    
    @pytest.mark.asyncio
    async def test_aremove_redis_mode(self):
        """Test async remove in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "aredis_remove:"
        dao._cams_host = None
        
        with patch.object(dao, '_aremove') as mock_aremove:
            mock_aremove.return_value = 1
            
            result = await dao.aremove("redis_remove_key")
            
            assert result == 1
            mock_aremove.assert_called_once_with("redis_remove_key")
    
    @pytest.mark.asyncio
    async def test_aremove_cams_mode(self):
        """Test async remove in CAMS mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "aredis_cams:"
        dao._cams_host = "http://cams.remove"
        
        with patch.object(dao, '_aremove_cams') as mock_aremove_cams:
            mock_aremove_cams.return_value = True
            
            result = await dao.aremove("cams_remove_key")
            
            assert result is True
            mock_aremove_cams.assert_called_once_with("cams_remove_key")
    
    def test_incrby_cams_mode_failure(self):
        """Test incrby CAMS mode when set fails"""
        dao = TraceRedis5Dao()
        dao._prefix = "cams_incr:"
        dao._cams_host = "http://cams.incr"
        
        with patch.object(dao, '_get_cams') as mock_get, \
             patch.object(dao, '_set_cams') as mock_set:
            
            mock_get.return_value = "5"
            mock_set.return_value = False  # Set fails
            
            result = dao.incrby("incr_key", 3)
            
            assert result == 0  # Should return 0 on failure
    
    @pytest.mark.asyncio
    async def test_aincrby_redis_mode(self):
        """Test async incrby in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "aincrby:"
        dao._cams_host = None
        
        mock_aredis = AsyncMock()
        mock_aredis.incrby.return_value = 15
        dao.ard = mock_aredis
        
        result = await dao.aincrby("async_counter", 5)
        
        assert result == 15
        mock_aredis.incrby.assert_called_once_with("aincrby:async_counter", 5)
    
    @pytest.mark.asyncio
    async def test_aincrby_cams_mode(self):
        """Test async incrby in CAMS mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "async_cams_incr:"
        dao._cams_host = "http://async.cams.incr"
        
        with patch.object(dao, '_aget_cams') as mock_aget, \
             patch.object(dao, '_aset_cams') as mock_aset:
            
            mock_aget.return_value = "20"
            mock_aset.return_value = True
            
            result = await dao.aincrby("async_cams_counter", 10)
            
            assert result == 30
            mock_aget.assert_called_once_with("async_cams_counter", 0)
            mock_aset.assert_called_once_with("async_cams_counter", 30)
    
    @pytest.mark.asyncio
    async def test_aincrby_cams_mode_failure(self):
        """Test async incrby CAMS mode when set fails"""
        dao = TraceRedis5Dao()
        dao._prefix = "async_cams_fail:"
        dao._cams_host = "http://async.cams.fail"
        
        with patch.object(dao, '_aget_cams') as mock_aget, \
             patch.object(dao, '_aset_cams') as mock_aset:
            
            mock_aget.return_value = "10"
            mock_aset.return_value = False  # Set fails
            
            result = await dao.aincrby("fail_counter", 5)
            
            assert result == 0  # Should return 0 on failure
    
    @pytest.mark.asyncio
    async def test_aexpire_redis_mode(self):
        """Test async expire in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "aexpire:"
        dao._cams_host = None
        
        mock_aredis = AsyncMock()
        mock_aredis.expire.return_value = True
        dao.ard = mock_aredis
        
        result = await dao.aexpire("expire_key", 1800)
        
        assert result is True
        mock_aredis.expire.assert_called_once_with("aexpire:expire_key", 1800)
    
    @pytest.mark.asyncio
    async def test_aexpire_cams_mode(self):
        """Test async expire in CAMS mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "async_cams_expire:"
        dao._cams_host = "http://async.cams.expire"
        
        with patch.object(dao, '_aget_cams') as mock_aget, \
             patch.object(dao, '_aset_cams') as mock_aset:
            
            mock_aget.return_value = "100"
            mock_aset.return_value = True
            
            result = await dao.aexpire("async_expire_key", 3600)
            
            assert result is True
            mock_aget.assert_called_once_with("async_expire_key", 0)
            mock_aset.assert_called_once_with("async_expire_key", 100, ex=3600)
    
    def test_zrem_redis_mode(self):
        """Test zrem operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "zrem:"
        dao._cams_host = None
        
        mock_redis = Mock()
        dao.rd = mock_redis
        
        dao.zrem("sorted_set", "member_to_remove")
        
        mock_redis.zrem.assert_called_once_with("zrem:sorted_set", "member_to_remove")
    
    @pytest.mark.asyncio
    async def test_azrem_redis_mode(self):
        """Test async zrem operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "azrem:"
        dao._cams_host = None
        
        mock_aredis = AsyncMock()
        dao.ard = mock_aredis
        
        await dao.azrem("async_sorted_set", "async_member_to_remove")
        
        mock_aredis.zrem.assert_called_once_with("azrem:async_sorted_set", "async_member_to_remove")
    
    @pytest.mark.asyncio
    async def test_azpopmin_redis_mode(self):
        """Test async zpopmin operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "azpopmin:"
        dao._cams_host = None
        
        mock_aredis = AsyncMock()
        mock_aredis.zpopmin.return_value = [(b"async_member1", 1.5), (b"async_member2", 2.5)]
        dao.ard = mock_aredis
        
        result = await dao.azpopmin("async_pop_set", 2)
        
        assert result == [(b"async_member1", 1.5), (b"async_member2", 2.5)]
        mock_aredis.zpopmin.assert_called_once_with("azpopmin:async_pop_set", 2)
    
    @pytest.mark.asyncio
    async def test_azrang_normal_order(self):
        """Test async zrang operation with normal order"""
        dao = TraceRedis5Dao()
        dao._prefix = "azrange:"
        dao._cams_host = None
        
        mock_aredis = AsyncMock()
        mock_aredis.zrange.return_value = [b"async_member1", b"async_member2"]
        dao.ard = mock_aredis
        
        result = await dao.azrang("async_range_set", 0, -1, withscores=False, rev=False)
        
        assert result == [b"async_member1", b"async_member2"]
        mock_aredis.zrange.assert_called_once_with(
            name="azrange:async_range_set", start=0, end=-1, withscores=False
        )
    
    @pytest.mark.asyncio
    async def test_azrang_reverse_order(self):
        """Test async zrang operation with reverse order"""
        dao = TraceRedis5Dao()
        dao._prefix = "azrevrange:"
        dao._cams_host = None
        
        mock_aredis = AsyncMock()
        mock_aredis.zrevrange.return_value = [(b"async_member2", 2.0), (b"async_member1", 1.0)]
        dao.ard = mock_aredis
        
        result = await dao.azrang("async_rev_set", 0, 1, withscores=True, rev=True)
        
        assert result == [(b"async_member2", 2.0), (b"async_member1", 1.0)]
        mock_aredis.zrevrange.assert_called_once_with(
            name="azrevrange:async_rev_set", start=0, end=1, withscores=True
        )
    
    @pytest.mark.asyncio
    async def test_azcard_redis_mode(self):
        """Test async zcard operation in Redis mode"""
        dao = TraceRedis5Dao()
        dao._prefix = "azcard:"
        dao._cams_host = None
        
        mock_aredis = AsyncMock()
        mock_aredis.zcard.return_value = 10
        dao.ard = mock_aredis
        
        result = await dao.azcard("async_card_set")
        
        assert result == 10
        mock_aredis.zcard.assert_called_once_with("azcard:async_card_set")
    
    @pytest.mark.asyncio
    async def test_acall_cams_http_error(self):
        """Test async CAMS API call with HTTP error"""
        dao = TraceRedis5Dao()
        dao._cams_host = "http://error.cams"
        
        with patch.object(dao, '_sig_cams') as mock_sig, \
             patch('aiohttp.ClientSession') as mock_session_class, \
             patch('logging.error') as mock_log_error:
            
            mock_sig.return_value = {"Authorization": "Bearer token"}
            
            # Mock HTTP error response
            mock_response = Mock()
            mock_response.status = status.HTTP_500_INTERNAL_SERVER_ERROR
            mock_response.text = AsyncMock(return_value="Internal Server Error")
            
            # Mock session context manager
            mock_session = Mock()
            mock_post_context = Mock()
            mock_post_context.__aenter__ = AsyncMock(return_value=mock_response)
            mock_post_context.__aexit__ = AsyncMock(return_value=None)
            mock_session.post.return_value = mock_post_context
            
            mock_session_context = Mock()
            mock_session_context.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session_context.__aexit__ = AsyncMock(return_value=None)
            mock_session_class.return_value = mock_session_context
            
            with pytest.raises(Exception, match="cams redis rpc request fail"):
                await dao._acall_cams("/api/error", {})
            
            mock_log_error.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_acall_cams_response_error(self):
        """Test async CAMS API call with response error code"""
        dao = TraceRedis5Dao()
        dao._cams_host = "http://resp_error.cams"
        
        with patch.object(dao, '_sig_cams') as mock_sig, \
             patch('aiohttp.ClientSession') as mock_session_class, \
             patch('logging.error') as mock_log_error:
            
            mock_sig.return_value = {"Authorization": "Bearer token"}
            
            # Mock response with error code
            mock_response = Mock()
            mock_response.status = status.HTTP_200_OK
            mock_response.text = AsyncMock(return_value='{"code": 1, "msg": "Response error"}')
            
            # Mock session context manager
            mock_session = Mock()
            mock_post_context = Mock()
            mock_post_context.__aenter__ = AsyncMock(return_value=mock_response)
            mock_post_context.__aexit__ = AsyncMock(return_value=None)
            mock_session.post.return_value = mock_post_context
            
            mock_session_context = Mock()
            mock_session_context.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session_context.__aexit__ = AsyncMock(return_value=None)
            mock_session_class.return_value = mock_session_context
            
            with pytest.raises(Exception, match="cams redis rpc request fail"):
                await dao._acall_cams("/api/resp_error", {})
            
            mock_log_error.assert_called_once()
    
    def test_set_redis_mode_with_other_exception(self):
        """Test set operation with non-connection exception"""
        dao = TraceRedis5Dao()
        dao._prefix = "exception_test:"
        dao._cams_host = None
        
        with patch.object(dao, '_set') as mock_set, \
             patch('logging.exception') as mock_exception:
            
            # First call raises a different exception
            mock_set.side_effect = ValueError("Non-connection error")
            
            with pytest.raises(ValueError, match="Non-connection error"):
                dao.set("exception_key", "exception_value")
            
            mock_exception.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_aset_redis_mode_with_other_exception(self):
        """Test async set operation with non-connection exception"""
        dao = TraceRedis5Dao()
        dao._prefix = "aexception_test:"
        dao._cams_host = None
        
        with patch.object(dao, '_aset') as mock_aset, \
             patch('logging.exception') as mock_exception:
            
            # Raises a different exception
            mock_aset.side_effect = ValueError("Async non-connection error")
            
            with pytest.raises(ValueError, match="Async non-connection error"):
                await dao.aset("async_exception_key", "async_exception_value")
            
            mock_exception.assert_called_once()
    
    def test_get_redis_mode_with_connection_error(self):
        """Test get operation with connection error and retry"""
        dao = TraceRedis5Dao()
        dao._prefix = "get_retry:"
        dao._cams_host = None
        
        with patch.object(dao, '_get') as mock_get, \
             patch.object(dao, '_connect') as mock_connect, \
             patch('logging.warning') as mock_warning:
            
            # First call raises ConnectionError, second succeeds
            mock_get.side_effect = [ConnectionError("Get connection lost"), b"retried_value"]
            mock_new_redis = Mock()
            mock_connect.return_value = mock_new_redis
            
            result = dao.get("retry_get_key", "default")
            
            assert result == b"retried_value"
            assert dao.rd is mock_new_redis
            assert mock_get.call_count == 2
            mock_warning.assert_called_once()
    
    def test_get_redis_mode_with_persistent_error(self):
        """Test get operation with persistent error"""
        dao = TraceRedis5Dao()
        dao._prefix = "get_error:"
        dao._cams_host = None
        
        with patch.object(dao, '_get') as mock_get, \
             patch.object(dao, '_connect') as mock_connect, \
             patch('logging.warning'), \
             patch('logging.exception') as mock_exception:
            
            # Both calls raise exceptions
            mock_get.side_effect = [ConnectionError("Connection lost"), RuntimeError("Persistent get error")]
            mock_connect.return_value = Mock()
            
            with pytest.raises(RuntimeError, match="Persistent get error"):
                dao.get("error_get_key", "default")
            
            mock_exception.assert_called_once()
    
    def test_get_redis_mode_with_other_exception(self):
        """Test get operation with non-connection exception"""
        dao = TraceRedis5Dao()
        dao._prefix = "get_other_error:"
        dao._cams_host = None
        
        with patch.object(dao, '_get') as mock_get, \
             patch('logging.exception') as mock_exception:
            
            # Raises a different exception
            mock_get.side_effect = ValueError("Get value error")
            
            with pytest.raises(ValueError, match="Get value error"):
                dao.get("value_error_key", "default")
            
            mock_exception.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_aget_redis_mode_with_connection_error(self):
        """Test async get operation with connection error and retry"""
        dao = TraceRedis5Dao()
        dao._prefix = "aget_retry:"
        dao._cams_host = None
        
        with patch.object(dao, '_aget') as mock_aget, \
             patch.object(dao, '_aconnect') as mock_aconnect, \
             patch('logging.warning') as mock_warning:
            
            # First call raises ConnectionError, second succeeds
            mock_aget.side_effect = [TimeoutError("Async get timeout"), b"async_retried_value"]
            mock_new_aredis = AsyncMock()
            mock_aconnect.return_value = mock_new_aredis
            
            result = await dao.aget("async_retry_key", "async_default")
            
            assert result == b"async_retried_value"
            assert dao.ard is mock_new_aredis
            assert mock_aget.call_count == 2
            mock_warning.assert_called_once()
    
    @pytest.mark.asyncio 
    async def test_aget_redis_mode_with_persistent_error(self):
        """Test async get operation with persistent error"""
        dao = TraceRedis5Dao()
        dao._prefix = "aget_error:"
        dao._cams_host = None
        
        with patch.object(dao, '_aget') as mock_aget, \
             patch.object(dao, '_aconnect') as mock_aconnect, \
             patch('logging.warning'), \
             patch('logging.exception') as mock_exception:
            
            # Both calls raise exceptions
            mock_aget.side_effect = [ConnectionError("Async connection lost"), RuntimeError("Persistent async get error")]
            mock_aconnect.return_value = AsyncMock()
            
            with pytest.raises(RuntimeError, match="Persistent async get error"):
                await dao.aget("async_error_key", "async_default")
            
            mock_exception.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_aget_redis_mode_with_other_exception(self):
        """Test async get operation with non-connection exception"""
        dao = TraceRedis5Dao()
        dao._prefix = "aget_other_error:"
        dao._cams_host = None
        
        with patch.object(dao, '_aget') as mock_aget, \
             patch('logging.exception') as mock_exception:
            
            # Raises a different exception
            mock_aget.side_effect = ValueError("Async get value error")
            
            with pytest.raises(ValueError, match="Async get value error"):
                await dao.aget("async_value_error_key", "async_default")
            
            mock_exception.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
