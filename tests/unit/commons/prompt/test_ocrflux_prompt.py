import pytest
import json
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from commons.prompt.ocrflux_prompt import prompt, prompt_pic
from commons.llm_gateway.models.chat_data import (
    Message, SftMultiModalImage, SftMultiModalText, SftMultiModalImageUrl,
    MultiModalType, MultiModalContent, LLMConfig, PublicGatewayHeader
)
from commons.llm_gateway.llm import LLModelRpc


class TestPromptConstants:
    """Test cases for prompt constants"""
    
    def test_prompt_constant_content(self):
        """Test that prompt constant contains expected content"""
        assert "文档的一页图像" in prompt
        assert "纯文本表示形式" in prompt
        assert "HTML 格式" in prompt
        assert "H1 标题" in prompt
        assert "不要产生幻觉" in prompt
    
    def test_prompt_pic_constant_content(self):
        """Test that prompt_pic constant contains expected content"""
        assert "一张图片" in prompt_pic
        assert "文字信息" in prompt_pic
        assert "纯文本表示形式" in prompt_pic
        assert "HTML 格式" in prompt_pic
        assert "H1 标题" in prompt_pic
        assert "不要产生幻觉" in prompt_pic
    
    def test_prompt_constants_are_strings(self):
        """Test that prompt constants are strings"""
        assert isinstance(prompt, str)
        assert isinstance(prompt_pic, str)
        assert len(prompt) > 0
        assert len(prompt_pic) > 0
    
    def test_prompt_constants_differences(self):
        """Test differences between prompt constants"""
        assert "文档的一页图像" in prompt
        assert "一张图片" in prompt_pic
        assert prompt != prompt_pic


class TestOcrFluxClass:
    """测试OcrFlux类"""
    
    def test_ocrflux_initialization(self):
        """测试OcrFlux类初始化（覆盖line 21）"""
        from commons.prompt.ocrflux_prompt import OcrFlux
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        
        assert ocrflux.public_gateway_header == mock_header
        assert ocrflux.llm_config is None

    def test_build_messages_sft_gateway(self):
        """测试_build_messages方法使用SFT网关（覆盖lines 24-34）"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        
        text = "测试文本内容"
        image_path = "http://example.com/test-image.jpg"
        
        messages = ocrflux._build_messages(text, image_path)
        
        assert len(messages) == 1
        assert isinstance(messages[0], Message)
        assert messages[0].role == "user"
        assert len(messages[0].content) == 2
        
        # 验证图片内容
        image_content = messages[0].content[0]
        assert isinstance(image_content, SftMultiModalImage)
        assert image_content.type == MultiModalType.image_url
        assert image_content.image_url.url == image_path
        
        # 验证文本内容
        text_content = messages[0].content[1]
        assert isinstance(text_content, SftMultiModalText)
        assert text_content.type == MultiModalType.text
        assert text_content.text == text

    def test_build_messages_public_gateway(self):
        """测试_build_messages方法使用Public网关（覆盖lines 35-43）"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig, MultiModalContent
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Public)
        
        text = "测试文本内容"
        image_path = "http://example.com/test-image.jpg"
        
        messages = ocrflux._build_messages(text, image_path)
        
        assert len(messages) == 1
        assert isinstance(messages[0], Message)
        assert messages[0].role == "user"
        assert len(messages[0].content) == 2
        
        # 验证图片内容
        image_content = messages[0].content[0]
        assert isinstance(image_content, MultiModalContent)
        assert image_content.type == MultiModalType.image_url
        assert image_content.content == image_path
        
        # 验证文本内容
        text_content = messages[0].content[1]
        assert isinstance(text_content, MultiModalContent)
        assert text_content.type == MultiModalType.text
        assert text_content.content == text

    @pytest.mark.asyncio
    async def test_preprocess_ocrflux_no_config(self):
        """测试preprocess_ocrflux方法未配置时抛出异常（覆盖lines 46-47）"""
        from commons.prompt.ocrflux_prompt import OcrFlux
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        # 不设置llm_config，保持为None
        
        with pytest.raises(Exception, match="配置未加载"):
            await ocrflux.preprocess_ocrflux("http://example.com/image.jpg", "测试提示", 60)

    @pytest.mark.asyncio
    async def test_preprocess_ocrflux_success(self):
        """测试preprocess_ocrflux方法成功处理（覆盖lines 49-58）"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        
        # 模拟adapter和返回数据
        mock_adapter = AsyncMock()
        mock_output_data = ["status", '{"natural_text": "解析后的自然文本"}']
        mock_adapter.async_chat_text.return_value = mock_output_data
        
        with patch('commons.prompt.ocrflux_prompt.create_gateway_adapter') as mock_create_adapter, \
             patch('logging.debug') as mock_log_debug:
            
            mock_create_adapter.return_value = mock_adapter
            
            result_text, success = await ocrflux.preprocess_ocrflux(
                "http://example.com/image.jpg", 
                "测试提示文本", 
                120
            )
            
            # 验证adapter被正确创建和调用
            mock_create_adapter.assert_called_once_with(
                llm_config=ocrflux.llm_config, 
                public_gateway_header=mock_header
            )
            mock_adapter.async_chat_text.assert_called_once()
            
            # 验证返回结果
            assert result_text == "解析后的自然文本"
            assert success is True
            mock_log_debug.assert_called()

    @pytest.mark.asyncio
    async def test_preprocess_ocrflux_empty_output(self):
        """测试preprocess_ocrflux方法输出为空（覆盖lines 54-56）"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Public)
        
        # 模拟adapter和空返回数据
        mock_adapter = AsyncMock()
        mock_adapter.async_chat_text.return_value = []  # 空输出
        
        with patch('commons.prompt.ocrflux_prompt.create_gateway_adapter') as mock_create_adapter, \
             patch('logging.debug') as mock_log_debug:
            
            mock_create_adapter.return_value = mock_adapter
            
            result_text, success = await ocrflux.preprocess_ocrflux(
                "http://example.com/image.jpg", 
                "测试提示", 
                60
            )
            
            # 验证返回结果
            assert result_text == ""
            assert success is True
            mock_log_debug.assert_called_with("llm output is empty")

    @pytest.mark.asyncio
    async def test_preprocess_ocrflux_insufficient_output(self):
        """测试preprocess_ocrflux方法输出长度不足（覆盖lines 54-56）"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        
        # 模拟adapter和长度不足的返回数据
        mock_adapter = AsyncMock()
        mock_adapter.async_chat_text.return_value = ["status"]  # 长度小于2
        
        with patch('commons.prompt.ocrflux_prompt.create_gateway_adapter') as mock_create_adapter, \
             patch('logging.debug') as mock_log_debug:
            
            mock_create_adapter.return_value = mock_adapter
            
            result_text, success = await ocrflux.preprocess_ocrflux(
                "http://example.com/image.jpg", 
                "测试提示", 
                60
            )
            
            # 验证返回结果
            assert result_text == ""
            assert success is True
            mock_log_debug.assert_called_with("llm output is empty")

    @pytest.mark.asyncio
    async def test_preprocess_ocrflux_json_parse_success(self):
        """测试preprocess_ocrflux方法JSON解析成功（覆盖lines 57-58）"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Public)
        
        # 模拟adapter和有效JSON返回数据
        mock_adapter = AsyncMock()
        mock_output_data = ["status", '{"natural_text": "成功解析的文本内容", "other_field": "其他信息"}']
        mock_adapter.async_chat_text.return_value = mock_output_data
        
        with patch('commons.prompt.ocrflux_prompt.create_gateway_adapter') as mock_create_adapter:
            mock_create_adapter.return_value = mock_adapter
            
            result_text, success = await ocrflux.preprocess_ocrflux(
                "http://example.com/image.jpg", 
                "测试提示", 
                90
            )
            
            # 验证返回结果
            assert result_text == "成功解析的文本内容"
            assert success is True

    @pytest.mark.asyncio
    async def test_preprocess_ocrflux_json_no_natural_text(self):
        """测试preprocess_ocrflux方法JSON中没有natural_text字段"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        
        # 模拟adapter和没有natural_text字段的JSON
        mock_adapter = AsyncMock()
        mock_output_data = ["status", '{"other_field": "其他信息"}']
        mock_adapter.async_chat_text.return_value = mock_output_data
        
        with patch('commons.prompt.ocrflux_prompt.create_gateway_adapter') as mock_create_adapter:
            mock_create_adapter.return_value = mock_adapter
            
            result_text, success = await ocrflux.preprocess_ocrflux(
                "http://example.com/image.jpg", 
                "测试提示", 
                60
            )
            
            # 验证返回空字符串（因为没有natural_text字段）
            assert result_text == ""
            assert success is True

    @pytest.mark.asyncio
    async def test_preprocess_ocrflux_json_parse_error(self):
        """测试preprocess_ocrflux方法JSON解析错误（覆盖lines 59-62）"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Public)
        
        # 模拟adapter和无效JSON返回数据
        mock_adapter = AsyncMock()
        mock_adapter.async_chat_text.return_value = ["status", "invalid json content"]
        
        with patch('commons.prompt.ocrflux_prompt.create_gateway_adapter') as mock_create_adapter, \
             patch('logging.error') as mock_log_error:
            
            mock_create_adapter.return_value = mock_adapter
            
            result_text, success = await ocrflux.preprocess_ocrflux(
                "http://example.com/image.jpg", 
                "测试提示", 
                60
            )
            
            # JSON解析错误时返回空字符串和False
            assert result_text == ""
            assert success is False
            mock_log_error.assert_called_once()

    @pytest.mark.asyncio
    async def test_preprocess_ocrflux_adapter_exception(self):
        """测试preprocess_ocrflux方法adapter异常（覆盖lines 59-62）"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        
        # 模拟adapter抛出异常
        mock_adapter = AsyncMock()
        mock_adapter.async_chat_text.side_effect = Exception("Network error")
        
        with patch('commons.prompt.ocrflux_prompt.create_gateway_adapter') as mock_create_adapter, \
             patch('logging.error') as mock_log_error:
            
            mock_create_adapter.return_value = mock_adapter
            
            result_text, success = await ocrflux.preprocess_ocrflux(
                "http://example.com/image.jpg", 
                "测试提示", 
                60
            )
            
            # 异常时返回空字符串和False
            assert result_text == ""
            assert success is False
            mock_log_error.assert_called_once()

    def test_ocrflux_class_variable(self):
        """测试OcrFlux类变量"""
        from commons.prompt.ocrflux_prompt import OcrFlux
        from commons.llm_gateway.models.chat_data import LLMConfig
        
        # 重置类变量
        OcrFlux.llm_config = None
        assert OcrFlux.llm_config is None
        
        # 设置类变量
        test_config = LLMConfig(gateway=LLModelRpc.Gateway.Public)
        OcrFlux.llm_config = test_config
        assert OcrFlux.llm_config == test_config

    @pytest.mark.asyncio
    async def test_preprocess_ocrflux_default_timeout(self):
        """测试preprocess_ocrflux方法使用默认超时时间"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        
        # 模拟adapter和返回数据
        mock_adapter = AsyncMock()
        mock_output_data = ["status", '{"natural_text": "默认超时测试"}']
        mock_adapter.async_chat_text.return_value = mock_output_data
        
        with patch('commons.prompt.ocrflux_prompt.create_gateway_adapter') as mock_create_adapter:
            mock_create_adapter.return_value = mock_adapter
            
            # 使用默认超时时间（60）
            result_text, success = await ocrflux.preprocess_ocrflux(
                "http://example.com/image.jpg", 
                "测试提示"
                # 不传time_out参数，使用默认值60
            )
            
            # 验证调用参数中包含默认超时时间（在extra_dict中）
            call_args = mock_adapter.async_chat_text.call_args[0][0]
            assert call_args.extra_dict.get("time_out") == 60
            
            assert result_text == "默认超时测试"
            assert success is True

    @pytest.mark.asyncio
    async def test_preprocess_ocrflux_custom_timeout(self):
        """测试preprocess_ocrflux方法使用自定义超时时间"""
        from commons.prompt.ocrflux_prompt import OcrFlux, LLMConfig
        
        mock_header = Mock(spec=PublicGatewayHeader)
        ocrflux = OcrFlux(mock_header)
        ocrflux.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Public)
        
        # 模拟adapter和返回数据
        mock_adapter = AsyncMock()
        mock_output_data = ["status", '{"natural_text": "自定义超时测试"}']
        mock_adapter.async_chat_text.return_value = mock_output_data
        
        with patch('commons.prompt.ocrflux_prompt.create_gateway_adapter') as mock_create_adapter:
            mock_create_adapter.return_value = mock_adapter
            
            # 使用自定义超时时间
            result_text, success = await ocrflux.preprocess_ocrflux(
                "http://example.com/image.jpg", 
                "测试提示",
                180  # 自定义超时时间
            )
            
            # 验证调用参数中包含自定义超时时间（在extra_dict中）
            call_args = mock_adapter.async_chat_text.call_args[0][0]
            assert call_args.extra_dict.get("time_out") == 180
            
            assert result_text == "自定义超时测试"
            assert success is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])