import pytest
import json
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from commons.llm_gateway.models.chat_data import (
    Message, SftMultiModalImage, SftMultiModalText, MultiModalType, 
    SftMultiModalImageUrl, LLMConfig, PublicGatewayHeader, MultiModalContent
)
from commons.llm_gateway.llm import LLModelRpc
from commons.prompt.checkbox_prompt import CheckBox, prompt


class TestCheckBoxPrompt:
    """测试CheckBox类"""
    
    def test_prompt_constant(self):
        """测试prompt常量"""
        assert "文档解析纠错专家" in prompt
        assert "复选框内容块" in prompt
        assert "错误类型" in prompt
        assert "工作流程" in prompt
        assert "输出格式" in prompt
        assert "json" in prompt

    def test_checkbox_initialization(self):
        """测试CheckBox类初始化"""
        mock_header = Mock(spec=PublicGatewayHeader)
        checkbox = CheckBox(mock_header)
        
        assert checkbox.public_gateway_header == mock_header
        assert checkbox.llm_config is None

    def test_load_json_object_success(self):
        """测试_load_json_object方法成功解析JSON"""
        text = '''这是一些文本
```json
{"line": 1, "wrong": "冈男", "refined": "☑男"}
```
更多文本'''
        
        result = CheckBox._load_json_object(text)
        
        assert isinstance(result, dict)
        assert result["line"] == 1
        assert result["wrong"] == "冈男"
        assert result["refined"] == "☑男"

    def test_load_json_object_with_list(self):
        """测试_load_json_object方法解析JSON列表"""
        text = '''
```json
[
    {"line": 1, "wrong": "冈男", "refined": "☑男"},
    {"line": 2, "wrong": "口女", "refined": "☐女"}
]
```
'''
        result = CheckBox._load_json_object(text)
        
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0]["line"] == 1
        assert result[1]["line"] == 2

    def test_load_json_object_invalid_json(self):
        """测试_load_json_object方法处理无效JSON"""
        text = '''这是一些文本
```json
{invalid json content
```
更多文本'''
        
        result = CheckBox._load_json_object(text)
        
        # 无效JSON时返回原始文本
        assert result == text

    def test_load_json_object_no_json_blocks(self):
        """测试_load_json_object方法没有JSON块时"""
        text = "这只是普通文本，没有JSON块"
        
        result = CheckBox._load_json_object(text)
        
        # 没有JSON块时返回原始文本
        assert result == text

    def test_load_json_object_empty_json_block(self):
        """测试_load_json_object方法空JSON块"""
        text = '''
```json
```
'''
        result = CheckBox._load_json_object(text)
        
        # 空JSON块时返回原始文本
        assert result == text

    def test_build_messages_sft_gateway(self):
        """测试_build_messages方法使用SFT网关"""
        mock_header = Mock(spec=PublicGatewayHeader)
        checkbox = CheckBox(mock_header)
        checkbox.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        
        text = "测试OCR文本"
        image_path = "http://example.com/image.jpg"
        
        messages = checkbox._build_messages(text, image_path)
        
        assert len(messages) == 1
        assert isinstance(messages[0], Message)
        assert messages[0].role == "user"
        assert len(messages[0].content) == 2
        
        # 验证图片内容
        image_content = messages[0].content[0]
        assert isinstance(image_content, SftMultiModalImage)
        assert image_content.type == MultiModalType.image_url
        assert image_content.image_url.url == image_path
        
        # 验证文本内容
        text_content = messages[0].content[1]
        assert isinstance(text_content, SftMultiModalText)
        assert text_content.type == MultiModalType.text
        assert prompt in text_content.text
        assert text in text_content.text

    def test_build_messages_public_gateway(self):
        """测试_build_messages方法使用Public网关"""
        mock_header = Mock(spec=PublicGatewayHeader)
        checkbox = CheckBox(mock_header)
        checkbox.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Public)
        
        text = "测试OCR文本"
        image_path = "http://example.com/image.jpg"
        
        messages = checkbox._build_messages(text, image_path)
        
        assert len(messages) == 1
        assert isinstance(messages[0], Message)
        assert messages[0].role == "user"
        assert len(messages[0].content) == 2
        
        # 验证图片内容
        image_content = messages[0].content[0]
        assert isinstance(image_content, MultiModalContent)
        assert image_content.type == MultiModalType.image_url
        assert image_content.content == image_path
        
        # 验证文本内容
        text_content = messages[0].content[1]
        assert isinstance(text_content, MultiModalContent)
        assert text_content.type == MultiModalType.text
        assert prompt in text_content.content
        assert text in text_content.content

    @pytest.mark.asyncio
    async def test_preprocess_llm_v1_no_config(self):
        """测试preprocess_llm_v1方法未配置时抛出异常"""
        mock_header = Mock(spec=PublicGatewayHeader)
        checkbox = CheckBox(mock_header)
        # 不设置llm_config，保持为None
        
        with pytest.raises(Exception, match="配置未加载"):
            await checkbox.preprocess_llm_v1("测试文本", "http://example.com/image.jpg")

    @pytest.mark.asyncio
    async def test_preprocess_llm_v1_success(self):
        """测试preprocess_llm_v1方法成功处理"""
        mock_header = Mock(spec=PublicGatewayHeader)
        checkbox = CheckBox(mock_header)
        checkbox.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        
        # 模拟adapter和返回数据，使用```json格式包装
        mock_adapter = AsyncMock()
        mock_output_data = ["status", '''这是处理结果：
```json
{"refined_results": [{"line": 1, "wrong": "冈男", "refined": "☑男"}]}
```
处理完成''']
        mock_adapter.async_multimodal.return_value = mock_output_data
        
        with patch('commons.prompt.checkbox_prompt.create_gateway_adapter') as mock_create_adapter:
            mock_create_adapter.return_value = mock_adapter
            
            result = await checkbox.preprocess_llm_v1("测试OCR文本", "http://example.com/image.jpg")
            
            # 验证adapter被正确创建和调用
            mock_create_adapter.assert_called_once_with(checkbox.llm_config, mock_header)
            mock_adapter.async_multimodal.assert_called_once()
            
            # 验证返回结果 - _load_json_object会解析JSON块
            assert isinstance(result, dict)
            assert "refined_results" in result

    @pytest.mark.asyncio
    async def test_preprocess_llm_v1_empty_output(self):
        """测试preprocess_llm_v1方法输出为空"""
        mock_header = Mock(spec=PublicGatewayHeader)
        checkbox = CheckBox(mock_header)
        checkbox.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Public)
        
        # 模拟adapter和空返回数据
        mock_adapter = AsyncMock()
        mock_adapter.async_multimodal.return_value = []  # 空输出
        
        with patch('commons.prompt.checkbox_prompt.create_gateway_adapter') as mock_create_adapter, \
             patch('logging.debug') as mock_log_debug:
            
            mock_create_adapter.return_value = mock_adapter
            
            result = await checkbox.preprocess_llm_v1("测试OCR文本", "http://example.com/image.jpg")
            
            # 验证返回空列表
            assert result == []
            mock_log_debug.assert_called_with("llm output is empty")

    @pytest.mark.asyncio
    async def test_preprocess_llm_v1_insufficient_output(self):
        """测试preprocess_llm_v1方法输出长度不足"""
        mock_header = Mock(spec=PublicGatewayHeader)
        checkbox = CheckBox(mock_header)
        checkbox.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        
        # 模拟adapter和不足长度的返回数据
        mock_adapter = AsyncMock()
        mock_adapter.async_multimodal.return_value = ["status"]  # 长度小于2
        
        with patch('commons.prompt.checkbox_prompt.create_gateway_adapter') as mock_create_adapter, \
             patch('logging.debug') as mock_log_debug:
            
            mock_create_adapter.return_value = mock_adapter
            
            result = await checkbox.preprocess_llm_v1("测试OCR文本", "http://example.com/image.jpg")
            
            # 验证返回空列表
            assert result == []
            mock_log_debug.assert_called_with("llm output is empty")

    @pytest.mark.asyncio
    async def test_preprocess_llm_v1_json_parse_error(self):
        """测试preprocess_llm_v1方法JSON解析错误"""
        mock_header = Mock(spec=PublicGatewayHeader)
        checkbox = CheckBox(mock_header)
        checkbox.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Public)
        
        # 模拟adapter和无效JSON返回数据
        mock_adapter = AsyncMock()
        mock_adapter.async_multimodal.return_value = ["status", "invalid json content"]
        
        with patch('commons.prompt.checkbox_prompt.create_gateway_adapter') as mock_create_adapter:
            mock_create_adapter.return_value = mock_adapter
            
            result = await checkbox.preprocess_llm_v1("测试OCR文本", "http://example.com/image.jpg")
            
            # _load_json_object无法解析JSON时返回原始文本
            assert result == "invalid json content"

    @pytest.mark.asyncio
    async def test_preprocess_llm_v1_adapter_exception(self):
        """测试preprocess_llm_v1方法adapter异常"""
        mock_header = Mock(spec=PublicGatewayHeader)
        checkbox = CheckBox(mock_header)
        checkbox.llm_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        
        # 模拟adapter抛出异常
        mock_adapter = AsyncMock()
        mock_adapter.async_multimodal.side_effect = Exception("Adapter error")
        
        with patch('commons.prompt.checkbox_prompt.create_gateway_adapter') as mock_create_adapter, \
             patch('logging.error') as mock_log_error:
            
            mock_create_adapter.return_value = mock_adapter
            
            result = await checkbox.preprocess_llm_v1("测试OCR文本", "http://example.com/image.jpg")
            
            # 异常时返回空列表
            assert result == []
            mock_log_error.assert_called_once()

    def test_load_json_object_complex_json(self):
        """测试_load_json_object方法处理复杂JSON"""
        text = '''检查结果：
```json
[
    {
        "line": 1,
        "wrong": "性别：冈男，口女",
        "refined": "性别：☑男，☐女"
    },
    {
        "line": 5,
        "wrong": "学历：区本科，因硕士",
        "refined": "学历：☐本科，☑硕士"
    }
]
```
结束'''
        
        result = CheckBox._load_json_object(text)
        
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0]["line"] == 1
        assert "冈男" in result[0]["wrong"]
        assert "☑男" in result[0]["refined"]
        assert result[1]["line"] == 5
        assert "区本科" in result[1]["wrong"]
        assert "☑硕士" in result[1]["refined"]

    def test_load_json_object_nested_json(self):
        """测试_load_json_object方法处理嵌套JSON"""
        text = '''
```json
{
    "results": [
        {"line": 1, "wrong": "冈", "refined": "☑"},
        {"line": 2, "wrong": "口", "refined": "☐"}
    ],
    "summary": {"total": 2, "errors": 2}
}
```
'''
        result = CheckBox._load_json_object(text)
        
        assert isinstance(result, dict)
        assert "results" in result
        assert "summary" in result
        assert len(result["results"]) == 2
        assert result["summary"]["total"] == 2


class TestCheckBoxClassVariable:
    """测试CheckBox类变量"""
    
    def test_llm_config_class_variable(self):
        """测试llm_config类变量"""
        # 重置类变量
        CheckBox.llm_config = None
        assert CheckBox.llm_config is None
        
        # 设置类变量
        test_config = LLMConfig(gateway=LLModelRpc.Gateway.Sft)
        CheckBox.llm_config = test_config
        assert CheckBox.llm_config == test_config
        
        # 创建实例应该能访问类变量
        mock_header = Mock(spec=PublicGatewayHeader)
        checkbox = CheckBox(mock_header)
        assert checkbox.llm_config == test_config


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
