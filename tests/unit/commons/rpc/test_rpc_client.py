# -*- coding: utf-8 -*-
"""
Unit tests for commons.rpc.rpc_client module
"""

import pytest
from unittest.mock import Mock, patch
from commons.rpc.rpc_client import RpcClient
from commons.rpc.rpc_entity import RpcParams, ConnInfo, RpcResp
from commons.auth.auth_rpc import AuthRequest, SigVerType


class TestRpcClient:
    """测试 RpcClient 类"""

    def test_rpc_client_init(self):
        """测试 RpcClient 初始化"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        rpc_params = RpcParams(
            enabled=True,
            class_path="test.path",
            conn_infos=[conn_info]
        )
        
        with patch('commons.rpc.rpc_client.AuthRequest') as mock_auth_request:
            mock_auth_request.return_value = Mock()
            client = RpcClient(rpc_params)
            
            assert len(client._reqs) == 1
            mock_auth_request.assert_called_once_with(
                conn_info.host, 
                conn_info.ak, 
                conn_info.sk, 
                conn_info.sig_type
            )

    def test_rpc_client_init_multiple_conn_infos(self):
        """测试 RpcClient 初始化多个连接信息"""
        conn_info1 = ConnInfo(
            host="test1.com",
            ak="test_ak1",
            sk="test_sk1",
            sig_type=SigVerType.wps2
        )
        conn_info2 = ConnInfo(
            host="test2.com",
            ak="test_ak2",
            sk="test_sk2",
            sig_type=SigVerType.wps4
        )
        rpc_params = RpcParams(
            enabled=True,
            class_path="test.path",
            conn_infos=[conn_info1, conn_info2]
        )
        
        with patch('commons.rpc.rpc_client.AuthRequest') as mock_auth_request:
            mock_auth_request.return_value = Mock()
            client = RpcClient(rpc_params)
            
            assert len(client._reqs) == 2
            assert mock_auth_request.call_count == 2

    def test_rpc_client_init_empty_conn_infos(self):
        """测试 RpcClient 初始化空连接信息"""
        rpc_params = RpcParams(
            enabled=True,
            class_path="test.path",
            conn_infos=[]
        )
        
        client = RpcClient(rpc_params)
        
        assert len(client._reqs) == 0

    def test_resp_error(self):
        """测试 resp_error 方法"""
        with patch('commons.rpc.rpc_client.logger') as mock_logger:
            with patch('commons.rpc.rpc_entity.RpcResp.error_response') as mock_error_response:
                mock_error_response.return_value = Mock()
                
                result = RpcClient.resp_error("test_func", "/test/uri", 400, "Bad Request")
                
                mock_logger.error.assert_called_once()
                mock_error_response.assert_called_once_with("API response error: Bad Request", 400)
                assert result == mock_error_response.return_value

    def test_http_error(self):
        """测试 http_error 方法"""
        with patch('commons.rpc.rpc_client.logger') as mock_logger:
            with patch('commons.rpc.rpc_entity.RpcResp.error_response') as mock_error_response:
                mock_error_response.return_value = Mock()
                
                result = RpcClient.http_error("test_func", "/test/uri", 500, "Internal Server Error")
                
                mock_logger.error.assert_called_once()
                mock_error_response.assert_called_once_with("HTTP error: status 500", 500)
                assert result == mock_error_response.return_value

    def test_exception_error(self):
        """测试 exception_error 方法"""
        with patch('commons.rpc.rpc_client.logger') as mock_logger:
            with patch('commons.rpc.rpc_entity.RpcResp.error_response') as mock_error_response:
                mock_error_response.return_value = Mock()
                
                result = RpcClient.exception_error("test_func", "/test/uri", "Connection timeout")
                
                mock_logger.error.assert_called_once()
                mock_error_response.assert_called_once_with("Request failed: Connection timeout")
                assert result == mock_error_response.return_value

    def test_exception_error_with_exception_object(self):
        """测试 exception_error 方法处理异常对象"""
        with patch('commons.rpc.rpc_client.logger') as mock_logger:
            with patch('commons.rpc.rpc_entity.RpcResp.error_response') as mock_error_response:
                mock_error_response.return_value = Mock()
                
                exception = ValueError("Invalid input")
                result = RpcClient.exception_error("test_func", "/test/uri", exception)
                
                mock_logger.error.assert_called_once()
                mock_error_response.assert_called_once_with("Request failed: Invalid input")
                assert result == mock_error_response.return_value

    def test_error_methods_log_format(self):
        """测试错误方法的日志格式"""
        with patch('commons.rpc.rpc_client.logger') as mock_logger:
            with patch('commons.rpc.rpc_entity.RpcResp.error_response') as mock_error_response:
                mock_error_response.return_value = Mock()
                
                # 测试 resp_error 日志格式
                RpcClient.resp_error("test_func", "/test/uri", 400, "Bad Request")
                log_call = mock_logger.error.call_args[0][0]
                assert "RpcClient rpc fail, response error" in log_call
                assert "class: RpcClient" in log_call
                assert "func: test_func" in log_call
                assert "uri: /test/uri" in log_call
                assert "status: 400" in log_call
                assert "text: Bad Request" in log_call
                
                # 测试 http_error 日志格式
                RpcClient.http_error("test_func", "/test/uri", 500, "Internal Server Error")
                log_call = mock_logger.error.call_args[0][0]
                assert "RpcClient rpc fail, http error" in log_call
                
                # 测试 exception_error 日志格式
                RpcClient.exception_error("test_func", "/test/uri", "Connection timeout")
                log_call = mock_logger.error.call_args[0][0]
                assert "RpcClient rpc fail, except exception" in log_call

    def test_rpc_client_inheritance(self):
        """测试 RpcClient 可以被继承"""
        class TestRpcClient(RpcClient):
            def test_method(self):
                return "test"
        
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        rpc_params = RpcParams(
            enabled=True,
            class_path="test.path",
            conn_infos=[conn_info]
        )
        
        with patch('commons.rpc.rpc_client.AuthRequest') as mock_auth_request:
            mock_auth_request.return_value = Mock()
            client = TestRpcClient(rpc_params)
            
            assert isinstance(client, RpcClient)
            assert client.test_method() == "test"
            assert len(client._reqs) == 1
