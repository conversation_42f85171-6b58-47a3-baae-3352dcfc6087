# -*- coding: utf-8 -*-
"""
Unit tests for commons.rpc.rpc_factory module
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from commons.rpc.rpc_factory import RpcFactory
from commons.rpc.rpc_entity import RpcPara<PERSON>, RpcName, ConnInfo
from commons.rpc.rpc_client import RpcClient
from commons.rpc.yun.rpc_interface import YunRpcInterface
from commons.rpc.normal_model.rpc_interface import NormalRpcInterface
from commons.rpc.monkey_ocr.rpc_interface import MonkeyOcrRpcInterface
from commons.auth.auth_rpc import SigVerType


class TestRpcFactory:
    """测试 RpcFactory 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        if hasattr(RpcFactory, '_instances'):
            RpcFactory._instances.clear()
        self.factory = RpcFactory()

    def test_rpc_factory_init(self):
        """测试 RpcFactory 初始化"""
        assert self.factory._rpc_config is None
        assert self.factory._rpc_instances == {}

    def test_rpc_factory_singleton(self):
        """测试 RpcFactory 单例模式"""
        factory1 = RpcFactory()
        factory2 = RpcFactory()
        assert factory1 is factory2

    def test_init_with_config(self):
        """测试使用配置初始化"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        config = {
            "yun": RpcParams(
                enabled=True,
                class_path="commons.rpc.yun.rpc_interface.YunRpcInterface",
                conn_infos=[conn_info]
            )
        }
        
        # 简化测试，只验证配置被设置
        self.factory.init(config)
        assert self.factory._rpc_config == config

    def test_init_with_disabled_config(self):
        """测试初始化时跳过禁用的配置"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        config = {
            "yun": RpcParams(
                enabled=False,
                class_path="commons.rpc.yun.rpc_interface.YunRpcInterface",
                conn_infos=[conn_info]
            )
        }
        
        self.factory.init(config)
        
        assert self.factory._rpc_config == config
        assert "yun" not in self.factory._rpc_instances

    def test_register_from_config_exception_handling(self):
        """测试配置注册时的异常处理"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        config = {
            "invalid": RpcParams(
                enabled=True,
                class_path="invalid.module.Class",
                conn_infos=[conn_info]
            )
        }
        
        with patch('commons.rpc.rpc_factory.logger') as mock_logger:
            result = self.factory.init(config)
            
            # 应该记录错误但不会抛出异常
            mock_logger.error.assert_called()

    def test_register_rpc_success(self):
        """测试成功注册RPC"""
        class MockRpcClient(RpcClient):
            def __init__(self, config):
                self.config = config
        
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        config = RpcParams(
            enabled=True,
            class_path="test.path",
            conn_infos=[conn_info]
        )
        
        self.factory._register_rpc("test_type", MockRpcClient, config)
        
        assert "test_type" in self.factory._rpc_instances
        assert isinstance(self.factory._rpc_instances["test_type"], MockRpcClient)

    def test_register_rpc_invalid_class(self):
        """测试注册无效的RPC类"""
        class InvalidClass:
            pass
        
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        config = RpcParams(
            enabled=True,
            class_path="test.path",
            conn_infos=[conn_info]
        )
        
        with pytest.raises(ValueError, match="RPC class must implement RpcClient"):
            self.factory._register_rpc("test_type", InvalidClass, config)

    def test_get_rpc_instance_success(self):
        """测试成功获取RPC实例"""
        mock_instance = Mock(spec=RpcClient)
        self.factory._rpc_instances["test_type"] = mock_instance
        
        result = self.factory._get_rpc_instance("test_type")
        assert result == mock_instance

    def test_get_rpc_instance_not_registered(self):
        """测试获取未注册的RPC实例"""
        with pytest.raises(ValueError, match="RPC type test_type is not registered"):
            self.factory._get_rpc_instance("test_type")

    def test_get_yun_rpc_success(self):
        """测试成功获取云文档RPC"""
        mock_yun_rpc = Mock(spec=YunRpcInterface)
        self.factory._rpc_instances[RpcName.yun] = mock_yun_rpc
        
        result = self.factory.get_yun_rpc()
        assert result == mock_yun_rpc

    def test_get_yun_rpc_wrong_type(self):
        """测试获取云文档RPC时类型错误"""
        mock_wrong_rpc = Mock()  # 不是 YunRpcInterface
        self.factory._rpc_instances[RpcName.yun] = mock_wrong_rpc
        
        with pytest.raises(TypeError, match="Expected YunRpcInterface"):
            self.factory.get_yun_rpc()

    def test_get_normal_model_rpc_success(self):
        """测试成功获取普通模型RPC"""
        mock_normal_rpc = Mock(spec=NormalRpcInterface)
        self.factory._rpc_instances[RpcName.normal_model] = mock_normal_rpc
        
        result = self.factory.get_normal_model_rpc()
        assert result == mock_normal_rpc

    def test_get_normal_model_rpc_wrong_type(self):
        """测试获取普通模型RPC时类型错误"""
        mock_wrong_rpc = Mock()  # 不是 NormalRpcInterface
        self.factory._rpc_instances[RpcName.normal_model] = mock_wrong_rpc
        
        with pytest.raises(TypeError, match="Expected NormalRpcInterface"):
            self.factory.get_normal_model_rpc()

    def test_get_monkey_ocr_rpc_success(self):
        """测试成功获取monkey ocr RPC"""
        mock_monkey_rpc = Mock(spec=MonkeyOcrRpcInterface)
        self.factory._rpc_instances[RpcName.monkey_ocr] = mock_monkey_rpc
        
        result = self.factory.get_monkey_ocr_rpc()
        assert result == mock_monkey_rpc

    def test_get_monkey_ocr_rpc_wrong_type(self):
        """测试获取monkey ocr RPC时类型错误"""
        mock_wrong_rpc = Mock()  # 不是 MonkeyOcrRpcInterface
        self.factory._rpc_instances[RpcName.monkey_ocr] = mock_wrong_rpc
        
        with pytest.raises(TypeError, match="Expected NormalRpcInterface"):
            self.factory.get_monkey_ocr_rpc()

    def test_register_from_config_with_exception(self):
        """测试配置注册时发生异常"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        config = {
            "test": RpcParams(
                enabled=True,
                class_path="test.path",
                conn_infos=[conn_info]
            )
        }
        
        # 简化测试，只验证配置被设置
        result = self.factory.init(config)
        assert self.factory._rpc_config == config

    def test_register_from_config_empty_config(self):
        """测试空配置注册"""
        config = {}
        
        result = self.factory.init(config)
        
        assert self.factory._rpc_config == config
        assert len(self.factory._rpc_instances) == 0

    def test_register_from_config_all_disabled(self):
        """测试所有配置都被禁用的情况"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        config = {
            "yun": RpcParams(
                enabled=False,
                class_path="test.path",
                conn_infos=[conn_info]
            ),
            "normal_model": RpcParams(
                enabled=False,
                class_path="test.path",
                conn_infos=[conn_info]
            )
        }
        
        with patch('commons.rpc.rpc_factory.logger') as mock_logger:
            result = self.factory.init(config)
            
            assert len(self.factory._rpc_instances) == 0
            # 应该记录禁用信息
            assert mock_logger.info.call_count == 2
