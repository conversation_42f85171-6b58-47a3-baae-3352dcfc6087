# -*- coding: utf-8 -*-
"""
Unit tests for commons.rpc.rpc_entity module
"""

import pytest
from unittest.mock import Mock
from commons.rpc.rpc_entity import (
    RpcName, ConnInfo, RpcParams, RpcResp, TRespData
)
from commons.auth.auth_rpc import SigVerType


class TestRpcName:
    """测试 RpcName 枚举"""

    def test_rpc_name_values(self):
        """测试 RpcName 枚举值"""
        assert RpcName.yun == "yun"
        assert RpcName.normal_model == "normal_model"
        assert RpcName.monkey_ocr == "monkey_ocr"


class TestConnInfo:
    """测试 ConnInfo 类"""

    def test_conn_info_init(self):
        """测试 ConnInfo 初始化"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        
        assert conn_info.host == "test.com"
        assert conn_info.ak == "test_ak"
        assert conn_info.sk == "test_sk"
        assert conn_info.sig_type == SigVerType.wps2
        assert conn_info.ak_env is None
        assert conn_info.sk_env is None

    def test_conn_info_with_env_vars(self):
        """测试 ConnInfo 带环境变量"""
        conn_info = ConnInfo(
            host="test.com",
            ak_env="TEST_AK",
            sk_env="TEST_SK",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps4
        )
        
        assert conn_info.ak_env == "TEST_AK"
        assert conn_info.sk_env == "TEST_SK"
        assert conn_info.sig_type == SigVerType.wps4

    def test_validate_sig_type_wps2_string(self):
        """测试验证 wps2 字符串签名类型"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type="wps2"
        )
        
        assert conn_info.sig_type == SigVerType.wps2

    def test_validate_sig_type_wps4_string(self):
        """测试验证 wps4 字符串签名类型"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type="wps4"
        )
        
        assert conn_info.sig_type == SigVerType.wps4

    def test_validate_sig_type_case_insensitive(self):
        """测试验证签名类型大小写不敏感"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type="WPS2"
        )
        
        assert conn_info.sig_type == SigVerType.wps2

    def test_validate_sig_type_invalid_string(self):
        """测试验证无效字符串签名类型"""
        with pytest.raises(ValueError, match="Invalid sig_type"):
            ConnInfo(
                host="test.com",
                ak="test_ak",
                sk="test_sk",
                sig_type="invalid"
            )

    def test_validate_sig_type_invalid_type(self):
        """测试验证无效类型签名类型"""
        with pytest.raises(Exception):  # Pydantic validation error
            ConnInfo(
                host="test.com",
                ak="test_ak",
                sk="test_sk",
                sig_type=123
            )


class TestRpcParams:
    """测试 RpcParams 类"""

    def test_rpc_params_init(self):
        """测试 RpcParams 初始化"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        
        rpc_params = RpcParams(
            class_path="test.path",
            enabled=True,
            conn_infos=[conn_info]
        )
        
        assert rpc_params.class_path == "test.path"
        assert rpc_params.enabled is True
        assert len(rpc_params.conn_infos) == 1
        assert rpc_params.conn_infos[0] == conn_info

    def test_rpc_params_default_enabled(self):
        """测试 RpcParams 默认 enabled 值"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        
        rpc_params = RpcParams(
            class_path="test.path",
            conn_infos=[conn_info]
        )
        
        assert rpc_params.enabled is False

    def test_rpc_params_multiple_conn_infos(self):
        """测试 RpcParams 多个连接信息"""
        conn_info1 = ConnInfo(
            host="test1.com",
            ak="test_ak1",
            sk="test_sk1",
            sig_type=SigVerType.wps2
        )
        conn_info2 = ConnInfo(
            host="test2.com",
            ak="test_ak2",
            sk="test_sk2",
            sig_type=SigVerType.wps4
        )
        
        rpc_params = RpcParams(
            class_path="test.path",
            enabled=True,
            conn_infos=[conn_info1, conn_info2]
        )
        
        assert len(rpc_params.conn_infos) == 2
        assert rpc_params.conn_infos[0] == conn_info1
        assert rpc_params.conn_infos[1] == conn_info2


class TestRpcResp:
    """测试 RpcResp 类"""

    def test_rpc_resp_init_success(self):
        """测试 RpcResp 成功初始化"""
        resp = RpcResp(
            result=RpcResp.ResultCode.OK,
            data={"test": "data"}
        )
        
        assert resp.result == RpcResp.ResultCode.OK
        assert resp.data == {"test": "data"}
        assert resp.error_message is None
        assert resp.status_code is None

    def test_rpc_resp_init_error(self):
        """测试 RpcResp 错误初始化"""
        resp = RpcResp(
            result=RpcResp.ResultCode.ERROR,
            error_message="Test error",
            status_code=500
        )
        
        assert resp.result == RpcResp.ResultCode.ERROR
        assert resp.data is None
        assert resp.error_message == "Test error"
        assert resp.status_code == 500

    def test_success_response(self):
        """测试 success_response 方法"""
        data = {"test": "data"}
        resp = RpcResp.success_response(data)
        
        assert resp.result == RpcResp.ResultCode.OK
        assert resp.data == data
        assert resp.error_message is None
        assert resp.status_code is None

    def test_error_response_default(self):
        """测试 error_response 方法默认参数"""
        resp = RpcResp.error_response("Test error")
        
        assert resp.result == RpcResp.ResultCode.ERROR
        assert resp.data is None
        assert resp.error_message == "Test error"
        assert resp.status_code is None

    def test_error_response_with_status_code(self):
        """测试 error_response 方法带状态码"""
        resp = RpcResp.error_response("Test error", 404)
        
        assert resp.result == RpcResp.ResultCode.ERROR
        assert resp.data is None
        assert resp.error_message == "Test error"
        assert resp.status_code == 404

    def test_error_response_custom_result(self):
        """测试 error_response 方法自定义结果"""
        resp = RpcResp.error_response("Test error", result=RpcResp.ResultCode.ERROR)
        
        assert resp.result == RpcResp.ResultCode.ERROR
        assert resp.data is None
        assert resp.error_message == "Test error"

    def test_is_success_true(self):
        """测试 is_success 方法成功情况"""
        resp = RpcResp(
            result=RpcResp.ResultCode.OK,
            data={"test": "data"}
        )
        
        assert resp.is_success() is True

    def test_is_success_false(self):
        """测试 is_success 方法失败情况"""
        resp = RpcResp(
            result=RpcResp.ResultCode.ERROR,
            error_message="Test error"
        )
        
        assert resp.is_success() is False

    def test_get_data_with_data(self):
        """测试 get_data 方法有数据情况"""
        data = {"test": "data"}
        resp = RpcResp(
            result=RpcResp.ResultCode.OK,
            data=data
        )
        
        assert resp.get_data() == data

    def test_get_data_without_data(self):
        """测试 get_data 方法无数据情况"""
        resp = RpcResp(
            result=RpcResp.ResultCode.OK,
            data=None
        )
        
        assert resp.get_data() is None

    def test_get_data_with_falsy_data(self):
        """测试 get_data 方法假值数据情况"""
        resp = RpcResp(
            result=RpcResp.ResultCode.OK,
            data=""
        )
        
        assert resp.get_data() is None  # 空字符串是假值，应该返回 None

    def test_result_code_enum(self):
        """测试 ResultCode 枚举"""
        assert RpcResp.ResultCode.OK == "ok"
        assert RpcResp.ResultCode.ERROR == "error"

    def test_rpc_resp_generic_type(self):
        """测试 RpcResp 泛型类型"""
        # 测试字符串类型
        str_resp = RpcResp[str](
            result=RpcResp.ResultCode.OK,
            data="test string"
        )
        assert isinstance(str_resp.data, str)
        
        # 测试字典类型
        dict_resp = RpcResp[dict](
            result=RpcResp.ResultCode.OK,
            data={"key": "value"}
        )
        assert isinstance(dict_resp.data, dict)
        
        # 测试列表类型
        list_resp = RpcResp[list](
            result=RpcResp.ResultCode.OK,
            data=[1, 2, 3]
        )
        assert isinstance(list_resp.data, list)
