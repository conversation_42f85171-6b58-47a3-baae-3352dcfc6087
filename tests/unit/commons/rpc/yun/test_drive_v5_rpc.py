# -*- coding: utf-8 -*-
"""
Unit tests for commons.rpc.yun.drive_v5_rpc module
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from commons.rpc.yun.drive_v5_rpc import DriveV5Rpc
from commons.rpc.rpc_entity import RpcParams, ConnInfo, RpcResp
from commons.rpc.rpc_client import RpcClient
from commons.rpc.yun.rpc_interface import GetDownloadUrlData
from commons.auth.auth_rpc import SigVerType


class TestDriveV5Rpc:
    """测试 DriveV5Rpc 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        rpc_params = RpcParams(
            enabled=True,
            class_path="test.path",
            conn_infos=[conn_info]
        )
        
        with patch('commons.auth.auth_rpc.AuthRequest') as mock_auth_request:
            mock_auth_request.return_value = Mock()
            self.rpc = DriveV5Rpc(rpc_params)

    def test_drive_v5_rpc_init(self):
        """测试 DriveV5Rpc 初始化"""
        expected_uri = "/api/v5/developer/files/{v5_file_id}/download?slave=true"
        assert self.rpc._download_uri == expected_uri
        assert len(self.rpc._reqs) == 1
        assert self.rpc._req == self.rpc._reqs[0]

    @pytest.mark.asyncio
    async def test_aget_download_url_success(self):
        """测试异步获取下载URL成功"""
        v5_file_id = "test_file_id"
        mock_response = {
            "result": "ok",
            "fileinfo": {
                "url": "https://test.com/download/file.pdf"
            }
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.aget_download_url(v5_file_id)
        
        assert result.is_success() is True
        assert isinstance(result.data, GetDownloadUrlData)
        assert result.data.url == "https://test.com/download/file.pdf"
        
        expected_uri = f"/api/v5/developer/files/{v5_file_id}/download?slave=true"
        self.rpc._req.async_call.assert_called_once_with("GET", expected_uri)

    def test_get_download_url_success(self):
        """测试同步获取下载URL成功"""
        v5_file_id = "test_file_id"
        mock_response = {
            "result": "ok",
            "fileinfo": {
                "url": "https://test.com/download/file.pdf"
            }
        }
        
        self.rpc._req.call = Mock(return_value=(200, json.dumps(mock_response)))
        
        result = self.rpc.get_download_url(v5_file_id)
        
        assert result.is_success() is True
        assert isinstance(result.data, GetDownloadUrlData)
        assert result.data.url == "https://test.com/download/file.pdf"
        
        expected_uri = f"/api/v5/developer/files/{v5_file_id}/download?slave=true"
        self.rpc._req.call.assert_called_once_with("GET", expected_uri)

    @pytest.mark.asyncio
    async def test_aget_download_url_api_error(self):
        """测试异步获取下载URL API错误"""
        v5_file_id = "test_file_id"
        mock_response = {
            "result": "error",
            "error": "File not found"
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.aget_download_url(v5_file_id)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    def test_get_download_url_api_error(self):
        """测试同步获取下载URL API错误"""
        v5_file_id = "test_file_id"
        mock_response = {
            "result": "error",
            "error": "File not found"
        }
        
        self.rpc._req.call = Mock(return_value=(200, json.dumps(mock_response)))
        
        result = self.rpc.get_download_url(v5_file_id)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_aget_download_url_missing_fileinfo(self):
        """测试异步获取下载URL缺少fileinfo字段"""
        v5_file_id = "test_file_id"
        mock_response = {
            "result": "ok"
            # 缺少 fileinfo 字段
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.aget_download_url(v5_file_id)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    def test_get_download_url_missing_fileinfo(self):
        """测试同步获取下载URL缺少fileinfo字段"""
        v5_file_id = "test_file_id"
        mock_response = {
            "result": "ok"
            # 缺少 fileinfo 字段
        }
        
        self.rpc._req.call = Mock(return_value=(200, json.dumps(mock_response)))
        
        result = self.rpc.get_download_url(v5_file_id)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_aget_download_url_null_fileinfo(self):
        """测试异步获取下载URL fileinfo为null"""
        v5_file_id = "test_file_id"
        mock_response = {
            "result": "ok",
            "fileinfo": None
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.aget_download_url(v5_file_id)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    def test_get_download_url_null_fileinfo(self):
        """测试同步获取下载URL fileinfo为null"""
        v5_file_id = "test_file_id"
        mock_response = {
            "result": "ok",
            "fileinfo": None
        }
        
        self.rpc._req.call = Mock(return_value=(200, json.dumps(mock_response)))
        
        result = self.rpc.get_download_url(v5_file_id)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_aget_download_url_http_error(self):
        """测试异步获取下载URL HTTP错误"""
        v5_file_id = "test_file_id"
        
        self.rpc._req.async_call = AsyncMock(return_value=(500, "Internal Server Error"))
        
        result = await self.rpc.aget_download_url(v5_file_id)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    def test_get_download_url_http_error(self):
        """测试同步获取下载URL HTTP错误"""
        v5_file_id = "test_file_id"
        
        self.rpc._req.call = Mock(return_value=(500, "Internal Server Error"))
        
        result = self.rpc.get_download_url(v5_file_id)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    @pytest.mark.asyncio
    async def test_aget_download_url_exception(self):
        """测试异步获取下载URL异常"""
        v5_file_id = "test_file_id"
        
        self.rpc._req.async_call = AsyncMock(side_effect=Exception("Connection error"))
        
        result = await self.rpc.aget_download_url(v5_file_id)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    def test_get_download_url_exception(self):
        """测试同步获取下载URL异常"""
        v5_file_id = "test_file_id"
        
        self.rpc._req.call = Mock(side_effect=Exception("Connection error"))
        
        result = self.rpc.get_download_url(v5_file_id)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    def test_drive_v5_rpc_inheritance(self):
        """测试 DriveV5Rpc 继承关系"""
        assert isinstance(self.rpc, RpcClient)
        # 检查是否实现了 YunRpcInterface 的方法
        methods = ['aget_download_url', 'get_download_url']
        for method in methods:
            assert hasattr(self.rpc, method)
            assert callable(getattr(self.rpc, method))
