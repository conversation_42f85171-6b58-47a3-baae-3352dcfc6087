# -*- coding: utf-8 -*-
"""
Unit tests for commons.rpc.monkey_ocr.aidocs_rpc module
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from commons.rpc.monkey_ocr.aidocs_rpc import AidocsMonkeyRpc
from commons.rpc.rpc_entity import RpcParams, ConnInfo, RpcResp
from commons.rpc.rpc_client import RpcClient
from commons.rpc.monkey_ocr.rpc_interface import MonkeyOcrData
from commons.auth.auth_rpc import SigVerType


class TestAidocsMonkeyRpc:
    """测试 AidocsMonkeyRpc 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        rpc_params = RpcParams(
            enabled=True,
            class_path="test.path",
            conn_infos=[conn_info]
        )
        
        with patch('commons.auth.auth_rpc.AuthRequest') as mock_auth_request:
            mock_auth_request.return_value = Mock()
            self.rpc = AidocsMonkeyRpc(rpc_params)

    def test_aidocs_monkey_rpc_init(self):
        """测试 AidocsMonkeyRpc 初始化"""
        assert self.rpc._monkey_ocr_uri == "/ocr/table"
        assert len(self.rpc._reqs) == 1
        assert self.rpc._req == self.rpc._reqs[0]

    @pytest.mark.asyncio
    async def test_arequest_ocr_success(self):
        """测试 OCR 请求成功"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "success": True,
            "content": "test text content",
            "task_type": "ocr",
            "message": "success"
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_ocr(pic_url)
        
        assert result.is_success() is True
        assert isinstance(result.data, MonkeyOcrData)
        self.rpc._req.async_call.assert_called_once_with("POST", "/ocr/table", body={"file": pic_url})

    @pytest.mark.asyncio
    async def test_arequest_ocr_api_error(self):
        """测试 OCR 请求 API 错误"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "success": False,
            "error": "API error"
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_ocr(pic_url)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_ocr_http_error(self):
        """测试 OCR 请求 HTTP 错误"""
        pic_url = "https://test.com/image.jpg"
        
        self.rpc._req.async_call = AsyncMock(return_value=(500, "Internal Server Error"))
        
        result = await self.rpc.arequest_ocr(pic_url)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    @pytest.mark.asyncio
    async def test_arequest_ocr_exception(self):
        """测试 OCR 请求异常"""
        pic_url = "https://test.com/image.jpg"
        
        self.rpc._req.async_call = AsyncMock(side_effect=Exception("Connection error"))
        
        result = await self.rpc.arequest_ocr(pic_url)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_ocr_missing_content(self):
        """测试 OCR 请求缺少 content 字段"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "success": True
            # 缺少 content 字段
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_ocr(pic_url)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_ocr_null_content(self):
        """测试 OCR 请求 content 为 null"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "success": True,
            "content": None
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_ocr(pic_url)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    def test_aidocs_monkey_rpc_inheritance(self):
        """测试 AidocsMonkeyRpc 继承关系"""
        assert isinstance(self.rpc, RpcClient)
        # 检查是否实现了 MonkeyOcrRpcInterface 的方法
        assert hasattr(self.rpc, 'arequest_ocr')
        assert callable(self.rpc.arequest_ocr)
