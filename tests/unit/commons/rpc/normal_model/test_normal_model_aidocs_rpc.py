# -*- coding: utf-8 -*-
"""
Unit tests for commons.rpc.normal_model.aidocs_rpc module
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from commons.rpc.normal_model.aidocs_rpc import AidocsNormalModelRpc
from commons.rpc.rpc_entity import RpcParams, ConnInfo, RpcResp
from commons.rpc.rpc_client import RpcClient
from commons.rpc.normal_model.rpc_interface import (
    TextEmbeddingData, LayOutData, OcrData, TableClsData, 
    WiredTableOcrData, WirelessTableOcrData, RerankerData
)
from commons.auth.auth_rpc import SigVerType


class TestAidocsNormalModelRpc:
    """测试 AidocsNormalModelRpc 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        conn_info = ConnInfo(
            host="test.com",
            ak="test_ak",
            sk="test_sk",
            sig_type=SigVerType.wps2
        )
        rpc_params = RpcParams(
            enabled=True,
            class_path="test.path",
            conn_infos=[conn_info]
        )
        
        with patch('commons.auth.auth_rpc.AuthRequest') as mock_auth_request:
            mock_auth_request.return_value = Mock()
            self.rpc = AidocsNormalModelRpc(rpc_params)

    def test_aidocs_normal_model_rpc_init(self):
        """测试 AidocsNormalModelRpc 初始化"""
        assert self.rpc._layout_uri == "/api/v1/ocr/doc_layout_3cls"
        assert self.rpc._orc_uri == "/api/v1/ocr/ocr_pipeline"
        assert self.rpc._table_cls_uri == "/api/v1/ocr/table_cls"
        assert self.rpc._wired_table_ocr_uri == "/api/v1/ocr/wired_ocr"
        assert self.rpc._wireless_table_ocr_uri == "/api/v1/ocr/wireless_table"
        assert self.rpc._text_embedding_uri == "/api/v1/text/vector"
        assert self.rpc._text_reranker_uri == "/api/v1/text/reranker"
        assert len(self.rpc._reqs) == 1
        assert self.rpc._req == self.rpc._reqs[0]

    @pytest.mark.asyncio
    async def test_arequest_layout_model_success(self):
        """测试布局模型请求成功"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "code": 0,
            "data": {
                "boxes": [
                    {
                        "cls_id": 1,
                        "label": "text",
                        "score": 0.95,
                        "coordinate": [10.0, 20.0, 100.0, 200.0]
                    }
                ]
            }
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_layout_model(pic_url)
        
        assert result.is_success() is True
        assert isinstance(result.data, LayOutData)
        self.rpc._req.async_call.assert_called_once_with("POST", "/api/v1/ocr/doc_layout_3cls", body={"image_url": pic_url})

    @pytest.mark.asyncio
    async def test_arequest_ocr_success(self):
        """测试 OCR 请求成功"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "code": 0,
            "data": {
                "rec_texts": ["test text 1", "test text 2"]
            }
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_ocr(pic_url)
        
        assert result.is_success() is True
        assert isinstance(result.data, OcrData)
        self.rpc._req.async_call.assert_called_once_with("POST", "/api/v1/ocr/ocr_pipeline", body={"image_url": pic_url}, timeout=30.0)

    @pytest.mark.asyncio
    async def test_arequest_table_cls_success(self):
        """测试表格分类请求成功"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "code": 0,
            "data": {
                "table_type": "wired"
            }
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_table_cls(pic_url)
        
        assert result.is_success() is True
        assert isinstance(result.data, TableClsData)
        self.rpc._req.async_call.assert_called_once_with("POST", "/api/v1/ocr/table_cls", body={"image_url": pic_url})

    @pytest.mark.asyncio
    async def test_arequest_table_cls_api_error(self):
        """测试表格分类 API 错误"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "code": 1,
            "error": "API error"
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_table_cls(pic_url)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_table_cls_http_error(self):
        """测试表格分类 HTTP 错误"""
        pic_url = "https://test.com/image.jpg"
        
        self.rpc._req.async_call = AsyncMock(return_value=(500, "Internal Server Error"))
        
        result = await self.rpc.arequest_table_cls(pic_url)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    @pytest.mark.asyncio
    async def test_arequest_table_cls_exception(self):
        """测试表格分类异常"""
        pic_url = "https://test.com/image.jpg"
        
        self.rpc._req.async_call = AsyncMock(side_effect=Exception("Connection error"))
        
        result = await self.rpc.arequest_table_cls(pic_url)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_wired_table_success(self):
        """测试有线表格请求成功"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "code": 0,
            "data": {
                "table_html": "<table><tr><td>test</td></tr></table>"
            }
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_wired_table(pic_url)
        
        assert result.is_success() is True
        assert isinstance(result.data, WiredTableOcrData)
        self.rpc._req.async_call.assert_called_once_with("POST", "/api/v1/ocr/wired_ocr", body={"image_url": pic_url})

    @pytest.mark.asyncio
    async def test_arequest_wired_table_api_error(self):
        """测试有线表格 API 错误"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "code": 1,
            "error": "API error"
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_wired_table(pic_url)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_wired_table_http_error(self):
        """测试有线表格 HTTP 错误"""
        pic_url = "https://test.com/image.jpg"
        
        self.rpc._req.async_call = AsyncMock(return_value=(500, "Internal Server Error"))
        
        result = await self.rpc.arequest_wired_table(pic_url)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    @pytest.mark.asyncio
    async def test_arequest_wired_table_exception(self):
        """测试有线表格异常"""
        pic_url = "https://test.com/image.jpg"
        
        self.rpc._req.async_call = AsyncMock(side_effect=Exception("Connection error"))
        
        result = await self.rpc.arequest_wired_table(pic_url)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_wireless_table_success(self):
        """测试无线表格请求成功"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "code": 0,
            "data": {
                "table_html": "<table><tr><td>test</td></tr></table>"
            }
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_wireless_table(pic_url)
        
        assert result.is_success() is True
        assert isinstance(result.data, WirelessTableOcrData)
        self.rpc._req.async_call.assert_called_once_with("POST", "/api/v1/ocr/wireless_table", body={"image_url": pic_url})

    @pytest.mark.asyncio
    async def test_arequest_wireless_table_api_error(self):
        """测试无线表格 API 错误"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "code": 1,
            "error": "API error"
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_wireless_table(pic_url)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_wireless_table_http_error(self):
        """测试无线表格 HTTP 错误"""
        pic_url = "https://test.com/image.jpg"
        
        self.rpc._req.async_call = AsyncMock(return_value=(500, "Internal Server Error"))
        
        result = await self.rpc.arequest_wireless_table(pic_url)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    @pytest.mark.asyncio
    async def test_arequest_wireless_table_exception(self):
        """测试无线表格异常"""
        pic_url = "https://test.com/image.jpg"
        
        self.rpc._req.async_call = AsyncMock(side_effect=Exception("Connection error"))
        
        result = await self.rpc.arequest_wireless_table(pic_url)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_text_embedding_success(self):
        """测试文本嵌入请求成功"""
        text = "test text"
        mock_response = {
            "code": 0,
            "data": {
                "embedding": [0.1, 0.2, 0.3]
            }
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_text_embedding(text)
        
        assert result.is_success() is True
        assert isinstance(result.data, TextEmbeddingData)
        assert result.data.embedding == [0.1, 0.2, 0.3]
        self.rpc._req.async_call.assert_called_once_with("POST", "/api/v1/text/vector", body={"text": text})

    @pytest.mark.asyncio
    async def test_arequest_text_embedding_api_error(self):
        """测试文本嵌入 API 错误"""
        text = "test text"
        mock_response = {
            "code": 1,
            "error": "API error"
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_text_embedding(text)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_text_embedding_http_error(self):
        """测试文本嵌入 HTTP 错误"""
        text = "test text"
        
        self.rpc._req.async_call = AsyncMock(return_value=(500, "Internal Server Error"))
        
        result = await self.rpc.arequest_text_embedding(text)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    @pytest.mark.asyncio
    async def test_arequest_text_embedding_exception(self):
        """测试文本嵌入异常"""
        text = "test text"
        
        self.rpc._req.async_call = AsyncMock(side_effect=Exception("Connection error"))
        
        result = await self.rpc.arequest_text_embedding(text)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_rerank_success(self):
        """测试重排序请求成功"""
        query = "test query"
        texts = ["text1", "text2", "text3"]
        mock_response = {
            "code": 0,
            "data": {
                "reranker_list": [
                    {
                        "text_a": query,
                        "text_b": "text1",
                        "score": 0.9,
                        "idx": 0
                    }
                ]
            }
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_rerank(query, texts)
        
        assert result.is_success() is True
        assert isinstance(result.data, RerankerData)
        expected_body = {"query": query, "texts": texts}
        self.rpc._req.async_call.assert_called_once_with("POST", "/api/v1/text/reranker", body=expected_body)

    @pytest.mark.asyncio
    async def test_arequest_rerank_with_threshold(self):
        """测试带阈值的重排序请求"""
        query = "test query"
        texts = ["text1", "text2"]
        threshold = 0.5
        mock_response = {
            "code": 0,
            "data": {
                "reranker_list": []
            }
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_rerank(query, texts, threshold)
        
        assert result.is_success() is True
        expected_body = {"query": query, "texts": texts, "threshold": threshold}
        self.rpc._req.async_call.assert_called_once_with("POST", "/api/v1/text/reranker", body=expected_body)

    @pytest.mark.asyncio
    async def test_arequest_rerank_api_error(self):
        """测试重排序 API 错误"""
        query = "test query"
        texts = ["text1", "text2"]
        mock_response = {
            "code": 1,
            "error": "API error"
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_rerank(query, texts)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_rerank_http_error(self):
        """测试重排序 HTTP 错误"""
        query = "test query"
        texts = ["text1", "text2"]
        
        self.rpc._req.async_call = AsyncMock(return_value=(500, "Internal Server Error"))
        
        result = await self.rpc.arequest_rerank(query, texts)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    @pytest.mark.asyncio
    async def test_arequest_rerank_exception(self):
        """测试重排序异常"""
        query = "test query"
        texts = ["text1", "text2"]
        
        self.rpc._req.async_call = AsyncMock(side_effect=Exception("Connection error"))
        
        result = await self.rpc.arequest_rerank(query, texts)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_api_error(self):
        """测试 API 错误"""
        pic_url = "https://test.com/image.jpg"
        mock_response = {
            "code": 1,
            "error": "API error"
        }
        
        self.rpc._req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_layout_model(pic_url)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_http_error(self):
        """测试 HTTP 错误"""
        pic_url = "https://test.com/image.jpg"
        
        self.rpc._req.async_call = AsyncMock(return_value=(500, "Internal Server Error"))
        
        result = await self.rpc.arequest_layout_model(pic_url)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    @pytest.mark.asyncio
    async def test_arequest_exception(self):
        """测试异常处理"""
        pic_url = "https://test.com/image.jpg"
        
        self.rpc._req.async_call = AsyncMock(side_effect=Exception("Connection error"))
        
        result = await self.rpc.arequest_layout_model(pic_url)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    def test_aidocs_normal_model_rpc_inheritance(self):
        """测试 AidocsNormalModelRpc 继承关系"""
        assert isinstance(self.rpc, RpcClient)
        # 检查是否实现了 NormalRpcInterface 的方法
        methods = [
            'arequest_layout_model',
            'arequest_ocr',
            'arequest_table_cls',
            'arequest_wired_table',
            'arequest_wireless_table',
            'arequest_text_embedding',
            'arequest_rerank'
        ]
        for method in methods:
            assert hasattr(self.rpc, method)
            assert callable(getattr(self.rpc, method))
