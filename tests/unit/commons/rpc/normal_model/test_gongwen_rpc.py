# -*- coding: utf-8 -*-
"""
Unit tests for commons.rpc.normal_model.gongwen_rpc module
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from commons.rpc.normal_model.gongwen_rpc import GongwenRpc
from commons.rpc.rpc_entity import RpcParams, ConnInfo, RpcResp
from commons.rpc.rpc_client import RpcClient
from commons.rpc.normal_model.rpc_interface import (
    TextEmbeddingData, LayOutData, OcrData, TableClsData, 
    WiredTableOcrData, WirelessTableOcrData, RerankerData, RerankerItem
)
from commons.auth.auth_rpc import SigVerType


class TestGongwenRpc:
    """测试 GongwenRpc 类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        conn_info1 = ConnInfo(
            host="test1.com",
            ak="test_ak1",
            sk="test_sk1",
            sig_type=SigVerType.wps2
        )
        conn_info2 = ConnInfo(
            host="test2.com",
            ak="test_ak2",
            sk="test_sk2",
            sig_type=SigVerType.wps2
        )
        rpc_params = RpcParams(
            enabled=True,
            class_path="test.path",
            conn_infos=[conn_info1, conn_info2]
        )
        
        with patch('commons.auth.auth_rpc.AuthRequest') as mock_auth_request:
            mock_auth_request.return_value = Mock()
            self.rpc = GongwenRpc(rpc_params)

    def test_gongwen_rpc_init(self):
        """测试 GongwenRpc 初始化"""
        assert self.rpc._text_embedding_uri == "/algorithm"
        assert self.rpc._text_reranker_uri == "/algorithm"
        assert len(self.rpc._reqs) == 2
        assert self.rpc._emb_req == self.rpc._reqs[0]
        assert self.rpc._rerank_req == self.rpc._reqs[1]

    @pytest.mark.asyncio
    async def test_arequest_text_embedding_success(self):
        """测试文本嵌入请求成功"""
        text = "test text"
        mock_response = {
            "code": 200,
            "data": [0.1, 0.2, 0.3]
        }
        
        self.rpc._emb_req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_text_embedding(text)
        
        assert result.is_success() is True
        assert isinstance(result.data, TextEmbeddingData)
        assert result.data.embedding == [0.1, 0.2, 0.3]
        
        expected_body = {
            "contents": [text],
            "algorithm_type": "embedding_bge",
            "is_search": False
        }
        self.rpc._emb_req.async_call.assert_called_once_with("POST", "/algorithm", body=expected_body)

    @pytest.mark.asyncio
    async def test_arequest_rerank_success(self):
        """测试重排序请求成功"""
        query = "test query"
        texts = ["text1", "text2", "text3"]
        mock_response = {
            "code": 200,
            "data": [0.8, 0.6, 0.9]
        }
        
        self.rpc._rerank_req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_rerank(query, texts)
        
        assert result.is_success() is True
        assert isinstance(result.data, RerankerData)
        assert len(result.data.reranker_list) == 3
        
        # 检查排序是否正确（按分数降序）
        assert result.data.reranker_list[0].score == 0.9
        assert result.data.reranker_list[1].score == 0.8
        assert result.data.reranker_list[2].score == 0.6
        
        expected_body = {
            "contents": [[query, "text1"], [query, "text2"], [query, "text3"]],
            "algorithm_type": "reranker"
        }
        self.rpc._rerank_req.async_call.assert_called_once_with("POST", "/algorithm", body=expected_body)

    @pytest.mark.asyncio
    async def test_arequest_text_embedding_api_error(self):
        """测试文本嵌入 API 错误"""
        text = "test text"
        mock_response = {
            "code": 400,
            "error": "API error"
        }
        
        self.rpc._emb_req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_text_embedding(text)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_rerank_api_error(self):
        """测试重排序 API 错误"""
        query = "test query"
        texts = ["text1", "text2"]
        mock_response = {
            "code": 400,
            "error": "API error"
        }
        
        self.rpc._rerank_req.async_call = AsyncMock(return_value=(200, json.dumps(mock_response)))
        
        result = await self.rpc.arequest_rerank(query, texts)
        
        assert result.is_success() is False
        assert "API response error" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_text_embedding_http_error(self):
        """测试文本嵌入 HTTP 错误"""
        text = "test text"
        
        self.rpc._emb_req.async_call = AsyncMock(return_value=(500, "Internal Server Error"))
        
        result = await self.rpc.arequest_text_embedding(text)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    @pytest.mark.asyncio
    async def test_arequest_rerank_http_error(self):
        """测试重排序 HTTP 错误"""
        query = "test query"
        texts = ["text1", "text2"]
        
        self.rpc._rerank_req.async_call = AsyncMock(return_value=(500, "Internal Server Error"))
        
        result = await self.rpc.arequest_rerank(query, texts)
        
        assert result.is_success() is False
        assert "HTTP error" in result.error_message
        assert result.status_code == 500

    @pytest.mark.asyncio
    async def test_arequest_text_embedding_exception(self):
        """测试文本嵌入异常"""
        text = "test text"
        
        self.rpc._emb_req.async_call = AsyncMock(side_effect=Exception("Connection error"))
        
        result = await self.rpc.arequest_text_embedding(text)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    @pytest.mark.asyncio
    async def test_arequest_rerank_exception(self):
        """测试重排序异常"""
        query = "test query"
        texts = ["text1", "text2"]
        
        self.rpc._rerank_req.async_call = AsyncMock(side_effect=Exception("Connection error"))
        
        result = await self.rpc.arequest_rerank(query, texts)
        
        assert result.is_success() is False
        assert "Request failed" in result.error_message

    def test_gongwen_rpc_inheritance(self):
        """测试 GongwenRpc 继承关系"""
        assert isinstance(self.rpc, RpcClient)
        # 检查是否实现了 NormalRpcInterface 的方法
        methods = [
            'arequest_layout_model',
            'arequest_ocr',
            'arequest_table_cls',
            'arequest_wired_table',
            'arequest_wireless_table',
            'arequest_text_embedding',
            'arequest_rerank'
        ]
        for method in methods:
            assert hasattr(self.rpc, method)
            assert callable(getattr(self.rpc, method))
