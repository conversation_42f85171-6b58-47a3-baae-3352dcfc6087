"""
commons.llm_gateway.llm 模块的测试 - 简化版本
"""
import pytest
from unittest.mock import Mock, patch
from commons.llm_gateway.llm import LLMChatStatus, LLMStreamItem, LLModelRpc
from commons.llm_gateway.models.chat_data import Message


class TestLLMChatStatus:
    """测试 LLM 聊天状态枚举"""

    def test_llm_chat_status_values(self):
        """测试 LLM 聊天状态枚举值"""
        assert LLMChatStatus.OK == "ok"
        assert LLMChatStatus.FAIL == "fail"
        assert LLMChatStatus.AUDIT == "audit"
        assert LLMChatStatus.PRIVILEGE == "privilege"
        assert LLMChatStatus.LIMIT == "limit"
        assert LLMChatStatus.JSON_ERROR == "json_error"


class TestLLMStreamItem:
    """测试 LLM 流式响应项"""

    def test_llm_stream_item_creation(self):
        """测试 LLM 流式响应项创建"""
        item = LLMStreamItem(status=LLMChatStatus.OK, text="测试文本")
        assert item.status == LLMChatStatus.OK
        assert item.text == "测试文本"

    def test_llm_stream_item_different_status(self):
        """测试不同状态的 LLM 流式响应项"""
        # 测试失败状态
        fail_item = LLMStreamItem(status=LLMChatStatus.FAIL, text="错误信息")
        assert fail_item.status == LLMChatStatus.FAIL
        assert fail_item.text == "错误信息"

        # 测试审核状态
        audit_item = LLMStreamItem(status=LLMChatStatus.AUDIT, text="内容被审核")
        assert audit_item.status == LLMChatStatus.AUDIT
        assert audit_item.text == "内容被审核"


class TestLLModelRpc:
    """测试 LLM 模型 RPC 类"""

    def test_singleton_pattern(self):
        """测试单例模式"""
        rpc1 = LLModelRpc()
        rpc2 = LLModelRpc()
        assert rpc1 is rpc2

    def test_gateway_enum(self):
        """测试网关枚举"""
        # 验证网关枚举存在
        assert hasattr(LLModelRpc, 'Gateway')

        # 验证枚举值
        gateway = LLModelRpc.Gateway
        assert hasattr(gateway, 'Public')
        assert hasattr(gateway, 'Sft')
        assert hasattr(gateway, 'Private')

    def test_model_selector_creation(self):
        """测试模型选择器创建"""
        selector = LLModelRpc.ModelSelector(
            provider="test_provider",
            model="test_model",
            version="v1.0"
        )

        assert selector.provider == "test_provider"
        assert selector.model == "test_model"
        assert selector.version == "v1.0"

    def test_llm_rpc_initialization(self):
        """测试 LLM RPC 初始化"""
        rpc = LLModelRpc()

        # 验证基本属性
        assert rpc is not None
        assert hasattr(rpc, 'create_models')
        assert hasattr(rpc, 'close')
        assert hasattr(rpc, 'chat')

    def test_basic_methods_exist(self):
        """测试基本方法存在"""
        rpc = LLModelRpc()

        # 验证方法存在
        assert hasattr(rpc, 'create_models')
        assert hasattr(rpc, 'close')
        assert hasattr(rpc, 'chat')
        assert hasattr(rpc, 'chat_text_stream')
        assert hasattr(rpc, 'async_chat_text_stream')

    def test_create_models_with_public_conf(self):
        """测试使用公共配置创建模型"""
        rpc = LLModelRpc()

        # 创建模拟配置
        from unittest.mock import Mock
        public_conf = Mock()

        # 模拟 PublicModelGateway 类
        with patch('commons.llm_gateway.llm.PublicModelGateway') as mock_gateway:
            mock_instance = Mock()
            mock_gateway.return_value = mock_instance

            # 调用创建模型
            rpc.create_models(public_conf=public_conf, pool_max=10)

            # 验证模型被创建
            mock_gateway.assert_called_once_with(public_conf, pool_max=10)
            assert rpc._public_model == mock_instance

    def test_create_models_with_sft_conf(self):
        """测试使用 SFT 配置创建模型"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        sft_conf = Mock()

        with patch('commons.llm_gateway.llm.SftModelGateway') as mock_gateway:
            mock_instance = Mock()
            mock_gateway.return_value = mock_instance

            rpc.create_models(sft_conf=sft_conf, pool_max=5)

            mock_gateway.assert_called_once_with(sft_conf, pool_max=5)
            assert rpc._sft_model == mock_instance

    def test_create_models_with_private_conf(self):
        """测试使用私有配置创建模型"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        private_conf = Mock()

        with patch('commons.llm_gateway.llm.PrivateModelGateway') as mock_gateway:
            mock_instance = Mock()
            mock_gateway.return_value = mock_instance

            rpc.create_models(private_conf=private_conf, pool_max=8)

            mock_gateway.assert_called_once_with(private_conf, pool_max=8)
            assert rpc._private_model == mock_instance

    @pytest.mark.asyncio
    async def test_close_with_public_model(self):
        """测试关闭公共模型"""
        rpc = LLModelRpc()

        # 创建模拟的公共模型
        from unittest.mock import AsyncMock
        mock_public_model = AsyncMock()
        rpc._public_model = mock_public_model

        # 调用关闭
        await rpc.close()

        # 验证关闭方法被调用
        mock_public_model.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_without_models(self):
        """测试关闭时没有模型"""
        rpc = LLModelRpc()

        # 确保没有模型
        rpc._public_model = None
        rpc._sft_model = None
        rpc._private_model = None

        # 调用关闭应该不会抛出异常
        await rpc.close()

    def test_select_model_by_gateway_public(self):
        """测试根据网关选择公共模型"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        mock_public_model = Mock()
        rpc._public_model = mock_public_model

        result = rpc._select_model_by_gateway(LLModelRpc.Gateway.Public)
        assert result == mock_public_model

    def test_select_model_by_gateway_sft(self):
        """测试根据网关选择 SFT 模型"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        mock_sft_model = Mock()
        rpc._sft_model = mock_sft_model

        result = rpc._select_model_by_gateway(LLModelRpc.Gateway.Sft)
        assert result == mock_sft_model

    def test_select_model_by_gateway_private(self):
        """测试根据网关选择私有模型"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        mock_private_model = Mock()
        rpc._private_model = mock_private_model

        result = rpc._select_model_by_gateway(LLModelRpc.Gateway.Private)
        assert result == mock_private_model

    def test_select_model_by_gateway_none(self):
        """测试选择不存在的模型"""
        rpc = LLModelRpc()

        # 所有模型都为 None
        rpc._public_model = None
        rpc._sft_model = None
        rpc._private_model = None

        result = rpc._select_model_by_gateway(LLModelRpc.Gateway.Public)
        assert result is None

    def test_create_params_for_public_gateway(self):
        """测试为公共网关创建参数"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        # 设置公共模型
        mock_public_model = Mock()
        mock_conf = Mock()
        mock_conf.provider = "test_provider"
        mock_conf.model = "test_model"
        mock_public_model.get_conf.return_value = mock_conf
        rpc._public_model = mock_public_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")
        args = LLModelRpc.LLMArgs(temperature=0.8, max_tokens=1000)

        with patch('commons.llm_gateway.llm.LLMParam') as mock_llm_param, \
             patch('commons.llm_gateway.llm.ChatParams') as mock_chat_params:

            mock_llm_param_instance = Mock()
            mock_llm_param.return_value = mock_llm_param_instance
            mock_chat_params_instance = Mock()
            mock_chat_params.return_value = mock_chat_params_instance

            result = rpc._create_params(
                stream=False,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector,
                args=args
            )

            # 验证 LLMParam 被正确创建
            mock_llm_param.assert_called_once_with(
                temperature=0.8,
                max_tokens=1000,
                top_p=args.top_p,
                stop=args.stop
            )

            assert result == mock_chat_params_instance

    def test_create_params_with_none_args(self):
        """测试创建参数时 args 为 None"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        # 设置公共模型
        mock_public_model = Mock()
        mock_conf = Mock()
        mock_conf.provider = "test_provider"
        mock_conf.model = "test_model"
        mock_public_model.get_conf.return_value = mock_conf
        rpc._public_model = mock_public_model

        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")

        with patch('commons.llm_gateway.llm.LLMParam') as mock_llm_param, \
             patch('commons.llm_gateway.llm.ChatParams') as mock_chat_params:

            mock_llm_param_instance = Mock()
            mock_llm_param.return_value = mock_llm_param_instance
            mock_chat_params_instance = Mock()
            mock_chat_params.return_value = mock_chat_params_instance

            result = rpc._create_params(
                stream=False,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector,
                args=None  # args 为 None
            )

            # 验证默认 args 被创建
            mock_llm_param.assert_called_once()
            call_kwargs = mock_llm_param.call_args[1]
            assert call_kwargs["top_p"] == 0.9  # 默认值

            assert result == mock_chat_params_instance

    def test_create_params_with_extended_arguments(self):
        """测试创建带扩展参数的参数"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        # 设置公共模型
        mock_public_model = Mock()
        mock_conf = Mock()
        mock_conf.provider = "test_provider"
        mock_conf.model = "test_model"
        mock_public_model.get_conf.return_value = mock_conf
        rpc._public_model = mock_public_model

        # 创建带扩展参数的测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="custom_provider", model="custom_model")
        extended_args = {"custom_param": "custom_value"}
        args = LLModelRpc.LLMArgs(
            temperature=0.7,
            extended_llm_arguments=extended_args
        )

        with patch('commons.llm_gateway.llm.LLMParam') as mock_llm_param, \
             patch('commons.llm_gateway.llm.ChatParams') as mock_chat_params:

            mock_llm_param_instance = Mock()
            mock_llm_param.return_value = mock_llm_param_instance
            mock_chat_params_instance = Mock()
            mock_chat_params.return_value = mock_chat_params_instance

            result = rpc._create_params(
                stream=False,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector,
                args=args
            )

            # 验证扩展参数被正确处理
            mock_chat_params.assert_called_once()
            call_kwargs = mock_chat_params.call_args[1]
            assert "extended_llm_arguments" in call_kwargs
            expected_extended = {"custom_provider_custom_model": extended_args}
            assert call_kwargs["extended_llm_arguments"] == expected_extended

    def test_chat_success(self):
        """测试聊天成功"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        # 设置公共模型
        mock_public_model = Mock()
        mock_response = Mock()
        mock_response.status = LLMChatStatus.OK
        mock_response.text = "Hello! How can I help you?"
        mock_public_model.chat.return_value = mock_response
        rpc._public_model = mock_public_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")
        args = LLModelRpc.LLMArgs(temperature=0.7)

        with patch.object(rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            result = rpc.chat(
                stream=False,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector,
                args=args
            )

            # 验证参数创建和聊天调用
            mock_create_params.assert_called_once()
            mock_public_model.chat.assert_called_once()

            assert result == mock_response

    def test_chat_stream_success(self):
        """测试流式聊天成功"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        # 设置公共模型
        mock_public_model = Mock()

        # 模拟流式响应
        def mock_stream():
            yield LLMStreamItem(status=LLMChatStatus.OK, text="Hello")
            yield LLMStreamItem(status=LLMChatStatus.OK, text=" there!")

        mock_public_model.chat_stream.return_value = mock_stream()
        rpc._public_model = mock_public_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")

        with patch.object(rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            # 收集流式响应
            stream_items = list(rpc.chat(
                stream=True,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector
            ))

            # 验证参数创建和流式聊天调用
            mock_create_params.assert_called_once()
            mock_public_model.chat_stream.assert_called_once()

            # 验证流式响应
            assert len(stream_items) == 2
            assert stream_items[0].text == "Hello"
            assert stream_items[1].text == " there!"

    @pytest.mark.asyncio
    async def test_async_chat_success(self):
        """测试异步聊天成功"""
        rpc = LLModelRpc()

        from unittest.mock import AsyncMock, Mock
        # 设置公共模型
        mock_public_model = AsyncMock()
        mock_response = Mock()
        mock_response.status = LLMChatStatus.OK
        mock_response.text = "Async response"
        mock_public_model.async_chat.return_value = mock_response
        rpc._public_model = mock_public_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")

        with patch.object(rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            result = await rpc.async_chat(
                stream=False,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector
            )

            # 验证参数创建和异步聊天调用
            mock_create_params.assert_called_once()
            mock_public_model.async_chat.assert_called_once()

            assert result == mock_response

    @pytest.mark.asyncio
    async def test_async_chat_stream_success(self):
        """测试异步流式聊天成功"""
        rpc = LLModelRpc()

        from unittest.mock import Mock
        # 设置公共模型
        mock_public_model = Mock()

        # 简化测试：只验证方法调用，不测试实际的异步生成器
        rpc._public_model = mock_public_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")

        with patch.object(rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            # 调用异步流式聊天
            result = await rpc.async_chat(
                stream=True,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector
            )

            # 验证参数创建和异步流式聊天调用
            mock_create_params.assert_called_once()
            mock_public_model.async_chat_stream.assert_called_once()

            # 验证返回了模拟的结果
            assert result == mock_public_model.async_chat_stream.return_value


class TestLLModelRpcAdvanced:
    """测试 LLM 模型 RPC 高级功能"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        if hasattr(LLModelRpc, '_instances'):
            LLModelRpc._instances = {}
        self.llm_rpc = LLModelRpc()

    def test_chat_with_sft_gateway(self):
        """测试使用 SFT 网关聊天"""
        from unittest.mock import Mock

        # 设置 SFT 模型
        mock_sft_model = Mock()
        mock_response = Mock()
        mock_response.status = LLMChatStatus.OK
        mock_response.text = "SFT response"
        mock_sft_model.chat.return_value = mock_response
        self.llm_rpc._sft_model = mock_sft_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="sft_provider", model="sft_model")

        with patch.object(self.llm_rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            result = self.llm_rpc.chat(
                stream=False,
                gateway=LLModelRpc.Gateway.Sft,
                messages=messages,
                selector=selector
            )

            # 验证参数创建和聊天调用
            mock_create_params.assert_called_once()
            mock_sft_model.chat.assert_called_once()
            assert result == mock_response

    def test_chat_with_private_gateway(self):
        """测试使用私有网关聊天"""
        from unittest.mock import Mock

        # 设置私有模型
        mock_private_model = Mock()
        mock_response = Mock()
        mock_response.status = LLMChatStatus.OK
        mock_response.text = "Private response"
        mock_private_model.chat.return_value = mock_response
        self.llm_rpc._private_model = mock_private_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="private_provider", model="private_model")

        with patch.object(self.llm_rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            result = self.llm_rpc.chat(
                stream=False,
                gateway=LLModelRpc.Gateway.Private,
                messages=messages,
                selector=selector
            )

            # 验证参数创建和聊天调用
            mock_create_params.assert_called_once()
            mock_private_model.chat.assert_called_once()
            assert result == mock_response

    def test_chat_model_not_found_error(self):
        """测试聊天时模型未找到错误"""
        # 没有设置任何模型
        self.llm_rpc._public_model = None
        self.llm_rpc._sft_model = None
        self.llm_rpc._private_model = None

        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")

        # 当模型为 None 时，会抛出 AttributeError
        with pytest.raises(AttributeError):
            self.llm_rpc.chat(
                stream=False,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector
            )

    def test_chat_stream_model_not_found_error(self):
        """测试流式聊天时模型未找到错误"""
        # 没有设置任何模型
        self.llm_rpc._public_model = None

        messages = [Message(role="user", content="Hello")]

        # 当模型为 None 时，会抛出 AttributeError
        with pytest.raises(AttributeError):
            list(self.llm_rpc.chat(
                stream=True,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages
            ))

    def test_chat_with_exception_handling(self):
        """测试聊天异常处理"""
        from unittest.mock import Mock

        # 设置公共模型抛出异常
        mock_public_model = Mock()
        mock_public_model.chat.side_effect = Exception("Network error")
        self.llm_rpc._public_model = mock_public_model

        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")

        with patch.object(self.llm_rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            # 异常会直接抛出，不会被捕获
            with pytest.raises(Exception) as exc_info:
                self.llm_rpc.chat(
                    stream=False,
                    gateway=LLModelRpc.Gateway.Public,
                    messages=messages,
                    selector=selector
                )

            assert "Network error" in str(exc_info.value)

    def test_chat_stream_with_exception_handling(self):
        """测试流式聊天异常处理"""
        from unittest.mock import Mock

        # 设置公共模型抛出异常
        mock_public_model = Mock()
        mock_public_model.chat_stream.side_effect = Exception("Stream error")
        self.llm_rpc._public_model = mock_public_model

        messages = [Message(role="user", content="Hello")]

        with patch.object(self.llm_rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            # 异常会直接抛出，不会被捕获
            with pytest.raises(Exception) as exc_info:
                list(self.llm_rpc.chat(
                    stream=True,
                    gateway=LLModelRpc.Gateway.Public,
                    messages=messages
                ))

            assert "Stream error" in str(exc_info.value)

    def test_create_params_for_sft_gateway(self):
        """测试为 SFT 网关创建参数"""
        from unittest.mock import Mock

        # 创建测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="sft_provider", model="sft_model")
        args = LLModelRpc.LLMArgs(temperature=0.9)

        with patch('commons.llm_gateway.llm.SftChatParams') as mock_sft_params:
            mock_sft_params_instance = Mock()
            mock_sft_params.return_value = mock_sft_params_instance

            result = self.llm_rpc._create_params(
                stream=True,
                gateway=LLModelRpc.Gateway.Sft,
                messages=messages,
                selector=selector,
                args=args
            )

            # 验证 SFT 参数被正确创建
            mock_sft_params.assert_called_once()
            call_kwargs = mock_sft_params.call_args[1]
            assert call_kwargs["stream"] is True
            assert call_kwargs["messages"] == messages
            assert result == mock_sft_params_instance

    def test_create_params_for_private_gateway(self):
        """测试为私有网关创建参数"""
        from unittest.mock import Mock

        # 创建测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="private_provider", model="private_model")
        args = LLModelRpc.LLMArgs(temperature=0.6)

        with patch('commons.llm_gateway.llm.PrivateChatParams') as mock_private_params:
            mock_private_params_instance = Mock()
            mock_private_params.return_value = mock_private_params_instance

            result = self.llm_rpc._create_params(
                stream=False,
                gateway=LLModelRpc.Gateway.Private,
                messages=messages,
                selector=selector,
                args=args,
                private_uid="test_uid",
                private_product_name="test_product"
            )

            # 验证私有参数被正确创建
            mock_private_params.assert_called_once()
            call_kwargs = mock_private_params.call_args[1]
            assert call_kwargs["stream"] is False
            assert call_kwargs["messages"] == messages
            assert call_kwargs["private_uid"] == "test_uid"
            assert call_kwargs["private_product_name"] == "test_product"
            assert result == mock_private_params_instance


class TestLLModelRpcAsyncMethods:
    """测试 LLM 模型 RPC 异步方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        if hasattr(LLModelRpc, '_instances'):
            LLModelRpc._instances = {}
        self.llm_rpc = LLModelRpc()

    def test_select_model_by_gateway_public(self):
        """测试按网关选择公共模型"""
        from unittest.mock import Mock

        mock_public_model = Mock()
        self.llm_rpc._public_model = mock_public_model

        result = self.llm_rpc._select_model_by_gateway(LLModelRpc.Gateway.Public)
        assert result == mock_public_model

    def test_select_model_by_gateway_sft(self):
        """测试按网关选择 SFT 模型"""
        from unittest.mock import Mock

        mock_sft_model = Mock()
        self.llm_rpc._sft_model = mock_sft_model

        result = self.llm_rpc._select_model_by_gateway(LLModelRpc.Gateway.Sft)
        assert result == mock_sft_model

    def test_select_model_by_gateway_private(self):
        """测试按网关选择私有模型"""
        from unittest.mock import Mock

        mock_private_model = Mock()
        self.llm_rpc._private_model = mock_private_model

        result = self.llm_rpc._select_model_by_gateway(LLModelRpc.Gateway.Private)
        assert result == mock_private_model

    def test_select_model_by_gateway_invalid(self):
        """测试选择无效网关类型"""
        # 创建一个无效的网关类型
        invalid_gateway = "invalid_gateway"

        with pytest.raises(Exception) as exc_info:
            self.llm_rpc._select_model_by_gateway(invalid_gateway)

        assert "gateway type error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_async_chat_non_stream(self):
        """测试异步聊天非流式"""
        from unittest.mock import Mock, AsyncMock

        # 设置公共模型
        mock_public_model = Mock()
        mock_response = Mock()
        mock_response.status = LLMChatStatus.OK
        mock_response.text = "Async response"
        mock_public_model.async_chat = AsyncMock(return_value=mock_response)
        self.llm_rpc._public_model = mock_public_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello async")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")

        with patch.object(self.llm_rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            result = await self.llm_rpc.async_chat(
                stream=False,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector
            )

            # 验证参数创建和异步聊天调用
            mock_create_params.assert_called_once()
            mock_public_model.async_chat.assert_called_once()
            assert result == mock_response

    @pytest.mark.asyncio
    async def test_async_chat_stream(self):
        """测试异步聊天流式"""
        from unittest.mock import Mock, AsyncMock

        # 设置公共模型
        mock_public_model = Mock()

        async def mock_async_generator():
            yield Mock(status=LLMChatStatus.OK, text="Stream 1")
            yield Mock(status=LLMChatStatus.OK, text="Stream 2")

        mock_public_model.async_chat_stream = AsyncMock(return_value=mock_async_generator())
        self.llm_rpc._public_model = mock_public_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello async stream")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")

        with patch.object(self.llm_rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            result = await self.llm_rpc.async_chat(
                stream=True,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector
            )

            # 验证参数创建和异步流式聊天调用
            mock_create_params.assert_called_once()
            mock_public_model.async_chat_stream.assert_called_once()

            # 验证异步流式聊天被调用
            assert result is not None

    @pytest.mark.asyncio
    async def test_async_chat_with_sft_gateway(self):
        """测试使用 SFT 网关的异步聊天"""
        from unittest.mock import Mock, AsyncMock

        # 设置 SFT 模型
        mock_sft_model = Mock()
        mock_response = Mock()
        mock_response.status = LLMChatStatus.OK
        mock_response.text = "SFT async response"
        mock_sft_model.async_chat = AsyncMock(return_value=mock_response)
        self.llm_rpc._sft_model = mock_sft_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello SFT async")]
        selector = LLModelRpc.ModelSelector(provider="sft_provider", model="sft_model")

        with patch.object(self.llm_rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            result = await self.llm_rpc.async_chat(
                stream=False,
                gateway=LLModelRpc.Gateway.Sft,
                messages=messages,
                selector=selector
            )

            # 验证参数创建和异步聊天调用
            mock_create_params.assert_called_once()
            mock_sft_model.async_chat.assert_called_once()
            assert result == mock_response

    @pytest.mark.asyncio
    async def test_async_chat_with_private_gateway(self):
        """测试使用私有网关的异步聊天"""
        from unittest.mock import Mock, AsyncMock

        # 设置私有模型
        mock_private_model = Mock()
        mock_response = Mock()
        mock_response.status = LLMChatStatus.OK
        mock_response.text = "Private async response"
        mock_private_model.async_chat = AsyncMock(return_value=mock_response)
        self.llm_rpc._private_model = mock_private_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello private async")]
        selector = LLModelRpc.ModelSelector(provider="private_provider", model="private_model")

        with patch.object(self.llm_rpc, '_create_params') as mock_create_params:
            mock_params = Mock()
            mock_create_params.return_value = mock_params

            result = await self.llm_rpc.async_chat(
                stream=False,
                gateway=LLModelRpc.Gateway.Private,
                messages=messages,
                selector=selector
            )

            # 验证参数创建和异步聊天调用
            mock_create_params.assert_called_once()
            mock_private_model.async_chat.assert_called_once()
            assert result == mock_response


class TestLLModelRpcTextMethods:
    """测试 LLM 模型 RPC 文本方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        if hasattr(LLModelRpc, '_instances'):
            LLModelRpc._instances = {}
        self.llm_rpc = LLModelRpc()

    def test_before_chattext_public_gateway(self):
        """测试公共网关的文本聊天前处理"""
        messages, selector, args = self.llm_rpc._before_chattext(
            gateway=LLModelRpc.Gateway.Public,
            query="Hello world",
            temperature=0.8,
            max_token=1000,
            system_content="You are a helpful assistant"
        )

        # 验证消息构建
        assert len(messages) == 2
        assert messages[0].role == "system"
        assert messages[0].content == "You are a helpful assistant"
        assert messages[1].role == "user"
        assert messages[1].content == "Hello world"

        # 验证参数
        assert args.temperature == 0.8
        assert args.max_tokens == 1000

    def test_before_chattext_public_gateway_no_system(self):
        """测试公共网关无系统消息的文本聊天前处理"""
        messages, selector, args = self.llm_rpc._before_chattext(
            gateway=LLModelRpc.Gateway.Public,
            query="Hello world",
            temperature=0.7,
            max_token=500
        )

        # 验证消息构建（只有用户消息）
        assert len(messages) == 1
        assert messages[0].role == "user"
        assert messages[0].content == "Hello world"

    def test_before_chattext_sft_gateway(self):
        """测试 SFT 网关的文本聊天前处理"""
        messages, selector, args = self.llm_rpc._before_chattext(
            gateway=LLModelRpc.Gateway.Sft,
            query="Hello SFT",
            temperature=0.9,
            max_token=2000,
            adapter="test_adapter"
        )

        # 验证消息构建
        assert len(messages) == 2
        assert messages[0].role == "system"
        assert messages[0].content == "You are a helpful assistant."
        assert messages[1].role == "user"
        assert messages[1].content == "Hello SFT"

        # 验证选择器
        assert selector is not None
        assert selector.model == "test_adapter"

    def test_before_chattext_sft_gateway_no_adapter(self):
        """测试 SFT 网关无适配器的文本聊天前处理"""
        messages, selector, args = self.llm_rpc._before_chattext(
            gateway=LLModelRpc.Gateway.Sft,
            query="Hello SFT",
            temperature=0.9,
            max_token=2000
        )

        # 验证选择器为空
        assert selector is None

    def test_before_chattext_private_gateway_minimax(self):
        """测试私有网关 Minimax 平台的文本聊天前处理"""
        from unittest.mock import Mock

        # 模拟私有模型配置
        mock_private_model = Mock()
        mock_conf = Mock()
        mock_conf.platform = "minimax"  # PrivatePlatform.minimax
        mock_private_model.get_conf.return_value = mock_conf
        self.llm_rpc._private_model = mock_private_model

        messages, selector, args = self.llm_rpc._before_chattext(
            gateway=LLModelRpc.Gateway.Private,
            query="Hello Private",
            temperature=0.6,
            max_token=1500
        )

        # 验证消息构建（Minimax 使用 USER 角色）
        assert len(messages) == 1
        assert messages[0].role == "USER"
        assert messages[0].content == "Hello Private"

    def test_before_chattext_with_custom_messages(self):
        """测试使用自定义消息的文本聊天前处理"""
        custom_messages = [
            Message(role="system", content="Custom system"),
            Message(role="user", content="Custom user")
        ]

        messages, selector, args = self.llm_rpc._before_chattext(
            gateway=LLModelRpc.Gateway.Public,
            query="This should be ignored",
            temperature=0.5,
            max_token=800,
            messages=custom_messages
        )

        # 验证使用了自定义消息
        assert messages == custom_messages
        assert len(messages) == 2

    def test_before_chattext_with_kwargs(self):
        """测试带额外参数的文本聊天前处理"""
        messages, selector, args = self.llm_rpc._before_chattext(
            gateway=LLModelRpc.Gateway.Public,
            query="Hello",
            temperature=0.7,
            max_token=1000,
            repetition_penalty=1.1,
            top_p=0.9,
            stop=["END"],
            invalid_param="should_be_ignored"  # 无效参数应被忽略
        )

        # 验证参数设置
        assert args.temperature == 0.7
        assert args.max_tokens == 1000
        assert args.repetition_penalty == 1.1
        assert args.top_p == 0.9
        assert args.stop == ["END"]

        # 验证无效参数被忽略
        assert not hasattr(args, 'invalid_param')

    def test_before_chattext_stop_string_conversion(self):
        """测试 stop 参数字符串转列表"""
        messages, selector, args = self.llm_rpc._before_chattext(
            gateway=LLModelRpc.Gateway.Public,
            query="Hello",
            temperature=0.7,
            max_token=1000,
            stop="STOP"  # 字符串应转换为列表
        )

        # 验证字符串 stop 被转换为列表
        assert args.stop == ["STOP"]


class TestLLModelRpcStreamProcessing:
    """测试 LLM 模型 RPC 流处理方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        if hasattr(LLModelRpc, '_instances'):
            LLModelRpc._instances = {}
        self.llm_rpc = LLModelRpc()

    def test_after_chattext_stream_public_success(self):
        """测试公共网关流处理成功"""
        from unittest.mock import Mock

        # 模拟成功响应
        from commons.llm_gateway.models.chat_data import Usage
        
        mock_line = Mock()
        mock_line.code = 0  # PublicCode.SUCCESS
        mock_line.choices = [Mock()]
        mock_line.choices[0].text = "Stream response"
        mock_line.usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)

        mock_res = [mock_line]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Public))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.OK
        assert results[0].text == "Stream response"

    def test_after_chattext_stream_public_failed(self):
        """测试公共网关流处理失败"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.public_model_gateway import PublicCode

        # 模拟失败响应
        mock_line = Mock()
        mock_line.code = PublicCode.FAILED
        mock_line.choices = []

        mock_res = [mock_line]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Public))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    def test_after_chattext_stream_public_audit(self):
        """测试公共网关流处理审核拦截"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.public_model_gateway import PublicCode

        # 模拟审核拦截响应
        mock_line = Mock()
        mock_line.code = "AuditError.InternalInputNoPass"  # 使用实际的审核错误码
        mock_line.choices = []

        mock_res = [mock_line]

        with patch('commons.llm_gateway.llm.ERROR_AUDIT_ARR', ["AuditError.InternalInputNoPass"]):
            results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Public))

            assert len(results) == 1
            assert results[0].status == LLMChatStatus.AUDIT
            assert results[0].text == ""

    def test_after_chattext_stream_public_privilege(self):
        """测试公共网关流处理权限错误"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.public_model_gateway import PublicCode

        # 模拟权限错误响应
        mock_line = Mock()
        mock_line.code = "Privilege.CheckFailed"  # 使用实际的权限错误码
        mock_line.choices = []

        mock_res = [mock_line]

        with patch('commons.llm_gateway.llm.ERROR_PRIVILEGE_ARR', ["Privilege.CheckFailed"]):
            results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Public))

            assert len(results) == 1
            assert results[0].status == LLMChatStatus.PRIVILEGE
            assert results[0].text == ""

    def test_after_chattext_stream_public_limit(self):
        """测试公共网关流处理限流错误"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.public_model_gateway import PublicCode

        # 模拟限流错误响应
        mock_line = Mock()
        mock_line.code = "GateWayLimitError"  # 使用实际的限流错误码
        mock_line.choices = []

        mock_res = [mock_line]

        with patch('commons.llm_gateway.llm.ERROR_LIMIT_ARR', ["GateWayLimitError"]):
            results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Public))

            assert len(results) == 1
            assert results[0].status == LLMChatStatus.LIMIT
            assert results[0].text == ""

    def test_after_chattext_stream_public_no_choices(self):
        """测试公共网关流处理无选择"""
        from unittest.mock import Mock

        # 模拟无选择响应
        mock_line = Mock()
        mock_line.code = 0  # 成功码但无选择
        mock_line.choices = []

        mock_res = [mock_line]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Public))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    def test_after_chattext_stream_public_none_line(self):
        """测试公共网关流处理空行"""
        mock_res = [None]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Public))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    def test_after_chattext_stream_sft_success(self):
        """测试 SFT 网关流处理成功"""
        from unittest.mock import Mock

        # 模拟成功响应
        from commons.llm_gateway.models.chat_data import Usage
        
        mock_line = Mock()
        mock_line.choices = [Mock()]
        mock_line.choices[0].text = "SFT response"
        mock_line.choices[0].delta = Mock()
        mock_line.choices[0].delta.content = "SFT delta content"
        mock_line.usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)

        mock_res = [mock_line]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Sft))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.OK
        assert results[0].text == "SFT response"

    def test_after_chattext_stream_sft_delta_content(self):
        """测试 SFT 网关流处理增量内容"""
        from unittest.mock import Mock

        # 模拟增量内容响应
        from commons.llm_gateway.models.chat_data import Usage
        
        mock_line = Mock()
        mock_line.choices = [Mock()]
        mock_line.choices[0].text = None  # 无文本，使用增量内容
        mock_line.choices[0].delta = Mock()
        mock_line.choices[0].delta.content = "Delta content"
        mock_line.usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)

        mock_res = [mock_line]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Sft))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.OK
        assert results[0].text == "Delta content"

    def test_after_chattext_stream_sft_no_choices(self):
        """测试 SFT 网关流处理无选择"""
        from unittest.mock import Mock

        # 模拟无选择响应
        from commons.llm_gateway.models.chat_data import Usage
        
        mock_line = Mock()
        mock_line.choices = []
        mock_line.usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)

        mock_res = [mock_line]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Sft))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.OK  # 有usage时返回OK
        assert results[0].text == ""

    def test_after_chattext_stream_private_success(self):
        """测试私有网关流处理成功"""
        from unittest.mock import Mock

        # 模拟成功响应
        from commons.llm_gateway.models.chat_data import Usage
        
        mock_line = Mock()
        mock_line.code = 0  # 成功码
        mock_line.choices = [Mock()]
        mock_line.choices[0].text = "Private response"
        mock_line.usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)

        mock_res = [mock_line]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Private))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.OK
        assert results[0].text == "Private response"

    def test_after_chattext_stream_private_failed(self):
        """测试私有网关流处理失败"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.private_model_gateway import PrivateCode

        # 模拟失败响应
        mock_line = Mock()
        mock_line.code = PrivateCode.FAILED
        mock_line.choices = [Mock()]

        mock_res = [mock_line]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Private))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    def test_after_chattext_stream_private_no_choices(self):
        """测试私有网关流处理无选择"""
        from unittest.mock import Mock

        # 模拟无选择响应
        mock_line = Mock()
        mock_line.code = 0  # 成功码但无选择
        mock_line.choices = []

        mock_res = [mock_line]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Private))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    def test_after_chattext_stream_private_none_line(self):
        """测试私有网关流处理空行"""
        mock_res = [None]

        # 处理流响应
        results = list(self.llm_rpc._after_chattext_stream(mock_res, LLModelRpc.Gateway.Private))

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    def test_after_chattext_stream_invalid_gateway(self):
        """测试无效网关类型"""
        mock_res = [Mock()]

        with pytest.raises(Exception) as exc_info:
            list(self.llm_rpc._after_chattext_stream(mock_res, "invalid_gateway"))

        assert "gateway type error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_public_success(self):
        """测试异步公共网关流处理成功"""
        from unittest.mock import Mock

        # 模拟成功响应
        from commons.llm_gateway.models.chat_data import Usage
        
        mock_line = Mock()
        mock_line.code = 0  # PublicCode.SUCCESS
        mock_line.choices = [Mock()]
        mock_line.choices[0].text = "Async stream response"
        mock_line.usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)

        async def mock_async_res():
            yield mock_line

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Public):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.OK
        assert results[0].text == "Async stream response"

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_sft_success(self):
        """测试异步 SFT 网关流处理成功"""
        from unittest.mock import Mock

        # 模拟成功响应
        from commons.llm_gateway.models.chat_data import Usage
        
        mock_line = Mock()
        mock_line.choices = [Mock()]
        mock_line.choices[0].delta = Mock()
        mock_line.choices[0].delta.content = "Async SFT content"
        mock_line.usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)

        async def mock_async_res():
            yield mock_line

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Sft):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.OK
        assert results[0].text == "Async SFT content"

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_sft_empty_choices(self):
        """测试异步 SFT 网关流处理空选择"""
        from unittest.mock import Mock

        # 模拟空选择响应
        from commons.llm_gateway.models.chat_data import Usage
        
        mock_line = Mock()
        mock_line.choices = []  # 空选择列表
        mock_line.usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)

        async def mock_async_res():
            yield mock_line

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Sft):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.OK
        assert results[0].text == ""  # 空内容

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_private_success(self):
        """测试异步私有网关流处理成功"""
        from unittest.mock import Mock

        # 模拟成功响应
        from commons.llm_gateway.models.chat_data import Usage
        
        mock_line = Mock()
        mock_line.code = 0  # 成功码
        mock_line.choices = [Mock()]
        mock_line.choices[0].text = "Async private response"
        mock_line.usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)

        async def mock_async_res():
            yield mock_line

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Private):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.OK
        assert results[0].text == "Async private response"

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_invalid_gateway(self):
        """测试异步无效网关类型"""
        async def mock_async_res():
            yield Mock()

        with pytest.raises(Exception) as exc_info:
            async for _ in self.llm_rpc._async_after_chattext_stream(mock_async_res(), "invalid_gateway"):
                pass

        assert "gateway type error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_public_failed(self):
        """测试异步公共网关流处理失败"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.public_model_gateway import PublicCode

        # 模拟失败响应
        mock_line = Mock()
        mock_line.code = PublicCode.FAILED
        mock_line.choices = []

        async def mock_async_res():
            yield mock_line

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Public):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_public_audit(self):
        """测试异步公共网关流处理审核拦截"""
        from unittest.mock import Mock

        # 模拟审核拦截响应
        mock_line = Mock()
        mock_line.code = "AuditError.InternalInputNoPass"
        mock_line.choices = []

        async def mock_async_res():
            yield mock_line

        with patch('commons.llm_gateway.llm.ERROR_AUDIT_ARR', ["AuditError.InternalInputNoPass"]):
            results = []
            async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Public):
                results.append(result)

            assert len(results) == 1
            assert results[0].status == LLMChatStatus.AUDIT
            assert results[0].text == ""

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_public_privilege(self):
        """测试异步公共网关流处理权限错误"""
        from unittest.mock import Mock

        # 模拟权限错误响应
        mock_line = Mock()
        mock_line.code = "Privilege.CheckFailed"
        mock_line.choices = []

        async def mock_async_res():
            yield mock_line

        with patch('commons.llm_gateway.llm.ERROR_PRIVILEGE_ARR', ["Privilege.CheckFailed"]):
            results = []
            async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Public):
                results.append(result)

            assert len(results) == 1
            assert results[0].status == LLMChatStatus.PRIVILEGE
            assert results[0].text == ""

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_public_limit(self):
        """测试异步公共网关流处理限流错误"""
        from unittest.mock import Mock

        # 模拟限流错误响应
        mock_line = Mock()
        mock_line.code = "GateWayLimitError"
        mock_line.choices = []

        async def mock_async_res():
            yield mock_line

        with patch('commons.llm_gateway.llm.ERROR_LIMIT_ARR', ["GateWayLimitError"]):
            results = []
            async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Public):
                results.append(result)

            assert len(results) == 1
            assert results[0].status == LLMChatStatus.LIMIT
            assert results[0].text == ""

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_public_no_choices(self):
        """测试异步公共网关流处理无选择"""
        from unittest.mock import Mock

        # 模拟无选择响应
        mock_line = Mock()
        mock_line.code = 0  # 成功码但无选择
        mock_line.choices = []

        async def mock_async_res():
            yield mock_line

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Public):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_public_none_line(self):
        """测试异步公共网关流处理空行"""
        async def mock_async_res():
            yield None

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Public):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_sft_no_choices(self):
        """测试异步 SFT 网关流处理无选择"""
        from unittest.mock import Mock

        # 模拟无选择响应
        mock_line = Mock()
        mock_line.choices = None  # choices 为 None

        async def mock_async_res():
            yield mock_line

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Sft):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_sft_none_line(self):
        """测试异步 SFT 网关流处理空行"""
        async def mock_async_res():
            yield None

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Sft):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_private_failed(self):
        """测试异步私有网关流处理失败"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.private_model_gateway import PrivateCode

        # 模拟失败响应
        mock_line = Mock()
        mock_line.code = PrivateCode.FAILED
        mock_line.choices = [Mock()]

        async def mock_async_res():
            yield mock_line

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Private):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_private_no_choices(self):
        """测试异步私有网关流处理无选择"""
        from unittest.mock import Mock

        # 模拟无选择响应
        mock_line = Mock()
        mock_line.code = 0  # 成功码但无选择
        mock_line.choices = []

        async def mock_async_res():
            yield mock_line

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Private):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""

    @pytest.mark.asyncio
    async def test_async_after_chattext_stream_private_none_line(self):
        """测试异步私有网关流处理空行"""
        async def mock_async_res():
            yield None

        # 处理异步流响应
        results = []
        async for result in self.llm_rpc._async_after_chattext_stream(mock_async_res(), LLModelRpc.Gateway.Private):
            results.append(result)

        assert len(results) == 1
        assert results[0].status == LLMChatStatus.FAIL
        assert results[0].text == ""


class TestLLModelRpcAfterChattext:
    """测试 LLM 模型 RPC 聊天后处理方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        if hasattr(LLModelRpc, '_instances'):
            LLModelRpc._instances = {}
        self.llm_rpc = LLModelRpc()

    def test_create_params_with_sec_text(self):
        """测试创建带安全文本参数的参数"""
        from unittest.mock import Mock

        # 设置公共模型
        mock_public_model = Mock()
        mock_conf = Mock()
        mock_conf.provider = "test_provider"
        mock_conf.model = "test_model"
        mock_public_model.get_conf.return_value = mock_conf
        self.llm_rpc._public_model = mock_public_model

        # 创建测试参数
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")
        args = LLModelRpc.LLMArgs(temperature=0.7)

        with patch('commons.llm_gateway.llm.LLMParam') as mock_llm_param, \
             patch('commons.llm_gateway.llm.ChatParams') as mock_chat_params, \
             patch('commons.llm_gateway.llm.SecText') as mock_sec_text:

            mock_llm_param_instance = Mock()
            mock_llm_param.return_value = mock_llm_param_instance
            mock_chat_params_instance = Mock()
            mock_chat_params.return_value = mock_chat_params_instance
            mock_sec_text_instance = Mock()
            mock_sec_text.return_value = mock_sec_text_instance

            result = self.llm_rpc._create_params(
                stream=False,
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                selector=selector,
                args=args,
                sec_text=True,
                sec_scene="test_scene",
                sec_from="test_from",
                sec_answer_flag=1,
                sec_extra_text="extra_text"
            )

            # 验证 SecText 被正确创建和设置
            mock_sec_text.assert_called_once_with(scene="test_scene")
            assert mock_sec_text_instance.from_ == "test_from"
            assert mock_sec_text_instance.answer_flag == 1
            assert mock_sec_text_instance.extra_text == "extra_text"

            assert result == mock_chat_params_instance

    def test_create_params_sft_with_none_args(self):
        """测试 SFT 网关创建参数时 args 为 None"""
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="sft_provider", model="sft_model")

        with patch('commons.llm_gateway.llm.SftChatParams') as mock_sft_params:
            mock_sft_params_instance = Mock()
            mock_sft_params.return_value = mock_sft_params_instance

            result = self.llm_rpc._create_params(
                stream=False,
                gateway=LLModelRpc.Gateway.Sft,
                messages=messages,
                selector=selector,
                args=None  # args 为 None
            )

            # 验证默认 stop_token_ids 被设置
            mock_sft_params.assert_called_once()
            call_kwargs = mock_sft_params.call_args[1]
            assert call_kwargs["stop_token_ids"] == [151645, 151644]

            assert result == mock_sft_params_instance

    def test_create_params_sft_with_existing_stop_token_ids(self):
        """测试 SFT 网关创建参数时已有 stop_token_ids"""
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="sft_provider", model="sft_model")
        args = LLModelRpc.LLMArgs(stop_token_ids=[123, 456])

        with patch('commons.llm_gateway.llm.SftChatParams') as mock_sft_params:
            mock_sft_params_instance = Mock()
            mock_sft_params.return_value = mock_sft_params_instance

            result = self.llm_rpc._create_params(
                stream=False,
                gateway=LLModelRpc.Gateway.Sft,
                messages=messages,
                selector=selector,
                args=args
            )

            # 验证现有的 stop_token_ids 被保留
            mock_sft_params.assert_called_once()
            call_kwargs = mock_sft_params.call_args[1]
            assert call_kwargs["stop_token_ids"] == [123, 456]

            assert result == mock_sft_params_instance

    def test_create_params_invalid_gateway(self):
        """测试创建参数时使用无效网关"""
        messages = [Message(role="user", content="Hello")]
        selector = LLModelRpc.ModelSelector(provider="test_provider", model="test_model")
        args = LLModelRpc.LLMArgs(temperature=0.7)

        with pytest.raises(Exception) as exc_info:
            self.llm_rpc._create_params(
                stream=False,
                gateway="invalid_gateway",
                messages=messages,
                selector=selector,
                args=args
            )

        assert "gateway type error" in str(exc_info.value)

    def test_before_chattext_private_gateway_non_minimax(self):
        """测试私有网关非 Minimax 平台的文本聊天前处理"""
        from unittest.mock import Mock

        # 模拟私有模型配置（非 Minimax）
        mock_private_model = Mock()
        mock_conf = Mock()
        mock_conf.platform = "other_platform"  # 非 Minimax 平台
        mock_private_model.get_conf.return_value = mock_conf
        self.llm_rpc._private_model = mock_private_model

        messages, selector, args = self.llm_rpc._before_chattext(
            gateway=LLModelRpc.Gateway.Private,
            query="Hello Private",
            temperature=0.6,
            max_token=1500
        )

        # 验证消息构建（非 Minimax 使用 user 角色）
        assert len(messages) == 1
        assert messages[0].role == "user"
        assert messages[0].content == "Hello Private"

    def test_get_default_tpm_key_public(self):
        """测试获取公共网关默认 TPM 键"""
        from unittest.mock import Mock

        # 设置公共模型
        mock_public_model = Mock()
        mock_conf = Mock()
        mock_conf.provider = "test_provider"
        mock_conf.model = "test_model"
        mock_public_model.get_conf.return_value = mock_conf
        self.llm_rpc._public_model = mock_public_model

        key = self.llm_rpc._get_default_tpm_key(LLModelRpc.Gateway.Public)
        assert key == "test_provider_test_model"

    def test_get_default_tpm_key_sft(self):
        """测试获取 SFT 网关默认 TPM 键"""
        from commons.llm_gateway.models.chat_data import ChatType

        key = self.llm_rpc._get_default_tpm_key(LLModelRpc.Gateway.Sft)
        assert key == ChatType.chat.value

    def test_get_default_tpm_key_private(self):
        """测试获取私有网关默认 TPM 键"""
        key = self.llm_rpc._get_default_tpm_key(LLModelRpc.Gateway.Private)
        assert key == ""

    def test_get_tpm_with_key(self):
        """测试获取 TPM 带指定键"""
        from unittest.mock import Mock

        # 设置公共模型
        mock_public_model = Mock()
        mock_public_model.get_tpm.return_value = 100
        self.llm_rpc._public_model = mock_public_model

        tpm = self.llm_rpc.get_tpm(LLModelRpc.Gateway.Public, "custom_key")

        mock_public_model.get_tpm.assert_called_once_with("custom_key")
        assert tpm == 100

    def test_get_tpm_without_key(self):
        """测试获取 TPM 不指定键"""
        from unittest.mock import Mock

        # 设置公共模型
        mock_public_model = Mock()
        mock_conf = Mock()
        mock_conf.provider = "test_provider"
        mock_conf.model = "test_model"
        mock_public_model.get_conf.return_value = mock_conf
        mock_public_model.get_tpm.return_value = 200
        self.llm_rpc._public_model = mock_public_model

        tpm = self.llm_rpc.get_tpm(LLModelRpc.Gateway.Public)

        mock_public_model.get_tpm.assert_called_once_with("test_provider_test_model")
        assert tpm == 200

    @pytest.mark.asyncio
    async def test_async_get_tpm_with_key(self):
        """测试异步获取 TPM 带指定键"""
        from unittest.mock import Mock, AsyncMock

        # 设置公共模型
        mock_public_model = Mock()
        mock_public_model.async_get_tpm = AsyncMock(return_value=150)
        self.llm_rpc._public_model = mock_public_model

        tpm = await self.llm_rpc.async_get_tpm(LLModelRpc.Gateway.Public, "async_key")

        mock_public_model.async_get_tpm.assert_called_once_with("async_key")
        assert tpm == 150

    @pytest.mark.asyncio
    async def test_async_get_tpm_without_key(self):
        """测试异步获取 TPM 不指定键"""
        from unittest.mock import Mock, AsyncMock

        # 设置公共模型
        mock_public_model = Mock()
        mock_conf = Mock()
        mock_conf.provider = "async_provider"
        mock_conf.model = "async_model"
        mock_public_model.get_conf.return_value = mock_conf
        mock_public_model.async_get_tpm = AsyncMock(return_value=250)
        self.llm_rpc._public_model = mock_public_model

        tpm = await self.llm_rpc.async_get_tpm(LLModelRpc.Gateway.Public)

        mock_public_model.async_get_tpm.assert_called_once_with("async_provider_async_model")
        assert tpm == 250

    def test_after_chattext_public_success(self):
        """测试公共网关聊天后处理成功"""
        from unittest.mock import Mock

        # 模拟成功响应
        mock_res = Mock()
        mock_res.code = 0  # PublicCode.SUCCESS
        mock_res.choices = [Mock()]
        mock_res.choices[0].text = "Success response"

        status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Public, return_usage=False)

        assert status == LLMChatStatus.OK
        assert text == "Success response"

    def test_after_chattext_public_failed(self):
        """测试公共网关聊天后处理失败"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.public_model_gateway import PublicCode

        # 模拟失败响应
        mock_res = Mock()
        mock_res.code = PublicCode.FAILED

        status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Public, return_usage=False)

        assert status == LLMChatStatus.FAIL
        assert text == ""

    def test_after_chattext_public_none_response(self):
        """测试公共网关聊天后处理空响应"""
        status, text = self.llm_rpc._after_chattext(None, LLModelRpc.Gateway.Public, return_usage=False)

        assert status == LLMChatStatus.FAIL
        assert text == ""

    def test_after_chattext_public_audit(self):
        """测试公共网关聊天后处理审核拦截"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.public_model_gateway import PublicCode

        # 模拟审核拦截响应
        mock_res = Mock()
        mock_res.code = "AuditError.InternalInputNoPass"  # 使用实际的审核错误码

        with patch('commons.llm_gateway.llm.ERROR_AUDIT_ARR', ["AuditError.InternalInputNoPass"]):
            status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Public, return_usage=False)

            assert status == LLMChatStatus.AUDIT
            assert text == ""

    def test_after_chattext_public_privilege(self):
        """测试公共网关聊天后处理权限错误"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.public_model_gateway import PublicCode

        # 模拟权限错误响应
        mock_res = Mock()
        mock_res.code = "Privilege.CheckFailed"  # 使用实际的权限错误码

        with patch('commons.llm_gateway.llm.ERROR_PRIVILEGE_ARR', ["Privilege.CheckFailed"]):
            status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Public, return_usage=False)

            assert status == LLMChatStatus.PRIVILEGE
            assert text == ""

    def test_after_chattext_public_limit(self):
        """测试公共网关聊天后处理限流错误"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.public_model_gateway import PublicCode

        # 模拟限流错误响应
        mock_res = Mock()
        mock_res.code = "GateWayLimitError"  # 使用实际的限流错误码

        with patch('commons.llm_gateway.llm.ERROR_LIMIT_ARR', ["GateWayLimitError"]):
            status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Public, return_usage=False)

            assert status == LLMChatStatus.LIMIT
            assert text == ""

    def test_after_chattext_public_no_choices(self):
        """测试公共网关聊天后处理无选择"""
        from unittest.mock import Mock

        # 模拟无选择响应
        mock_res = Mock()
        mock_res.code = 0  # 成功码但无选择
        mock_res.choices = []

        status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Public, return_usage=False)

        assert status == LLMChatStatus.FAIL
        assert text == ""

    def test_after_chattext_sft_success_single(self):
        """测试 SFT 网关聊天后处理成功（单个选择）"""
        from unittest.mock import Mock

        # 模拟成功响应（单个选择）
        mock_res = Mock()
        mock_res.choices = [Mock()]
        mock_res.choices[0].text = "SFT response"

        status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Sft, return_usage=False)

        assert status == LLMChatStatus.OK
        assert text == "SFT response"

    def test_after_chattext_sft_success_multiple(self):
        """测试 SFT 网关聊天后处理成功（多个选择）"""
        from unittest.mock import Mock

        # 模拟成功响应（多个选择）
        mock_res = Mock()
        mock_res.choices = [Mock(), Mock()]
        mock_res.choices[0].text = "SFT response 1"
        mock_res.choices[1].text = "SFT response 2"

        status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Sft, return_usage=False)

        assert status == LLMChatStatus.OK
        assert text == ["SFT response 1", "SFT response 2"]

    def test_after_chattext_sft_message_content(self):
        """测试 SFT 网关聊天后处理消息内容"""
        from unittest.mock import Mock

        # 模拟消息内容响应
        mock_res = Mock()
        mock_res.choices = [Mock()]
        mock_res.choices[0].text = None  # 无文本，使用消息内容
        mock_res.choices[0].message = Mock()
        mock_res.choices[0].message.content = "SFT message content"

        status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Sft, return_usage=False)

        assert status == LLMChatStatus.OK
        assert text == "SFT message content"

    def test_after_chattext_sft_no_choices(self):
        """测试 SFT 网关聊天后处理无选择"""
        from unittest.mock import Mock

        # 模拟无选择响应
        mock_res = Mock()
        mock_res.choices = []

        status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Sft, return_usage=False)

        assert status == LLMChatStatus.FAIL
        assert text == ""

    def test_after_chattext_private_success(self):
        """测试私有网关聊天后处理成功"""
        from unittest.mock import Mock

        # 模拟成功响应
        mock_res = Mock()
        mock_res.code = 0  # 成功码
        mock_res.choices = [Mock()]
        mock_res.choices[0].text = "Private response"

        status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Private, return_usage=False)

        assert status == LLMChatStatus.OK
        assert text == "Private response"

    def test_after_chattext_private_failed(self):
        """测试私有网关聊天后处理失败"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.private_model_gateway import PrivateCode

        # 模拟失败响应
        mock_res = Mock()
        mock_res.code = PrivateCode.FAILED
        mock_res.choices = []

        status, text = self.llm_rpc._after_chattext(mock_res, LLModelRpc.Gateway.Private, return_usage=False)

        assert status == LLMChatStatus.FAIL
        assert text == ""

    def test_after_chattext_invalid_gateway(self):
        """测试无效网关类型"""
        from unittest.mock import Mock

        mock_res = Mock()

        with pytest.raises(Exception) as exc_info:
            self.llm_rpc._after_chattext(mock_res, "invalid_gateway", return_usage=False)

        assert "gateway type error" in str(exc_info.value)


class TestLLModelRpcHighLevelMethods:
    """测试 LLM 模型 RPC 高级方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例实例
        if hasattr(LLModelRpc, '_instances'):
            LLModelRpc._instances = {}
        self.llm_rpc = LLModelRpc()

    def test_chat_text_success(self):
        """测试文本聊天成功"""
        from unittest.mock import Mock

        # 设置公共模型
        mock_public_model = Mock()
        mock_response = Mock()
        mock_response.code = 0
        mock_response.choices = [Mock()]
        mock_response.choices[0].text = "Chat text response"
        mock_public_model.chat.return_value = mock_response
        self.llm_rpc._public_model = mock_public_model

        with patch.object(self.llm_rpc, '_before_chattext') as mock_before, \
             patch.object(self.llm_rpc, 'chat') as mock_chat, \
             patch.object(self.llm_rpc, '_after_chattext') as mock_after:

            mock_before.return_value = ([Message(role="user", content="test")], None, Mock())
            mock_chat.return_value = mock_response
            mock_after.return_value = (LLMChatStatus.OK, "Chat text response")

            status, text = self.llm_rpc.chat_text(
                gateway=LLModelRpc.Gateway.Public,
                query="Hello",
                temperature=0.7,
                max_token=1000
            )

            assert status == LLMChatStatus.OK
            assert text == "Chat text response"
            mock_before.assert_called_once()
            mock_chat.assert_called_once()
            mock_after.assert_called_once()

    def test_chat_text_with_prompt_non_sft(self):
        """测试使用 prompt 但非 SFT 网关的文本聊天"""
        status, text = self.llm_rpc.chat_text(
            gateway=LLModelRpc.Gateway.Public,
            query="Hello",
            prompt="test prompt"
        )

        assert status == LLMChatStatus.FAIL
        assert text == "补全只支持私有化模型"

    def test_chat_text_with_prompt_sft(self):
        """测试使用 prompt 和 SFT 网关的文本聊天"""
        from unittest.mock import Mock

        # 设置 SFT 模型
        mock_sft_model = Mock()
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].text = "SFT prompt response"
        mock_sft_model.chat.return_value = mock_response
        self.llm_rpc._sft_model = mock_sft_model

        with patch.object(self.llm_rpc, '_before_chattext') as mock_before, \
             patch.object(self.llm_rpc, 'chat') as mock_chat, \
             patch.object(self.llm_rpc, '_after_chattext') as mock_after:

            mock_before.return_value = ([Message(role="user", content="")], None, Mock())
            mock_chat.return_value = mock_response
            mock_after.return_value = (LLMChatStatus.OK, "SFT prompt response")

            status, text = self.llm_rpc.chat_text(
                gateway=LLModelRpc.Gateway.Sft,
                query="This will be ignored",
                prompt="test prompt"
            )

            assert status == LLMChatStatus.OK
            assert text == "SFT prompt response"
            # 验证 query 被设置为空字符串
            call_args = mock_before.call_args[0]
            assert call_args[1] == ""  # query 参数

    def test_chat_text_stream_success(self):
        """测试流式文本聊天成功"""
        from unittest.mock import Mock

        # 设置公共模型
        mock_public_model = Mock()

        def mock_stream():
            yield Mock(code=0, choices=[Mock(text="Stream 1")])
            yield Mock(code=0, choices=[Mock(text="Stream 2")])

        mock_public_model.chat_stream.return_value = mock_stream()
        self.llm_rpc._public_model = mock_public_model

        with patch.object(self.llm_rpc, '_before_chattext') as mock_before, \
             patch.object(self.llm_rpc, 'chat') as mock_chat, \
             patch.object(self.llm_rpc, '_after_chattext_stream') as mock_after_stream:

            mock_before.return_value = ([Message(role="user", content="test")], None, Mock())
            mock_chat.return_value = mock_stream()

            def mock_after_stream_func(res, gateway):
                for item in res:
                    yield LLMStreamItem(status=LLMChatStatus.OK, text=item.choices[0].text)

            mock_after_stream.side_effect = mock_after_stream_func

            results = list(self.llm_rpc.chat_text_stream(
                gateway=LLModelRpc.Gateway.Public,
                query="Hello stream"
            ))

            assert len(results) == 2
            assert results[0].text == "Stream 1"
            assert results[1].text == "Stream 2"

    def test_chat_text_stream_with_prompt_non_sft(self):
        """测试使用 prompt 但非 SFT 网关的流式文本聊天"""
        with patch.object(self.llm_rpc, '_after_chattext_stream') as mock_after_stream:

            def mock_after_stream_func(res, gateway):
                yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")

            mock_after_stream.side_effect = mock_after_stream_func

            results = list(self.llm_rpc.chat_text_stream(
                gateway=LLModelRpc.Gateway.Public,
                query="Hello",
                prompt="test prompt"
            ))

            # 应该返回失败的流项
            assert len(results) == 1
            assert results[0].status == LLMChatStatus.FAIL
            # 验证调用了 _after_chattext_stream 且传入了 [None]
            mock_after_stream.assert_called_once_with([None], LLModelRpc.Gateway.Public)

    @pytest.mark.asyncio
    async def test_async_chat_text_success(self):
        """测试异步文本聊天成功"""
        from unittest.mock import Mock, AsyncMock

        # 设置公共模型
        mock_public_model = Mock()
        mock_response = Mock()
        mock_response.code = 0
        mock_response.choices = [Mock()]
        mock_response.choices[0].text = "Async chat text response"
        mock_public_model.async_chat = AsyncMock(return_value=mock_response)
        self.llm_rpc._public_model = mock_public_model

        with patch.object(self.llm_rpc, '_before_chattext') as mock_before, \
             patch.object(self.llm_rpc, 'async_chat') as mock_async_chat, \
             patch.object(self.llm_rpc, '_after_chattext') as mock_after:

            mock_before.return_value = ([Message(role="user", content="test")], None, Mock())
            mock_async_chat.return_value = mock_response
            mock_after.return_value = (LLMChatStatus.OK, "Async chat text response")

            status, text = await self.llm_rpc.async_chat_text(
                gateway=LLModelRpc.Gateway.Public,
                query="Hello async",
                temperature=0.8,
                max_token=1500
            )

            assert status == LLMChatStatus.OK
            assert text == "Async chat text response"

    @pytest.mark.asyncio
    async def test_async_chat_text_with_prompt_non_sft(self):
        """测试异步使用 prompt 但非 SFT 网关的文本聊天"""
        status, text = await self.llm_rpc.async_chat_text(
            gateway=LLModelRpc.Gateway.Public,
            query="Hello",
            prompt="test prompt"
        )

        assert status == LLMChatStatus.FAIL
        assert text == "补全只支持私有化模型"

    @pytest.mark.asyncio
    async def test_async_chat_text_stream_success(self):
        """测试异步流式文本聊天成功"""
        from unittest.mock import Mock, AsyncMock

        # 设置公共模型
        mock_public_model = Mock()

        async def mock_async_stream():
            yield Mock(code=0, choices=[Mock(text="Async Stream 1")])
            yield Mock(code=0, choices=[Mock(text="Async Stream 2")])

        mock_public_model.async_chat_stream = AsyncMock(return_value=mock_async_stream())
        self.llm_rpc._public_model = mock_public_model

        with patch.object(self.llm_rpc, '_before_chattext') as mock_before, \
             patch.object(self.llm_rpc, 'async_chat') as mock_async_chat, \
             patch.object(self.llm_rpc, '_async_after_chattext_stream') as mock_async_after_stream:

            mock_before.return_value = ([Message(role="user", content="test")], None, Mock())
            mock_async_chat.return_value = mock_async_stream()

            async def mock_async_after_stream_func(res, gateway):
                async for item in res:
                    yield LLMStreamItem(status=LLMChatStatus.OK, text=item.choices[0].text)

            mock_async_after_stream.return_value = mock_async_after_stream_func(mock_async_stream(), LLModelRpc.Gateway.Public)

            result_generator = await self.llm_rpc.async_chat_text_stream(
                gateway=LLModelRpc.Gateway.Public,
                query="Hello async stream"
            )

            results = []
            async for item in result_generator:
                results.append(item)

            assert len(results) == 2
            assert results[0].text == "Async Stream 1"
            assert results[1].text == "Async Stream 2"

    @pytest.mark.asyncio
    async def test_async_chat_text_stream_with_prompt_non_sft(self):
        """测试异步使用 prompt 但非 SFT 网关的流式文本聊天"""
        with patch.object(self.llm_rpc, '_async_after_chattext_stream') as mock_async_after_stream:

            async def mock_async_after_stream_func(res, gateway):
                yield LLMStreamItem(status=LLMChatStatus.FAIL, text="")

            mock_async_after_stream.return_value = mock_async_after_stream_func([None], LLModelRpc.Gateway.Public)

            result_generator = await self.llm_rpc.async_chat_text_stream(
                gateway=LLModelRpc.Gateway.Public,
                query="Hello",
                prompt="test prompt"
            )

            results = []
            async for item in result_generator:
                results.append(item)

            # 应该返回失败的流项
            assert len(results) == 1
            assert results[0].status == LLMChatStatus.FAIL

    def test_multimodal_non_stream(self):
        """测试多模态非流式"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.chat_data import ChatType

        # 设置公共模型
        mock_public_model = Mock()
        mock_response = Mock()
        mock_response.code = 0
        mock_response.choices = [Mock()]
        mock_response.choices[0].text = "Multimodal response"
        mock_public_model.chat.return_value = mock_response
        self.llm_rpc._public_model = mock_public_model

        messages = [Message(role="user", content="Describe this image")]

        with patch.object(self.llm_rpc, '_before_chattext') as mock_before, \
             patch.object(self.llm_rpc, 'chat') as mock_chat, \
             patch.object(self.llm_rpc, '_after_chattext') as mock_after:

            mock_before.return_value = (messages, None, Mock())
            mock_chat.return_value = mock_response
            mock_after.return_value = (LLMChatStatus.OK, "Multimodal response")

            status, text = self.llm_rpc.multimodal(
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                stream=False
            )

            assert status == LLMChatStatus.OK
            assert text == "Multimodal response"
            # 验证使用了多模态聊天类型
            mock_chat.assert_called_once()
            call_args = mock_chat.call_args[0]
            # chat_type 是第6个位置参数（索引5）
            assert len(call_args) > 5 and call_args[5] == ChatType.multimodal

    def test_multimodal_stream(self):
        """测试多模态流式"""
        from unittest.mock import Mock
        from commons.llm_gateway.models.chat_data import ChatType

        # 设置公共模型
        mock_public_model = Mock()

        def mock_stream():
            yield Mock(code=0, choices=[Mock(text="Multimodal stream 1")])
            yield Mock(code=0, choices=[Mock(text="Multimodal stream 2")])

        mock_public_model.chat_stream.return_value = mock_stream()
        self.llm_rpc._public_model = mock_public_model

        messages = [Message(role="user", content="Describe this image")]

        with patch.object(self.llm_rpc, '_before_chattext') as mock_before, \
             patch.object(self.llm_rpc, 'chat') as mock_chat, \
             patch.object(self.llm_rpc, '_after_chattext_stream') as mock_after_stream:

            mock_before.return_value = (messages, None, Mock())
            mock_chat.return_value = mock_stream()

            def mock_after_stream_func(res, gateway):
                for item in res:
                    yield LLMStreamItem(status=LLMChatStatus.OK, text=item.choices[0].text)

            mock_after_stream.side_effect = mock_after_stream_func

            results = list(self.llm_rpc.multimodal(
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                stream=True
            ))

            assert len(results) == 2
            assert results[0].text == "Multimodal stream 1"
            assert results[1].text == "Multimodal stream 2"

    @pytest.mark.asyncio
    async def test_async_multimodal_non_stream(self):
        """测试异步多模态非流式"""
        from unittest.mock import Mock, AsyncMock
        from commons.llm_gateway.models.chat_data import ChatType

        # 设置公共模型
        mock_public_model = Mock()
        mock_response = Mock()
        mock_response.code = 0
        mock_response.choices = [Mock()]
        mock_response.choices[0].text = "Async multimodal response"
        mock_public_model.async_chat = AsyncMock(return_value=mock_response)
        self.llm_rpc._public_model = mock_public_model

        messages = [Message(role="user", content="Describe this image")]

        with patch.object(self.llm_rpc, '_before_chattext') as mock_before, \
             patch.object(self.llm_rpc, 'async_chat') as mock_async_chat, \
             patch.object(self.llm_rpc, '_after_chattext') as mock_after:

            mock_before.return_value = (messages, None, Mock())
            mock_async_chat.return_value = mock_response
            mock_after.return_value = (LLMChatStatus.OK, "Async multimodal response")

            status, text = await self.llm_rpc.async_multimodal(
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                stream=False
            )

            assert status == LLMChatStatus.OK
            assert text == "Async multimodal response"

    @pytest.mark.asyncio
    async def test_async_multimodal_stream(self):
        """测试异步多模态流式"""
        from unittest.mock import Mock, AsyncMock
        from commons.llm_gateway.models.chat_data import ChatType

        # 设置公共模型
        mock_public_model = Mock()

        async def mock_async_stream():
            yield Mock(code=0, choices=[Mock(text="Async multimodal stream 1")])
            yield Mock(code=0, choices=[Mock(text="Async multimodal stream 2")])

        mock_public_model.async_chat_stream = AsyncMock(return_value=mock_async_stream())
        self.llm_rpc._public_model = mock_public_model

        messages = [Message(role="user", content="Describe this image")]

        with patch.object(self.llm_rpc, '_before_chattext') as mock_before, \
             patch.object(self.llm_rpc, 'async_chat') as mock_async_chat, \
             patch.object(self.llm_rpc, '_async_after_chattext_stream') as mock_async_after_stream:

            mock_before.return_value = (messages, None, Mock())
            mock_async_chat.return_value = mock_async_stream()

            async def mock_async_after_stream_func(res, gateway):
                async for item in res:
                    yield LLMStreamItem(status=LLMChatStatus.OK, text=item.choices[0].text)

            mock_async_after_stream.return_value = mock_async_after_stream_func(mock_async_stream(), LLModelRpc.Gateway.Public)

            result_generator = await self.llm_rpc.async_multimodal(
                gateway=LLModelRpc.Gateway.Public,
                messages=messages,
                stream=True
            )

            results = []
            async for item in result_generator:
                results.append(item)

            assert len(results) == 2
            assert results[0].text == "Async multimodal stream 1"
            assert results[1].text == "Async multimodal stream 2"



