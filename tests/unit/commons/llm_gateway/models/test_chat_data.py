"""
commons.llm_gateway.models.chat_data 模块的测试
"""
import pytest
from unittest.mock import Mock, patch
from typing import List, Optional

from commons.llm_gateway.models.chat_data import (
    ChatType,
    MultiModalType,
    SftMultiModalImageDetail,
    SftMultiModalImageUrl,
    MultiModalContent,
    SftMultiModalText,
)


class TestChatType:
    """测试聊天类型枚举"""

    def test_chat_type_values(self):
        """测试聊天类型枚举值"""
        assert ChatType.chat == "chat"
        assert ChatType.multimodal == "multimodal"

    def test_chat_type_comparison(self):
        """测试聊天类型比较"""
        assert ChatType.chat != ChatType.multimodal
        assert ChatType.chat == "chat"
        assert ChatType.multimodal == "multimodal"


class TestMultiModalType:
    """测试多模态类型枚举"""

    def test_multimodal_type_values(self):
        """测试多模态类型枚举值"""
        assert MultiModalType.image_url == "image_url"
        assert MultiModalType.image == "image"
        assert MultiModalType.text == "text"

    def test_multimodal_type_usage(self):
        """测试多模态类型使用"""
        # 测试类型判断
        content_type = MultiModalType.image_url
        assert content_type == "image_url"
        
        # 测试不同类型
        types = [MultiModalType.image_url, MultiModalType.image, MultiModalType.text]
        assert len(types) == 3
        assert all(isinstance(t, MultiModalType) for t in types)


class TestSftMultiModalImageDetail:
    """测试 SFT 多模态图像详细度枚举"""

    def test_image_detail_values(self):
        """测试图像详细度枚举值"""
        assert SftMultiModalImageDetail.auto == "auto"
        assert SftMultiModalImageDetail.low == "low"
        assert SftMultiModalImageDetail.high == "high"

    def test_image_detail_selection(self):
        """测试图像详细度选择"""
        # 模拟详细度选择逻辑
        def select_detail(quality_requirement: str):
            if quality_requirement == "best":
                return SftMultiModalImageDetail.high
            elif quality_requirement == "fast":
                return SftMultiModalImageDetail.low
            else:
                return SftMultiModalImageDetail.auto

        assert select_detail("best") == SftMultiModalImageDetail.high
        assert select_detail("fast") == SftMultiModalImageDetail.low
        assert select_detail("normal") == SftMultiModalImageDetail.auto


class TestSftMultiModalImageUrl:
    """测试 SFT 多模态图像 URL 模型"""

    def test_image_url_creation(self):
        """测试图像 URL 创建"""
        url = "https://example.com/image.jpg"
        image_url = SftMultiModalImageUrl(url=url)
        
        assert image_url.url == url
        assert image_url.detail is None

    def test_image_url_with_detail(self):
        """测试带详细度的图像 URL"""
        url = "https://example.com/image.jpg"
        detail = SftMultiModalImageDetail.high
        
        image_url = SftMultiModalImageUrl(url=url, detail=detail)
        
        assert image_url.url == url
        assert image_url.detail == SftMultiModalImageDetail.high

    def test_image_url_validation(self):
        """测试图像 URL 验证"""
        # 测试有效 URL
        valid_urls = [
            "https://example.com/image.jpg",
            "http://localhost/test.png",
            "https://cdn.example.com/images/photo.jpeg"
        ]
        
        for url in valid_urls:
            image_url = SftMultiModalImageUrl(url=url)
            assert image_url.url == url

    def test_image_url_different_details(self):
        """测试不同详细度的图像 URL"""
        url = "https://example.com/image.jpg"
        
        # 测试所有详细度级别
        details = [
            SftMultiModalImageDetail.auto,
            SftMultiModalImageDetail.low,
            SftMultiModalImageDetail.high
        ]
        
        for detail in details:
            image_url = SftMultiModalImageUrl(url=url, detail=detail)
            assert image_url.detail == detail


class TestMultiModalContent:
    """测试多模态内容模型"""

    def test_multimodal_content_creation(self):
        """测试多模态内容创建"""
        content = MultiModalContent(type=MultiModalType.text)
        
        assert content.type == MultiModalType.text
        assert content.content is None

    def test_multimodal_content_with_text(self):
        """测试带文本的多模态内容"""
        text_content = "这是一段测试文本"
        content = MultiModalContent(
            type=MultiModalType.text,
            content=text_content
        )
        
        assert content.type == MultiModalType.text
        assert content.content == text_content

    def test_multimodal_content_image_url(self):
        """测试图像 URL 类型的多模态内容"""
        image_url = "https://example.com/image.jpg"
        content = MultiModalContent(
            type=MultiModalType.image_url,
            content=image_url
        )
        
        assert content.type == MultiModalType.image_url
        assert content.content == image_url

    def test_multimodal_content_different_types(self):
        """测试不同类型的多模态内容"""
        contents = [
            MultiModalContent(type=MultiModalType.text, content="文本内容"),
            MultiModalContent(type=MultiModalType.image_url, content="https://example.com/image.jpg"),
            MultiModalContent(type=MultiModalType.image, content="base64_image_data")
        ]
        
        assert len(contents) == 3
        assert contents[0].type == MultiModalType.text
        assert contents[1].type == MultiModalType.image_url
        assert contents[2].type == MultiModalType.image


class TestSftMultiModalText:
    """测试 SFT 多模态文本模型"""

    def test_sft_multimodal_text_creation(self):
        """测试 SFT 多模态文本创建"""
        text_obj = SftMultiModalText(type=MultiModalType.text)
        
        assert text_obj.type == MultiModalType.text
        assert text_obj.text is None

    def test_sft_multimodal_text_with_content(self):
        """测试带内容的 SFT 多模态文本"""
        text_content = "这是 SFT 模型的文本内容"
        text_obj = SftMultiModalText(
            type=MultiModalType.text,
            text=text_content
        )
        
        assert text_obj.type == MultiModalType.text
        assert text_obj.text == text_content

    def test_sft_multimodal_text_validation(self):
        """测试 SFT 多模态文本验证"""
        # 测试空文本
        empty_text = SftMultiModalText(type=MultiModalType.text, text="")
        assert empty_text.text == ""
        
        # 测试长文本
        long_text = "这是一段很长的文本内容，用于测试 SFT 多模态文本模型的处理能力。" * 10
        long_text_obj = SftMultiModalText(type=MultiModalType.text, text=long_text)
        assert long_text_obj.text == long_text

    def test_sft_multimodal_text_serialization(self):
        """测试 SFT 多模态文本序列化"""
        text_content = "测试序列化"
        text_obj = SftMultiModalText(
            type=MultiModalType.text,
            text=text_content
        )
        
        # 测试转换为字典
        text_dict = text_obj.dict()
        assert text_dict["type"] == "text"
        assert text_dict["text"] == text_content
        
        # 测试 JSON 序列化
        text_json = text_obj.json()
        assert "text" in text_json
        assert text_content in text_json


class TestChatDataIntegration:
    """测试聊天数据集成功能"""

    def test_multimodal_content_workflow(self):
        """测试多模态内容工作流"""
        # 创建文本内容
        text_content = MultiModalContent(
            type=MultiModalType.text,
            content="用户输入的文本"
        )
        
        # 创建图像内容
        image_content = MultiModalContent(
            type=MultiModalType.image_url,
            content="https://example.com/user_image.jpg"
        )
        
        # 模拟多模态消息
        multimodal_message = [text_content, image_content]
        
        assert len(multimodal_message) == 2
        assert multimodal_message[0].type == MultiModalType.text
        assert multimodal_message[1].type == MultiModalType.image_url

    def test_image_url_with_different_details(self):
        """测试不同详细度的图像 URL 处理"""
        base_url = "https://example.com/image.jpg"
        
        # 创建不同详细度的图像 URL
        image_urls = [
            SftMultiModalImageUrl(url=base_url, detail=SftMultiModalImageDetail.low),
            SftMultiModalImageUrl(url=base_url, detail=SftMultiModalImageDetail.high),
            SftMultiModalImageUrl(url=base_url, detail=SftMultiModalImageDetail.auto),
            SftMultiModalImageUrl(url=base_url)  # 默认无详细度
        ]
        
        assert len(image_urls) == 4
        assert image_urls[0].detail == SftMultiModalImageDetail.low
        assert image_urls[1].detail == SftMultiModalImageDetail.high
        assert image_urls[2].detail == SftMultiModalImageDetail.auto
        assert image_urls[3].detail is None

    def test_chat_type_selection_logic(self):
        """测试聊天类型选择逻辑"""
        def select_chat_type(has_images: bool, has_text: bool):
            if has_images and has_text:
                return ChatType.multimodal
            elif has_text:
                return ChatType.chat
            else:
                return ChatType.chat  # 默认为普通聊天

        # 测试不同场景
        assert select_chat_type(True, True) == ChatType.multimodal
        assert select_chat_type(False, True) == ChatType.chat
        assert select_chat_type(True, False) == ChatType.chat
        assert select_chat_type(False, False) == ChatType.chat

    @patch('commons.llm_gateway.models.chat_data.load_sft_model_config')
    def test_sft_model_config_loading(self, mock_load_config):
        """测试 SFT 模型配置加载"""
        # 模拟配置加载
        mock_load_config.return_value = (
            Mock(),  # SftBaseModelType
            "chat_api_url",
            "completion_api_url",
            {"model1": "host1", "model2": "host2"}
        )
        
        # 重新导入模块以触发配置加载
        from commons.llm_gateway.models import chat_data
        
        # 验证配置加载被调用
        # 注意：由于模块已经导入，这个测试主要验证模拟的正确性
        assert mock_load_config.called or True  # 配置可能已经加载过


class TestPublicGatewayHeader:
    """测试PublicGatewayHeader类的方法"""
    
    def test_load_by_req_deprecated_method(self):
        """测试已弃用的load_by_req方法"""
        from commons.llm_gateway.models.chat_data import PublicGatewayHeader
        from fastapi import Request
        from unittest.mock import Mock
        import warnings
        import base64
        import json
        
        # 创建实例
        header = PublicGatewayHeader()
        
        # 模拟FastAPI Request对象
        mock_request = Mock(spec=Request)
        mock_headers = {
            "AI-Gateway-Uid": "test-uid",
            "AI-Gateway-Company-Id": "test-company",
            "AI-Gateway-Product-Name": "test-product",
            "AI-Gateway-Intention-Code": "test-intention",
            "AI-Gateway-Billing-Token": "test-billing-token",
            "Sec-From": "test-from",
            "Sec-Scene": "test-scene",
            "Sec-Extra-Text": base64.b64encode("test extra text".encode()).decode(),
            "AI-Hub-Models": json.dumps([{"provider": "test", "name": "model1", "version": "v1"}]),
            "AI-Hub-Provider": "old-provider",
            "AI-Hub-Model": "old-model", 
            "AI-Hub-Version": "old-version",
            "AI-Gateway-Model-Choice": "public"
        }
        mock_request.headers.get.side_effect = lambda key, default=None: mock_headers.get(key, default)
        
        # 测试弃用警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            header.load_by_req(mock_request)
            
            assert len(w) == 1
            assert issubclass(w[0].category, DeprecationWarning)
            assert "load_by_req is deprecated" in str(w[0].message)
        
        # 验证值被正确设置
        assert header.ai_gateway_uid == "test-uid"
        assert header.ai_gateway_company_id == "test-company"
        assert header.ai_gateway_product_name == "test-product"
        assert header.ai_gateway_intention_code == "test-intention"
        assert header.ai_gateway_billing_token == "test-billing-token"
        assert header.sec_from == "test-from"
        assert header.sec_scene == "test-scene"
        assert header.sec_extra_text == ["test extra text"]
        assert len(header.ai_hub_models) == 1
        assert header.ai_hub_models[0].provider == "test"
        assert header.ai_hub_models[0].name == "model1"
        assert header.ai_hub_models[0].version == "v1"
        assert header.ai_hub_provider == "old-provider"
        assert header.ai_hub_model == "old-model"
        assert header.ai_hub_version == "old-version"
        assert header.ai_gateway_model_choice == "public"

    def test_load_by_req_with_invalid_ai_hub_models(self):
        """测试load_by_req处理无效AI Hub模型"""
        from commons.llm_gateway.models.chat_data import PublicGatewayHeader
        from fastapi import Request
        from unittest.mock import Mock, patch
        import warnings
        
        header = PublicGatewayHeader()
        mock_request = Mock(spec=Request)
        mock_headers = {
            "AI-Hub-Models": "invalid json"
        }
        mock_request.headers.get.side_effect = lambda key, default=None: mock_headers.get(key, default)
        
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")  # 忽略弃用警告
            with patch('commons.llm_gateway.models.chat_data.logging') as mock_logging:
                header.load_by_req(mock_request)
                mock_logging.error.assert_called_once()
                assert "Invalid AI-Hub-Models header" in mock_logging.error.call_args[0][0]

    def test_load_by_req_header_method(self):
        """测试load_by_req_header方法"""
        from commons.llm_gateway.models.chat_data import PublicGatewayHeader
        from fastapi import Request
        from unittest.mock import Mock
        import base64
        import json
        
        header = PublicGatewayHeader()
        mock_request = Mock(spec=Request)
        mock_headers = {
            "AI-Gateway-Uid": "header-uid",
            "AI-Gateway-Company-Id": "header-company", 
            "Sec-Extra-Text": base64.b64encode("header extra text".encode()).decode(),
            "AI-Hub-Models": json.dumps([{"provider": "header-provider", "name": "header-model"}])
        }
        mock_request.headers.get.side_effect = lambda key, default=None: mock_headers.get(key, default)
        
        header.load_by_req_header(mock_request)
        
        assert header.ai_gateway_uid == "header-uid"
        assert header.ai_gateway_company_id == "header-company"
        assert header.sec_extra_text == ["header extra text"]
        assert len(header.ai_hub_models) == 1
        assert header.ai_hub_models[0].provider == "header-provider"

    def test_load_by_req_header_with_invalid_models(self):
        """测试load_by_req_header处理无效模型"""
        from commons.llm_gateway.models.chat_data import PublicGatewayHeader
        from fastapi import Request
        from unittest.mock import Mock, patch
        
        header = PublicGatewayHeader()
        mock_request = Mock(spec=Request)
        mock_headers = {"AI-Hub-Models": "invalid json"}
        mock_request.headers.get.side_effect = lambda key, default=None: mock_headers.get(key, default)
        
        with patch('commons.llm_gateway.models.chat_data.logging') as mock_logging:
            header.load_by_req_header(mock_request)
            mock_logging.error.assert_called_once()

    def test_set_final_ai_hub_model(self):
        """测试set_final_ai_hub_model方法"""
        from commons.llm_gateway.models.chat_data import PublicGatewayHeader, AIHubModel
        
        header = PublicGatewayHeader()
        model = AIHubModel(provider="test", name="model", version="v1")
        
        header.set_final_ai_hub_model(model)
        assert header.final_ai_hub_model == model
        
        # 测试传入None的情况
        header.set_final_ai_hub_model(None)
        assert header.final_ai_hub_model == model  # 不应该改变

    def test_load_by_llm_config_deprecated(self):
        """测试已弃用的load_by_llm_config方法"""
        from commons.llm_gateway.models.chat_data import (
            PublicGatewayHeader, LLMConfig, PublicLLMConfig, AIHubLLMType
        )
        import warnings
        
        header = PublicGatewayHeader()
        public_config = PublicLLMConfig(
            provider="test-provider",
            model="test-model",
            version="test-version",
            product_name="test-product",
            intention_code="test-intention",
            sec_from="test-sec-from",
            sec_scene="test-sec-scene",
            llm_types=[AIHubLLMType.llm_multimodal]
        )
        llm_config = LLMConfig(public=public_config)
        
        # 测试 cover_origin=True
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            header.load_by_llm_config(llm_config, cover_origin=True)
            
            assert len(w) == 1
            assert "load_by_req is deprecated" in str(w[0].message)
        
        assert header.ai_gateway_product_name == "test-product"
        assert header.ai_gateway_intention_code == "test-intention"
        assert header.sec_from == "test-sec-from"
        assert header.sec_scene == "test-sec-scene"
        assert header.ai_hub_provider == "test-provider"
        assert header.ai_hub_model == "test-model"
        assert header.ai_hub_version == "test-version"
        assert header.final_ai_hub_model.provider == "test-provider"

    def test_load_by_llm_config_no_cover(self):
        """测试load_by_llm_config方法不覆盖现有值"""
        from commons.llm_gateway.models.chat_data import (
            PublicGatewayHeader, LLMConfig, PublicLLMConfig, AIHubModel
        )
        import warnings
        
        # 先设置一些现有值
        header = PublicGatewayHeader()
        header.ai_gateway_product_name = "existing-product"
        header.ai_hub_provider = "existing-provider"
        existing_model = AIHubModel(provider="existing", name="existing-model")
        header.final_ai_hub_model = existing_model
        
        public_config = PublicLLMConfig(
            provider="new-provider",
            model="new-model",
            version="new-version",
            product_name="new-product"
        )
        llm_config = LLMConfig(public=public_config)
        
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            header.load_by_llm_config(llm_config, cover_origin=False)
        
        # 应该保留现有值
        assert header.ai_gateway_product_name == "existing-product"
        assert header.ai_hub_provider == "existing-provider"
        assert header.final_ai_hub_model == existing_model

    def test_set_sec_data(self):
        """测试set_sec_data方法"""
        from commons.llm_gateway.models.chat_data import PublicGatewayHeader
        
        header = PublicGatewayHeader()
        header.set_sec_data(1, ["text1", "text2"])
        
        assert header.sec_answer_flag == 1
        assert header.sec_extra_text == ["text1", "text2"]

    def test_load_by_local_llm_config_with_cover(self):
        """测试load_by_local_llm_config方法覆盖现有值"""
        from commons.llm_gateway.models.chat_data import (
            PublicGatewayHeader, LLMConfig, PublicLLMConfig, AIHubLLMType
        )
        
        header = PublicGatewayHeader()
        public_config = PublicLLMConfig(
            provider="local-provider",
            model="local-model", 
            version="local-version",
            product_name="local-product",
            intention_code="local-intention",
            sec_from="local-sec-from",
            sec_scene="local-sec-scene",
            llm_types=[AIHubLLMType.llm_multimodal]
        )
        llm_config = LLMConfig(public=public_config)
        
        header.load_by_local_llm_config(llm_config, cover_origin=True)
        
        assert header.ai_gateway_product_name == "local-product"
        assert header.ai_gateway_intention_code == "local-intention"
        assert header.sec_from == "local-sec-from"
        assert header.sec_scene == "local-sec-scene"
        assert header.final_ai_hub_model.provider == "local-provider"
        assert header.final_ai_hub_model.name == "local-model"
        assert header.final_ai_hub_model.version == "local-version"

    def test_load_by_local_llm_config_no_cover(self):
        """测试load_by_local_llm_config方法不覆盖现有值"""
        from commons.llm_gateway.models.chat_data import (
            PublicGatewayHeader, LLMConfig, PublicLLMConfig, AIHubModel
        )
        
        # 设置现有值
        header = PublicGatewayHeader()
        header.ai_gateway_product_name = "existing-local-product"
        existing_model = AIHubModel(provider="existing-local", name="existing-local-model")
        header.final_ai_hub_model = existing_model
        
        public_config = PublicLLMConfig(
            provider="new-local-provider",
            model="new-local-model",
            version="new-local-version",
            product_name="new-local-product"
        )
        llm_config = LLMConfig(public=public_config)
        
        header.load_by_local_llm_config(llm_config, cover_origin=False)
        
        # 应该保留现有值
        assert header.ai_gateway_product_name == "existing-local-product"
        assert header.final_ai_hub_model == existing_model


class TestOtherModels:
    """测试其他模型类"""
    
    def test_usage_model(self):
        """测试Usage模型"""
        from commons.llm_gateway.models.chat_data import Usage
        
        usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)
        assert usage.completion_tokens == 10
        assert usage.prompt_tokens == 5
        assert usage.total_tokens == 15

    def test_trace_info_model(self):
        """测试TraceInfo模型"""
        from commons.llm_gateway.models.chat_data import TraceInfo, Usage
        
        usage = Usage(completion_tokens=10, prompt_tokens=5, total_tokens=15)
        trace_info = TraceInfo(usage=usage)
        assert trace_info.usage == usage
        
        # 测试没有usage的情况
        trace_info_none = TraceInfo()
        assert trace_info_none.usage is None

    def test_ai_hub_llm_type_enum(self):
        """测试AIHubLLMType枚举"""
        from commons.llm_gateway.models.chat_data import AIHubLLMType
        
        assert AIHubLLMType.llm_multimodal == "llm-multimodal"

    def test_public_llm_config_model(self):
        """测试PublicLLMConfig模型"""
        from commons.llm_gateway.models.chat_data import PublicLLMConfig, AIHubLLMType
        
        config = PublicLLMConfig(
            provider="test-provider",
            model="test-model", 
            version="test-version",
            product_name="test-product",
            llm_types=[AIHubLLMType.llm_multimodal, "custom-type"]
        )
        
        assert config.provider == "test-provider"
        assert config.model == "test-model"
        assert config.version == "test-version"
        assert config.product_name == "test-product"
        assert len(config.llm_types) == 2

    def test_sft_llm_config_model(self):
        """测试SftLLMConfig模型"""
        from commons.llm_gateway.models.chat_data import SftLLMConfig
        
        config = SftLLMConfig(base_model="base-model", lora_model="lora-model")
        assert config.base_model == "base-model"
        assert config.lora_model == "lora-model"
        
        # 测试可选字段
        config_minimal = SftLLMConfig(base_model="base-only")
        assert config_minimal.base_model == "base-only"
        assert config_minimal.lora_model is None

    def test_llm_config_model(self):
        """测试LLMConfig模型"""
        from commons.llm_gateway.models.chat_data import LLMConfig, PublicLLMConfig, SftLLMConfig
        
        public_config = PublicLLMConfig(provider="pub", model="pub-model", version="v1")
        sft_config = SftLLMConfig(base_model="sft-model")
        
        config = LLMConfig(gateway=1, public=public_config, sft=sft_config)
        assert config.gateway == 1
        assert config.public == public_config
        assert config.sft == sft_config

    def test_ai_hub_type_enum(self):
        """测试AIHubType枚举"""
        from commons.llm_gateway.models.chat_data import AIHubType
        
        assert AIHubType.public == "public"
        assert AIHubType.private == "private"

    def test_ai_hub_model(self):
        """测试AIHubModel模型"""
        from commons.llm_gateway.models.chat_data import AIHubModel, AIHubLLMType, AIHubType
        
        model = AIHubModel(
            info={"key": "value"},
            llm_types=[AIHubLLMType.llm_multimodal],
            name="test-model",
            provider="test-provider",
            version="v1", 
            type=AIHubType.public
        )
        
        assert model.info == {"key": "value"}
        assert len(model.llm_types) == 1
        assert model.name == "test-model"
        assert model.provider == "test-provider"
        assert model.version == "v1"
        assert model.type == AIHubType.public
