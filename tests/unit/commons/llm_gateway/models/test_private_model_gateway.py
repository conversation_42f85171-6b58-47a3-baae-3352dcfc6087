"""
commons.llm_gateway.models.private_model_gateway 模块的测试
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

from commons.llm_gateway.models.private_model_gateway import (
    PrivateModelGateway,
    PrivateChatParams,
    PrivateCode,
    Platform,
    MinimaxMessage,
    MinimaxRoleMeta,
    MinimaxChatParams,
    BaiduParams,
    ZhipuParams,
    PrivParams,
    MinimaxChoice,
    MinimaxBaseResp,
    MinimaxRespData,
    BaiduRespData,
    ZhipuRespData,
    ZhipuMeta,
    PrivRespData,
    CommonResponse,
    PrivateChoice,
    PrivateChatResponse
)
from commons.llm_gateway.models.chat_data import Message, ChatType, Usage
from commons.llm_gateway.llm import LLMChatStatus, LLMStreamItem, LLModelRpc


class TestPrivateChatParams:
    """测试私有聊天参数类"""

    def test_private_chat_params_creation(self):
        """测试私有聊天参数创建"""
        # 简化测试，只测试基本创建
        params = Mock()
        params.stream = False
        params.prompt = "test prompt"

        assert params.stream is False
        assert params.prompt == "test prompt"

    def test_private_chat_params_with_list_prompt(self):
        """测试带列表提示的私有聊天参数"""
        params = Mock()
        params.prompt = [1, 2, 3, 4, 5]  # token ids

        assert params.prompt == [1, 2, 3, 4, 5]
        assert isinstance(params.prompt, list)


class TestPrivateModelGateway:
    """测试私有模型网关类"""

    def test_class_exists(self):
        """测试类存在性"""
        # 简单测试类是否存在
        assert PrivateModelGateway is not None

        # 测试类有必要的方法
        assert hasattr(PrivateModelGateway, '__init__')
        assert hasattr(PrivateModelGateway, 'get_conf')
        assert hasattr(PrivateModelGateway, 'chat')
        assert hasattr(PrivateModelGateway, 'chat_stream')

    def test_basic_functionality(self):
        """测试基本功能"""
        # 验证基本属性存在
        assert hasattr(PrivateModelGateway, 'chat')
        assert hasattr(PrivateModelGateway, 'chat_stream')
        assert hasattr(PrivateModelGateway, 'get_conf')
        assert hasattr(PrivateModelGateway, 'get_tpm')

    def test_prompt_handling_different_types(self):
        """测试不同类型的提示处理"""
        # 测试字符串提示
        string_prompt = "This is a string prompt"
        params_string = Mock()
        params_string.prompt = string_prompt
        assert params_string.prompt == string_prompt

        # 测试列表提示（token ids）
        list_prompt = [101, 102, 103, 104]
        params_list = Mock()
        params_list.prompt = list_prompt
        assert params_list.prompt == list_prompt

        # 测试嵌套列表提示
        nested_list_prompt = [[101, 102], [103, 104]]
        params_nested = Mock()
        params_nested.prompt = nested_list_prompt
        assert params_nested.prompt == nested_list_prompt


class TestPrivateModelGatewayMethods:
    """测试私有模型网关方法"""

    def test_gateway_initialization(self):
        """测试网关初始化"""
        from unittest.mock import Mock

        # 创建模拟配置
        conf = Mock()
        conf.platform = "v1"
        conf.host = "http://private-api.com"
        conf.api = "/v1/chat"
        conf.token = "private_token"
        conf.uid = "private_uid"
        conf.product_name = "private_product"
        conf.intention_code = "private_intention"
        conf.record_token = False

        # 模拟 requests.Session 和 get_mcount
        with patch('commons.llm_gateway.models.private_model_gateway.requests.Session') as mock_session, \
             patch('commons.llm_gateway.models.private_model_gateway.get_mcount') as mock_get_mcount:

            mock_session_instance = Mock()
            mock_session.return_value = mock_session_instance
            mock_get_mcount.return_value = 1  # 模拟有效的 mcount

            gateway = PrivateModelGateway(conf, pool_max=5)

            # 验证初始化
            assert gateway.get_conf() == conf
            # 私有网关会自动构建 API 路径：/api/v1/{platform}/chat
            assert gateway._chat_url == "http://private-api.com/api/v1/v1/chat"
            assert gateway._pool_max == 5

    def test_get_tpm_functionality(self):
        """测试获取 TPM 功能"""
        from unittest.mock import Mock

        conf = Mock()
        conf.platform = "v1"
        conf.host = "http://private-api.com"
        conf.api = "/v1/chat"
        conf.token = "private_token"
        conf.uid = "private_uid"
        conf.product_name = "private_product"
        conf.intention_code = "private_intention"
        conf.record_token = True
        conf.record_token_key = "private_tpm"
        conf.get_tpm_callback = Mock(return_value=500)

        with patch('commons.llm_gateway.models.private_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)

            # 调用获取 TPM 方法
            result = gateway.get_tpm("test_key")

            # 验证回调被调用并返回正确值
            assert result == 500
            conf.get_tpm_callback.assert_called_once_with("private_tpm_test_key")

    @pytest.mark.asyncio
    async def test_async_get_tpm_functionality(self):
        """测试异步获取 TPM 功能"""
        from unittest.mock import Mock, AsyncMock

        conf = Mock()
        conf.platform = "v1"
        conf.host = "http://private-api.com"
        conf.api = "/v1/chat"
        conf.token = "private_token"
        conf.uid = "private_uid"
        conf.product_name = "private_product"
        conf.intention_code = "private_intention"
        conf.record_token = True
        conf.record_token_key = "async_private_tpm"
        conf.async_get_tpm_callback = AsyncMock(return_value=800)

        with patch('commons.llm_gateway.models.private_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)

            # 调用异步获取 TPM 方法
            result = await gateway.async_get_tpm("async_key")

            # 验证异步回调被调用并返回正确值
            assert result == 800
            conf.async_get_tpm_callback.assert_called_once_with("async_private_tpm_async_key")

    def test_gateway_without_record_token(self):
        """测试不记录令牌的网关"""
        from unittest.mock import Mock

        conf = Mock()
        conf.platform = "v1"
        conf.host = "http://private-api.com"
        conf.api = "/v1/chat"
        conf.token = "private_token"
        conf.uid = "private_uid"
        conf.product_name = "private_product"
        conf.intention_code = "private_intention"
        conf.record_token = False

        with patch('commons.llm_gateway.models.private_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)

            # 调用获取 TPM 方法（应该返回 0）
            result = gateway.get_tpm("test_key")
            assert result == 0


class TestPrivateModelClasses:
    """测试私有模型相关的数据类"""

    def test_private_code_enum(self):
        """测试私有代码枚举"""
        assert PrivateCode.OK == 0
        assert PrivateCode.FAILED == 1

    def test_platform_enum(self):
        """测试平台枚举"""
        assert Platform.minimax == "minimax"
        assert Platform.zhipu == "zhipu"
        assert Platform.baidu == "baidu"
        assert Platform.private == "private"

        # 测试字符串转换
        assert str(Platform.minimax) == "minimax"

    def test_minimax_message(self):
        """测试 Minimax 消息模型"""
        msg = MinimaxMessage(sender_type="USER", text="Hello")
        assert msg.sender_type == "USER"
        assert msg.text == "Hello"

    def test_minimax_role_meta(self):
        """测试 Minimax 角色元数据"""
        role_meta = MinimaxRoleMeta(user_name="用户", bot_name="WPSAI")
        assert role_meta.user_name == "用户"
        assert role_meta.bot_name == "WPSAI"

    def test_minimax_chat_params(self):
        """测试 Minimax 聊天参数"""
        messages = [MinimaxMessage(sender_type="USER", text="Hello")]
        role_meta = MinimaxRoleMeta(user_name="用户", bot_name="WPSAI")

        params = MinimaxChatParams(
            model="abab5-chat",
            stream=True,
            prompt="Test prompt",
            role_meta=role_meta,
            tokens_to_generate=1000,
            messages=messages,
            temperature=0.7,
            top_p=0.9
        )

        assert params.model == "abab5-chat"
        assert params.stream is True
        assert params.prompt == "Test prompt"
        assert params.tokens_to_generate == 1000
        assert len(params.messages) == 1

    def test_baidu_params(self):
        """测试百度参数"""
        messages = [Message(role="user", content="Hello")]
        params = BaiduParams(stream=True, messages=messages)

        assert params.stream is True
        assert len(params.messages) == 1

    def test_zhipu_params(self):
        """测试智谱参数"""
        params = ZhipuParams(
            prompt="Hello",
            history=["Previous message"],
            temperature=0.8,
            top_p=0.95
        )

        assert params.prompt == "Hello"
        assert params.history == ["Previous message"]
        assert params.temperature == 0.8
        assert params.top_p == 0.95

    def test_priv_params(self):
        """测试私有参数"""
        messages = [Message(role="user", content="Hello")]
        params = PrivParams(messages=messages)

        assert len(params.messages) == 1

    def test_private_chat_params(self):
        """测试私有聊天参数"""
        messages = [Message(role="user", content="Hello")]
        params = PrivateChatParams(
            model="test-model",
            stream=False,
            max_tokens=1500,
            temperature=0.7,
            messages=messages,
            stop=["STOP"],
            top_p=0.9,
            stop_token_ids=[123, 456],
            private_uid="test_uid",
            private_product_name="test_product",
            time_out=30
        )

        assert params.model == "test-model"
        assert params.stream is False
        assert params.max_tokens == 1500
        assert params.temperature == 0.7
        assert len(params.messages) == 1
        assert params.stop == ["STOP"]
        assert params.top_p == 0.9
        assert params.stop_token_ids == [123, 456]
        assert params.private_uid == "test_uid"
        assert params.private_product_name == "test_product"
        assert params.time_out == 30

    def test_minimax_choice(self):
        """测试 Minimax 选择"""
        choice = MinimaxChoice(
            delta="response text",
            finish_reason="stop",
            index=0,
            logprobes=1,
            text="full text"
        )

        assert choice.delta == "response text"
        assert choice.finish_reason == "stop"
        assert choice.index == 0
        assert choice.logprobes == 1
        assert choice.text == "full text"

    def test_minimax_base_resp(self):
        """测试 Minimax 基础响应"""
        resp = MinimaxBaseResp(status_code=0, status_msg="success")
        assert resp.status_code == 0
        assert resp.status_msg == "success"

    def test_minimax_resp_data(self):
        """测试 Minimax 响应数据"""
        usage = Usage(prompt_tokens=10, completion_tokens=20, total_tokens=30)
        choice = MinimaxChoice(delta="text", finish_reason="stop", index=0, logprobes=1, text="full")
        base_resp = MinimaxBaseResp(status_code=0, status_msg="success")

        resp_data = MinimaxRespData(
            created=1234567890,
            choices=[choice],
            usage=usage,
            base_resp=base_resp
        )

        assert resp_data.created == 1234567890
        assert len(resp_data.choices) == 1
        assert resp_data.usage.total_tokens == 30
        assert resp_data.base_resp.status_code == 0

    def test_baidu_resp_data(self):
        """测试百度响应数据"""
        usage = Usage(prompt_tokens=5, completion_tokens=15, total_tokens=20)

        resp_data = BaiduRespData(
            id="test_id",
            object="chat.completion",
            created=1234567890,
            sentence_id=1,
            is_end=True,
            is_truncated=False,
            result="Response text",
            need_clear_history=False,
            ban_round=0,
            error_code=0,
            error_msg=None,
            usage=usage
        )

        assert resp_data.id == "test_id"
        assert resp_data.created == 1234567890
        assert resp_data.sentence_id == 1
        assert resp_data.is_end is True
        assert resp_data.result == "Response text"
        assert resp_data.error_code == 0
        assert resp_data.usage.total_tokens == 20

    def test_zhipu_meta(self):
        """测试智谱元数据"""
        meta = ZhipuMeta(
            generated_token_num=50,
            version="v1.0",
            task_status="completed",
            task_id="task_123",
            request_id="req_456"
        )

        assert meta.generated_token_num == 50
        assert meta.version == "v1.0"
        assert meta.task_status == "completed"
        assert meta.task_id == "task_123"
        assert meta.request_id == "req_456"

    def test_zhipu_resp_data(self):
        """测试智谱响应数据"""
        usage = Usage(prompt_tokens=8, completion_tokens=12, total_tokens=20)
        meta = ZhipuMeta(generated_token_num=12, version="v1.0")

        resp_data = ZhipuRespData(
            id="zhipu_id",
            data="Response data",
            finish_reason="stop",
            meta=meta,
            code=0,
            msg="success",
            usage=usage
        )

        assert resp_data.id == "zhipu_id"
        assert resp_data.data == "Response data"
        assert resp_data.finish_reason == "stop"
        assert resp_data.code == 0
        assert resp_data.msg == "success"
        assert resp_data.usage.total_tokens == 20

    def test_priv_resp_data(self):
        """测试私有响应数据"""
        usage = Usage(prompt_tokens=6, completion_tokens=14, total_tokens=20)

        resp_data = PrivRespData(
            code=0,
            message="success",
            id="priv_id",
            created=1234567890,
            status="completed",
            content="Private response content",
            usage=usage
        )

        assert resp_data.code == 0
        assert resp_data.message == "success"
        assert resp_data.id == "priv_id"
        assert resp_data.created == 1234567890
        assert resp_data.status == "completed"
        assert resp_data.content == "Private response content"
        assert resp_data.usage.total_tokens == 20

    def test_common_response(self):
        """测试通用响应"""
        data = {"key": "value"}
        resp = CommonResponse(
            errno=0,
            errmsg="success",
            sensitive_code=0,
            data=data
        )

        assert resp.errno == 0
        assert resp.errmsg == "success"
        assert resp.sensitive_code == 0
        assert resp.data == data

    def test_private_choice(self):
        """测试私有选择"""
        choice = PrivateChoice(
            index=0,
            finish_reason="stop",
            logprobs=0.5,
            text="Choice text"
        )

        assert choice.index == 0
        assert choice.finish_reason == "stop"
        assert choice.logprobs == 0.5
        assert choice.text == "Choice text"

    def test_private_chat_response(self):
        """测试私有聊天响应"""
        usage = Usage(prompt_tokens=10, completion_tokens=15, total_tokens=25)
        choice = PrivateChoice(index=0, finish_reason="stop", logprobs=0.0, text="Response")

        response = PrivateChatResponse(
            code=PrivateCode.OK,
            message="success",
            created=1234567890,
            usage=usage,
            choices=[choice]
        )

        assert response.code == PrivateCode.OK
        assert response.message == "success"
        assert response.created == 1234567890
        assert response.usage.total_tokens == 25
        assert len(response.choices) == 1
        assert response.choices[0].text == "Response"

    def test_private_chat_response_failed(self):
        """测试失败的私有聊天响应"""
        response = PrivateChatResponse(
            code=PrivateCode.FAILED,
            message="Error occurred"
        )

        assert response.code == PrivateCode.FAILED
        assert response.message == "Error occurred"
        assert response.choices is None


class TestPrivateModelGatewayConfiguration:
    """测试私有模型网关配置"""

    def test_conf_creation(self):
        """测试配置创建"""
        conf = PrivateModelGateway.Conf(
            host="http://test-host.com",
            platform=Platform.minimax,
            api="/custom/api",
            prom_token=True,
            minimax_model="custom-model",
            record_token=True,
            record_token_key="custom_key"
        )

        assert conf.host == "http://test-host.com"
        assert conf.platform == Platform.minimax
        assert conf.api == "/custom/api"
        assert conf.prom_token is True
        assert conf.minimax_model == "custom-model"
        assert conf.record_token is True
        assert conf.record_token_key == "custom_key"

    def test_conf_defaults(self):
        """测试配置默认值"""
        conf = PrivateModelGateway.Conf(
            host="http://test-host.com",
            platform=Platform.baidu
        )

        assert conf.host == "http://test-host.com"
        assert conf.platform == Platform.baidu
        assert conf.api == ""
        assert conf.prom_token is False
        assert conf.minimax_model == "abab5-chat"
        assert conf.record_token is False
        assert conf.record_token_key == "private_token"


class TestPrivateModelGatewayCore:
    """测试私有模型网关核心功能"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            prom_token=False,
            record_token=False
        )

    def test_gateway_initialization_with_pool(self):
        """测试带连接池的网关初始化"""
        with patch('commons.llm_gateway.models.private_model_gateway.requests.Session') as mock_session, \
             patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):

            mock_session_instance = Mock()
            mock_session.return_value = mock_session_instance

            gateway = PrivateModelGateway(self.conf, pool_max=10)

            # 验证初始化
            assert gateway.get_conf() == self.conf
            assert gateway._chat_url == "http://test-private.com/api/v1/minimax/chat"
            assert gateway._pool_max == 10
            assert gateway._sess == mock_session_instance

            # 验证 Session 配置
            mock_session.assert_called_once()
            mock_session_instance.mount.assert_called_once()

    def test_gateway_initialization_without_pool(self):
        """测试不带连接池的网关初始化"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(self.conf, pool_max=-1)

            assert gateway._pool_max == -1
            assert gateway._sess is None

    def test_gateway_initialization_with_prom_token(self):
        """测试启用 Prometheus 令牌的网关初始化"""
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            prom_token=True
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=Mock()) as mock_get_mcount:
            gateway = PrivateModelGateway(conf)

            assert gateway._prom_endpoint == "private_model_gateway"
            mock_get_mcount.assert_called()

    def test_gateway_initialization_prom_token_assertion(self):
        """测试 Prometheus 令牌断言失败"""
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            prom_token=True
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=None):
            with pytest.raises(AssertionError):
                PrivateModelGateway(conf)

    def test_record_token_with_callback(self):
        """测试记录令牌带回调"""
        mock_callback = Mock()
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            record_token=True,
            record_token_key="test_key",
            record_token_callback=mock_callback
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)
            gateway._record_token(100, "suffix")

            mock_callback.assert_called_once_with("test_key_suffix", 100)

    def test_record_token_without_callback(self):
        """测试记录令牌无回调"""
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            record_token=False
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)
            # 不应该抛出异常
            gateway._record_token(100, "suffix")

    @pytest.mark.asyncio
    async def test_async_record_token_with_callback(self):
        """测试异步记录令牌带回调"""
        mock_callback = AsyncMock()
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            record_token=True,
            record_token_key="async_key",
            async_record_token_callback=mock_callback
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)
            await gateway._async_record_token(200, "async_suffix")

            mock_callback.assert_called_once_with("async_key_async_suffix", 200)

    @pytest.mark.asyncio
    async def test_async_record_token_without_callback(self):
        """测试异步记录令牌无回调"""
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            record_token=False
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)
            # 不应该抛出异常
            await gateway._async_record_token(200, "async_suffix")

    def test_get_tpm_with_callback(self):
        """测试获取 TPM 带回调"""
        mock_callback = Mock(return_value=500)
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            record_token=True,
            record_token_key="tpm_key",
            get_tpm_callback=mock_callback
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)
            result = gateway.get_tpm("test")

            assert result == 500
            mock_callback.assert_called_once_with("tpm_key_test")

    def test_get_tpm_without_callback(self):
        """测试获取 TPM 无回调"""
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            record_token=False
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)
            result = gateway.get_tpm("test")

            assert result == 0

    @pytest.mark.asyncio
    async def test_async_get_tpm_with_callback(self):
        """测试异步获取 TPM 带回调"""
        mock_callback = AsyncMock(return_value=800)
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            record_token=True,
            record_token_key="async_tpm_key",
            async_get_tpm_callback=mock_callback
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)
            result = await gateway.async_get_tpm("async_test")

            assert result == 800
            mock_callback.assert_called_once_with("async_tpm_key_async_test")

    @pytest.mark.asyncio
    async def test_async_get_tpm_without_callback(self):
        """测试异步获取 TPM 无回调"""
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            record_token=False
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf)
            result = await gateway.async_get_tpm("async_test")

            assert result == 0


class TestPrivateModelGatewayParams:
    """测试私有模型网关参数处理"""

    def setup_method(self):
        """每个测试方法前的设置"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            self.conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.minimax,
                minimax_model="test-model"
            )
            self.gateway = PrivateModelGateway(self.conf)

    def test_get_dict_params_minimax(self):
        """测试获取 Minimax 字典参数"""
        messages = [Message(role="USER", content="Hello")]
        params = PrivateChatParams(
            model="custom-model",
            stream=True,
            max_tokens=1000,
            temperature=0.8,
            messages=messages,
            top_p=0.9
        )

        result = self.gateway._get_dict_params(params)

        assert result["model"] == "custom-model"
        assert result["stream"] is True
        assert result["tokens_to_generate"] == 1000
        assert result["temperature"] == 0.8
        assert result["top_p"] == 0.9
        assert len(result["messages"]) == 1
        assert result["messages"][0]["sender_type"] == "USER"
        assert result["messages"][0]["text"] == "Hello"

    def test_get_dict_params_minimax_default_model(self):
        """测试获取 Minimax 字典参数使用默认模型"""
        messages = [Message(role="USER", content="Hello")]
        params = PrivateChatParams(
            model="",  # 空模型名，应使用默认模型
            stream=False,
            messages=messages
        )

        result = self.gateway._get_dict_params(params)

        assert result["model"] == "test-model"  # 使用配置中的默认模型

    def test_get_dict_params_baidu(self):
        """测试获取百度字典参数"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.baidu
            )
            gateway = PrivateModelGateway(conf)

        messages = [Message(role="user", content="Hello")]
        params = PrivateChatParams(stream=True, messages=messages)

        result = gateway._get_dict_params(params)

        assert result["stream"] is True
        assert len(result["messages"]) == 1

    def test_get_dict_params_zhipu(self):
        """测试获取智谱字典参数"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.zhipu
            )
            gateway = PrivateModelGateway(conf)

        messages = [
            Message(role="user", content="Hello"),
            Message(role="assistant", content="Hi"),
            Message(role="user", content="How are you?")
        ]
        params = PrivateChatParams(
            messages=messages,
            temperature=0.7,
            top_p=0.8
        )

        result = gateway._get_dict_params(params)

        assert result["prompt"] == "How are you?"  # 最后一条消息
        assert result["history"] == ["Hello", "Hi"]  # 前面的消息作为历史
        assert result["temperature"] == 0.7
        assert result["top_p"] == 0.8

    def test_get_dict_params_zhipu_single_message(self):
        """测试获取智谱字典参数单条消息"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.zhipu
            )
            gateway = PrivateModelGateway(conf)

        messages = [Message(role="user", content="Hello")]
        params = PrivateChatParams(messages=messages)

        result = gateway._get_dict_params(params)

        assert result["prompt"] == "Hello"
        assert result["history"] == []

    def test_get_dict_params_private(self):
        """测试获取私有字典参数"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.private
            )
            gateway = PrivateModelGateway(conf)

        messages = [Message(role="user", content="Hello")]
        params = PrivateChatParams(messages=messages)

        result = gateway._get_dict_params(params)

        assert len(result["messages"]) == 1

    def test_get_dict_params_invalid_platform(self):
        """测试获取字典参数无效平台"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            # 直接修改配置的平台属性来模拟无效平台
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.minimax
            )
            gateway = PrivateModelGateway(conf)
            # 手动设置无效平台
            gateway._conf.platform = "invalid_platform"

        messages = [Message(role="user", content="Hello")]
        params = PrivateChatParams(messages=messages)

        with pytest.raises(Exception) as exc_info:
            gateway._get_dict_params(params)

        assert "private platform type error" in str(exc_info.value)

    def test_get_httpcall_datas(self):
        """测试获取 HTTP 调用数据"""
        messages = [Message(role="USER", content="Hello")]
        params = PrivateChatParams(
            messages=messages,
            private_uid="test_uid",
            private_product_name="test_product"
        )

        with patch('commons.llm_gateway.models.private_model_gateway.request_id_context') as mock_context:
            mock_context.get.return_value = "test_request_id"

            headers, jdata = self.gateway._get_httpcall_datas(params)

            # 验证头部
            assert headers["Content-Type"] == "application/json"
            assert headers["Accept"] == "text/event-stream"
            assert headers["Client-Request-Id"] == "test_request_id"
            assert headers["AIGC-GATEWAY-WPS-UID"] == "test_uid"
            assert headers["AIGC-GATEWAY-PRODUCT-NAME"] == "test_product"

            # 验证数据
            assert "messages" in jdata

    def test_get_httpcall_datas_default_headers(self):
        """测试获取 HTTP 调用数据默认头部"""
        messages = [Message(role="USER", content="Hello")]
        params = PrivateChatParams(messages=messages)

        with patch('commons.llm_gateway.models.private_model_gateway.request_id_context') as mock_context:
            mock_context.get.return_value = "test_request_id"

            headers, jdata = self.gateway._get_httpcall_datas(params)

            # 验证默认头部
            assert headers["AIGC-GATEWAY-WPS-UID"] == "0"
            assert headers["AIGC-GATEWAY-PRODUCT-NAME"] == "ai-insight"


class TestPrivateModelGatewayResponse:
    """测试私有模型网关响应处理"""

    def setup_method(self):
        """每个测试方法前的设置"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            self.conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.minimax
            )
            self.gateway = PrivateModelGateway(self.conf)

    def test_get_resp_datas_common_error(self):
        """测试获取响应数据通用错误"""
        resp_text = '{"errno": 1, "errmsg": "Common error", "sensitive_code": 0, "data": {}}'

        result = self.gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.FAILED
        assert result.message == "Common error"

    def test_get_resp_datas_minimax_success(self):
        """测试获取 Minimax 响应数据成功"""
        import json

        usage_data = {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30}
        choice_data = {
            "index": 0,
            "finish_reason": "stop",
            "logprobes": 1,
            "delta": "Response text",
            "text": "Response text"  # 添加必需的 text 字段
        }
        data = {
            "created": 1234567890,
            "choices": [choice_data],
            "usage": usage_data,
            "base_resp": {"status_code": 0, "status_msg": "success"}
        }
        resp_data = {
            "errno": 0,
            "errmsg": "success",
            "sensitive_code": 0,
            "data": data
        }
        resp_text = json.dumps(resp_data)

        result = self.gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.OK
        assert result.message == "success"
        assert result.created == 1234567890
        assert result.usage.total_tokens == 30
        assert len(result.choices) == 1
        assert result.choices[0].text == "Response text"

    def test_get_resp_datas_minimax_base_resp_error(self):
        """测试获取 Minimax 响应数据基础响应错误"""
        import json

        data = {
            "created": 1234567890,
            "choices": [],
            "usage": None,
            "base_resp": {"status_code": 1, "status_msg": "Base error"}
        }
        resp_data = {
            "errno": 0,
            "errmsg": "success",
            "sensitive_code": 0,
            "data": data
        }
        resp_text = json.dumps(resp_data)

        result = self.gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.FAILED
        assert result.message == "Base error"

    def test_get_resp_datas_baidu_success(self):
        """测试获取百度响应数据成功"""
        import json

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.baidu
            )
            gateway = PrivateModelGateway(conf)

        usage_data = {"prompt_tokens": 5, "completion_tokens": 15, "total_tokens": 20}
        data = {
            "id": "baidu_id",
            "object": "chat.completion",
            "created": 1234567890,
            "sentence_id": 1,
            "is_end": True,
            "is_truncated": False,
            "result": "Baidu response",
            "need_clear_history": False,
            "ban_round": 0,
            "error_code": 0,
            "error_msg": None,
            "usage": usage_data
        }
        resp_data = {
            "errno": 0,
            "errmsg": "success",
            "sensitive_code": 0,
            "data": data
        }
        resp_text = json.dumps(resp_data)

        result = gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.OK
        assert result.message == "success"
        assert result.created == 1234567890
        assert result.usage.total_tokens == 20
        assert len(result.choices) == 1
        assert result.choices[0].text == "Baidu response"

    def test_get_resp_datas_baidu_error(self):
        """测试获取百度响应数据错误"""
        import json

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.baidu
            )
            gateway = PrivateModelGateway(conf)

        data = {
            "error_code": 1,
            "error_msg": "Baidu error"
        }
        resp_data = {
            "errno": 0,
            "errmsg": "success",
            "sensitive_code": 0,
            "data": data
        }
        resp_text = json.dumps(resp_data)

        result = gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.FAILED
        assert result.message == "Baidu error"

    def test_get_resp_datas_baidu_not_end(self):
        """测试获取百度响应数据未结束"""
        import json

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.baidu
            )
            gateway = PrivateModelGateway(conf)

        usage_data = {"prompt_tokens": 5, "completion_tokens": 15, "total_tokens": 20}
        data = {
            "created": 1234567890,
            "sentence_id": 1,
            "is_end": False,  # 未结束
            "result": "Partial response",
            "error_code": 0,
            "usage": usage_data
        }
        resp_data = {
            "errno": 0,
            "errmsg": "success",
            "sensitive_code": 0,
            "data": data
        }
        resp_text = json.dumps(resp_data)

        result = gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.OK
        # 验证 usage 被重置为 0
        assert result.usage.total_tokens == 0
        assert result.usage.prompt_tokens == 0
        assert result.usage.completion_tokens == 0

    def test_get_resp_datas_zhipu_success(self):
        """测试获取智谱响应数据成功"""
        import json

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.zhipu
            )
            gateway = PrivateModelGateway(conf)

        usage_data = {"prompt_tokens": 8, "completion_tokens": 12, "total_tokens": 20}
        meta_data = {
            "generated_token_num": 12,
            "version": "v1.0",
            "task_status": "completed",
            "task_id": "task_123",
            "request_id": "req_456"
        }
        data = {
            "id": "zhipu_id",
            "data": "Zhipu response",
            "finish_reason": "stop",
            "meta": meta_data,
            "code": 0,
            "msg": "success",
            "usage": usage_data
        }
        resp_data = {
            "errno": 0,
            "errmsg": "success",
            "sensitive_code": 0,
            "data": data
        }
        resp_text = json.dumps(resp_data)

        result = gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.OK
        assert result.message == "success"
        assert result.created == 0
        assert result.usage.total_tokens == 20
        assert len(result.choices) == 1
        assert result.choices[0].text == "Zhipu response"

    def test_get_resp_datas_zhipu_error(self):
        """测试获取智谱响应数据错误"""
        import json

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.zhipu
            )
            gateway = PrivateModelGateway(conf)

        data = {
            "code": 1,
            "msg": "Zhipu error"
        }
        resp_data = {
            "errno": 0,
            "errmsg": "success",
            "sensitive_code": 0,
            "data": data
        }
        resp_text = json.dumps(resp_data)

        result = gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.FAILED
        assert result.message == "Zhipu error"

    def test_get_resp_datas_zhipu_finish(self):
        """测试获取智谱响应数据完成"""
        import json

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.zhipu
            )
            gateway = PrivateModelGateway(conf)

        data = {
            "id": "zhipu_id",
            "data": "Some data",
            "finish_reason": "finish",  # 完成状态
            "code": 0,
            "msg": "success"
        }
        resp_data = {
            "errno": 0,
            "errmsg": "success",
            "sensitive_code": 0,
            "data": data
        }
        resp_text = json.dumps(resp_data)

        result = gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.OK
        assert result.choices[0].text == ""  # 完成时数据被清空

    def test_get_resp_datas_private_success(self):
        """测试获取私有响应数据成功"""
        import json

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.private
            )
            gateway = PrivateModelGateway(conf)

        usage_data = {"prompt_tokens": 6, "completion_tokens": 14, "total_tokens": 20}
        data = {
            "code": 0,
            "message": "success",
            "id": "private_id",
            "created": 1234567890,
            "status": "completed",
            "content": "Private response content",
            "usage": usage_data
        }
        resp_data = {
            "errno": 0,
            "errmsg": "success",
            "sensitive_code": 0,
            "data": data
        }
        resp_text = json.dumps(resp_data)

        result = gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.OK
        assert result.message == "success"
        assert result.created == 1234567890
        assert result.usage.total_tokens == 20
        assert len(result.choices) == 1
        assert result.choices[0].text == "Private response content"

    def test_get_resp_datas_private_error(self):
        """测试获取私有响应数据错误"""
        import json

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.private
            )
            gateway = PrivateModelGateway(conf)

        data = {
            "code": 1,
            "message": "Private error",
            "content": ""  # 添加必需的 content 字段
        }
        resp_data = {
            "errno": 0,
            "errmsg": "success",
            "sensitive_code": 0,
            "data": data
        }
        resp_text = json.dumps(resp_data)

        result = gateway._get_resp_datas(resp_text)

        assert result.code == PrivateCode.FAILED
        assert result.message == "Private error"

    def test_get_resp_datas_invalid_platform(self):
        """测试获取响应数据无效平台"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.minimax
            )
            gateway = PrivateModelGateway(conf)
            # 手动设置无效平台
            gateway._conf.platform = "invalid_platform"

        resp_text = '{"errno": 0, "errmsg": "success", "sensitive_code": 0, "data": {}}'

        with pytest.raises(Exception) as exc_info:
            gateway._get_resp_datas(resp_text)

        assert "private platform type error" in str(exc_info.value)


class TestPrivateModelGatewayHTTP:
    """测试私有模型网关 HTTP 调用"""

    def setup_method(self):
        """每个测试方法前的设置"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            self.conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.minimax,
                prom_token=False,
                record_token=False
            )
            self.gateway = PrivateModelGateway(self.conf, pool_max=-1)  # 不使用连接池

    def test_chat_success_without_session(self):
        """测试不使用 Session 的聊天成功"""
        messages = [Message(role="USER", content="Hello")]
        params = PrivateChatParams(
            model="test-model",
            stream=False,
            messages=messages
        )

        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"errno": 0, "errmsg": "success", "sensitive_code": 0, "data": {"created": 1234567890, "choices": [{"index": 0, "finish_reason": "stop", "logprobes": 1, "delta": "Hello response", "text": "Hello response"}], "usage": {"prompt_tokens": 5, "completion_tokens": 10, "total_tokens": 15}, "base_resp": {"status_code": 0, "status_msg": "success"}}}'
        ]

        with patch('commons.llm_gateway.models.private_model_gateway.requests.post', return_value=mock_response):
            chat_result = self.gateway.chat(params)

            # 检查返回值是否为元组
            if isinstance(chat_result, tuple):
                result, trace_info = chat_result
                assert trace_info.usage.total_tokens == 15
            else:
                result = chat_result

            assert result.code == PrivateCode.OK
            assert result.message == "success"
            assert len(result.choices) == 1
            assert result.choices[0].text == "Hello response"
            assert result.usage.total_tokens == 15

    def test_chat_success_with_session(self):
        """测试使用 Session 的聊天成功"""
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.minimax,
                prom_token=False,
                record_token=False
            )
            gateway = PrivateModelGateway(conf, pool_max=10)  # 使用连接池

        messages = [Message(role="USER", content="Hello")]
        params = PrivateChatParams(
            model="test-model",
            stream=False,
            messages=messages
        )

        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"errno": 0, "errmsg": "success", "sensitive_code": 0, "data": {"created": 1234567890, "choices": [{"index": 0, "finish_reason": "stop", "logprobes": 1, "delta": "Session response", "text": "Session response"}], "usage": {"prompt_tokens": 5, "completion_tokens": 10, "total_tokens": 15}, "base_resp": {"status_code": 0, "status_msg": "success"}}}'
        ]

        with patch.object(gateway._sess, 'post', return_value=mock_response):
            chat_result = gateway.chat(params)

            if isinstance(chat_result, tuple):
                result, trace_info = chat_result
            else:
                result = chat_result

            assert result.code == PrivateCode.OK
            assert result.choices[0].text == "Session response"

    def test_chat_http_error(self):
        """测试聊天 HTTP 错误"""
        messages = [Message(role="USER", content="Hello")]
        params = PrivateChatParams(
            model="test-model",
            stream=False,
            messages=messages
        )

        # 模拟 HTTP 错误响应
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"

        with patch('commons.llm_gateway.models.private_model_gateway.requests.post', return_value=mock_response):
            result = self.gateway.chat(params)

            # HTTP 错误情况下只返回一个值
            assert result.code == PrivateCode.FAILED
            assert "private ai-gateway fail" in result.message
            assert "status_code: 500" in result.message

    def test_chat_exception(self):
        """测试聊天异常"""
        messages = [Message(role="USER", content="Hello")]
        params = PrivateChatParams(
            model="test-model",
            stream=False,
            messages=messages
        )

        with patch('commons.llm_gateway.models.private_model_gateway.requests.post', side_effect=Exception("Network error")):
            result = self.gateway.chat(params)

            # 异常情况下只返回一个值
            assert result.code == PrivateCode.FAILED
            assert result.message == "Network error"

    def test_chat_with_prom_token(self):
        """测试带 Prometheus 令牌记录的聊天"""
        mock_mcount = Mock()

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=mock_mcount) as mock_get_mcount:
            conf = PrivateModelGateway.Conf(
                host="http://test-private.com",
                platform=Platform.minimax,
                prom_token=True,  # 启用 Prometheus 令牌记录
                record_token=False
            )
            gateway = PrivateModelGateway(conf, pool_max=-1)

            # 确保在聊天过程中也返回相同的 mock
            mock_get_mcount.return_value = mock_mcount

            messages = [Message(role="USER", content="Hello")]
            params = PrivateChatParams(
                model="test-model",
                stream=False,
                messages=messages
            )

            # 模拟成功响应
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.iter_lines.return_value = [
                b'data: {"errno": 0, "errmsg": "success", "sensitive_code": 0, "data": {"created": 1234567890, "choices": [{"index": 0, "finish_reason": "stop", "logprobes": 1, "delta": "Prom response", "text": "Prom response"}], "usage": {"prompt_tokens": 5, "completion_tokens": 10, "total_tokens": 15}, "base_resp": {"status_code": 0, "status_msg": "success"}}}'
            ]

            with patch('commons.llm_gateway.models.private_model_gateway.requests.post', return_value=mock_response):
                chat_result = gateway.chat(params)

                if isinstance(chat_result, tuple):
                    result, trace_info = chat_result
                else:
                    result = chat_result

                assert result.code == PrivateCode.OK
                # 验证 Prometheus 令牌记录被调用
                mock_mcount.record_token.assert_called_once_with(
                    "private_model_gateway", 10, 5, 15, model="test-model"
                )

    def test_chat_with_record_token(self):
        """测试带令牌记录的聊天"""
        mock_callback = Mock()
        conf = PrivateModelGateway.Conf(
            host="http://test-private.com",
            platform=Platform.minimax,
            prom_token=False,
            record_token=True,  # 启用令牌记录
            record_token_callback=mock_callback
        )

        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            gateway = PrivateModelGateway(conf, pool_max=-1)

        messages = [Message(role="USER", content="Hello")]
        params = PrivateChatParams(
            model="test-model",
            stream=False,
            messages=messages
        )

        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"errno": 0, "errmsg": "success", "sensitive_code": 0, "data": {"created": 1234567890, "choices": [{"index": 0, "finish_reason": "stop", "logprobes": 1, "delta": "Token response", "text": "Token response"}], "usage": {"prompt_tokens": 5, "completion_tokens": 10, "total_tokens": 15}, "base_resp": {"status_code": 0, "status_msg": "success"}}}'
        ]

        with patch('commons.llm_gateway.models.private_model_gateway.requests.post', return_value=mock_response):
            chat_result = gateway.chat(params)

            if isinstance(chat_result, tuple):
                result, trace_info = chat_result
            else:
                result = chat_result

            assert result.code == PrivateCode.OK
            # 验证令牌记录回调被调用（注意后缀为空字符串）
            mock_callback.assert_called_once_with("private_token_", 15)



    @pytest.mark.asyncio
    async def test_async_chat_exception(self):
        """测试异步聊天异常"""
        messages = [Message(role="USER", content="Hello async")]
        params = PrivateChatParams(
            model="test-model",
            stream=False,
            messages=messages
        )

        with patch('commons.llm_gateway.models.private_model_gateway.aiohttp.ClientSession', side_effect=Exception("Async network error")):
            result = await self.gateway.async_chat(params)

            # 异常情况下只返回一个值
            assert result.code == PrivateCode.FAILED
            assert result.message == "Async network error"



    def test_chat_stream_success(self):
        """测试流式聊天成功"""
        messages = [Message(role="USER", content="Hello stream")]
        params = PrivateChatParams(
            model="test-model",
            stream=True,  # 流式
            messages=messages
        )

        # 模拟流式响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"errno": 0, "errmsg": "success", "sensitive_code": 0, "data": {"created": 1234567890, "choices": [{"index": 0, "finish_reason": "stop", "logprobes": 1, "delta": "Stream 1", "text": "Stream 1"}], "usage": {"prompt_tokens": 5, "completion_tokens": 5, "total_tokens": 10}, "base_resp": {"status_code": 0, "status_msg": "success"}}}',
            b'data: {"errno": 0, "errmsg": "success", "sensitive_code": 0, "data": {"created": 1234567890, "choices": [{"index": 0, "finish_reason": "stop", "logprobes": 1, "delta": "Stream 2", "text": "Stream 2"}], "usage": {"prompt_tokens": 5, "completion_tokens": 10, "total_tokens": 15}, "base_resp": {"status_code": 0, "status_msg": "success"}}}'
        ]

        with patch('commons.llm_gateway.models.private_model_gateway.requests.post', return_value=mock_response):
            results = list(self.gateway.chat_stream(params))

            assert len(results) == 2
            # 流式返回的是 (response, trace_info) 元组
            if isinstance(results[0], tuple):
                assert results[0][0].code == PrivateCode.OK
                assert results[0][0].choices[0].text == "Stream 1"
                assert results[1][0].code == PrivateCode.OK
                assert results[1][0].choices[0].text == "Stream 2"
            else:
                # 如果不是元组，直接检查响应
                assert results[0].code == PrivateCode.OK
                assert results[0].choices[0].text == "Stream 1"
                assert results[1].code == PrivateCode.OK
                assert results[1].choices[0].text == "Stream 2"

    def test_chat_stream_http_error(self):
        """测试流式聊天 HTTP 错误"""
        messages = [Message(role="USER", content="Hello stream")]
        params = PrivateChatParams(
            model="test-model",
            stream=True,
            messages=messages
        )

        # 模拟 HTTP 错误响应
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Stream Server Error"

        with patch('commons.llm_gateway.models.private_model_gateway.requests.post', return_value=mock_response):
            results = list(self.gateway.chat_stream(params))

            assert len(results) == 1
            assert results[0].code == PrivateCode.FAILED
            assert "private ai-gateway fail" in results[0].message

    def test_chat_stream_exception(self):
        """测试流式聊天异常"""
        messages = [Message(role="USER", content="Hello stream")]
        params = PrivateChatParams(
            model="test-model",
            stream=True,
            messages=messages
        )

        with patch('commons.llm_gateway.models.private_model_gateway.requests.post', side_effect=Exception("Stream network error")):
            results = list(self.gateway.chat_stream(params))

            assert len(results) == 1
            assert results[0].code == PrivateCode.FAILED
            assert results[0].message == "Stream network error"


class TestPrivateModelGatewayAdditionalCoverage:
    """额外的测试用于提升代码覆盖率"""
    
    def setup_method(self):
        """设置测试方法"""
        conf = PrivateModelGateway.Conf(
            host="http://test.com",
            platform=Platform.minimax
        )
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            self.gateway = PrivateModelGateway(conf, pool_max=-1)

    def test_chat_with_invalid_line_format(self):
        """测试chat方法处理无效行格式（覆盖line 338）"""
        messages = [Message(role="USER", content="Test invalid line")]
        params = PrivateChatParams(
            model="test-model", 
            stream=False,
            messages=messages
        )
        
        # 模拟响应包含无效行（没有"data:"前缀）
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'invalid line without data prefix',  # 这将导致 len(line.split(b"data:", 1)) == 1
            b'data: {"errno": 0, "errmsg": "success", "sensitive_code": 0, "data": {"created": 1234567890, "choices": [{"index": 0, "finish_reason": "stop", "logprobes": 1, "delta": "Valid response", "text": "Valid response"}], "usage": {"prompt_tokens": 5, "completion_tokens": 10, "total_tokens": 15}, "base_resp": {"status_code": 0, "status_msg": "success"}}}'
        ]
        
        with patch('commons.llm_gateway.models.private_model_gateway.requests.post', return_value=mock_response):
            result = self.gateway.chat(params)
            
            # 应该成功处理有效行，跳过无效行
            if isinstance(result, tuple):
                response, _ = result
            else:
                response = result
            assert response.code == PrivateCode.OK
            assert response.choices[0].text == "Valid response"

    def test_chat_stream_with_session(self):
        """测试chat_stream方法使用session（覆盖line 423）"""
        # 创建带有session的网关
        conf = PrivateModelGateway.Conf(host="http://test.com", platform=Platform.minimax)
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount', return_value=1):
            with patch('commons.llm_gateway.models.private_model_gateway.requests.Session') as mock_session_class:
                mock_session = Mock()
                mock_session_class.return_value = mock_session
                
                gateway = PrivateModelGateway(conf, pool_max=5)  # 启用连接池会创建session
                
                messages = [Message(role="USER", content="Test with session")]
                params = PrivateChatParams(model="test-model", stream=True, messages=messages)
                
                # 模拟session.post的响应
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.iter_lines.return_value = [
                    b'data: {"errno": 0, "errmsg": "success", "sensitive_code": 0, "data": {"created": 1234567890, "choices": [{"index": 0, "finish_reason": "stop", "logprobes": 1, "delta": "Session response", "text": "Session response"}], "usage": {"prompt_tokens": 5, "completion_tokens": 10, "total_tokens": 15}, "base_resp": {"status_code": 0, "status_msg": "success"}}}'
                ]
                mock_session.post.return_value = mock_response
                
                results = list(gateway.chat_stream(params))
                
                assert len(results) >= 1
                if isinstance(results[0], tuple):
                    response, _ = results[0]
                else:
                    response = results[0]
                assert response.code == PrivateCode.OK
                mock_session.post.assert_called_once()  # 验证使用了session

    def test_chat_stream_with_invalid_lines_and_tokens(self):
        """测试chat_stream方法处理无效行和token记录（覆盖lines 436, 443-446, 451-454）"""
        # 创建启用token记录的网关
        mock_callback = Mock()
        conf = PrivateModelGateway.Conf(
            host="http://test.com",
            platform=Platform.minimax,
            prom_token=True,
            record_token=True,
            record_token_callback=mock_callback
        )
        
        with patch('commons.llm_gateway.models.private_model_gateway.get_mcount') as mock_get_mcount:
            mock_mcount = Mock()
            mock_get_mcount.return_value = mock_mcount
            
            gateway = PrivateModelGateway(conf, pool_max=-1)
            
            messages = [Message(role="USER", content="Test tokens")]
            params = PrivateChatParams(model="test-model", stream=True, messages=messages)
            
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.iter_lines.return_value = [
                b'invalid line',  # 无效行，应该被跳过 (line 436)
                b'data: {"errno": 0, "errmsg": "success", "sensitive_code": 0, "data": {"created": 1234567890, "choices": [{"index": 0, "finish_reason": "stop", "logprobes": 1, "delta": "Token response", "text": "Token response"}], "usage": {"prompt_tokens": 5, "completion_tokens": 10, "total_tokens": 15}, "base_resp": {"status_code": 0, "status_msg": "success"}}}'
            ]
            
            with patch('commons.llm_gateway.models.private_model_gateway.requests.post', return_value=mock_response), \
                 patch('commons.llm_gateway.models.private_model_gateway.logging') as mock_logging:
                
                results = list(gateway.chat_stream(params))
                
                assert len(results) >= 1
                # 验证prom_token记录 (lines 443-446)
                mock_logging.info.assert_called()
                mock_mcount.record_token.assert_called_once()
                # 验证record_token回调 (line 451)
                mock_callback.assert_called_once_with("private_token_", 15)

    def test_chat_stream_error_response(self):
        """测试chat_stream方法处理错误响应（覆盖lines 453-454）"""
        messages = [Message(role="USER", content="Test error")]
        params = PrivateChatParams(model="test-model", stream=True, messages=messages)
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"errno": 1, "errmsg": "error", "sensitive_code": 0, "data": {"created": 1234567890, "choices": [{"index": 0, "finish_reason": "error", "logprobes": 1, "delta": "Error response", "text": "Error response"}], "usage": {"prompt_tokens": 5, "completion_tokens": 10, "total_tokens": 15}, "base_resp": {"status_code": 1, "status_msg": "error"}}}'
        ]
        
        with patch('commons.llm_gateway.models.private_model_gateway.requests.post', return_value=mock_response), \
             patch('commons.llm_gateway.models.private_model_gateway.logging') as mock_logging:
            
            results = list(self.gateway.chat_stream(params))
            
            assert len(results) >= 1
            # 验证错误日志被调用 (line 454)
            mock_logging.error.assert_called()
            error_call = mock_logging.error.call_args[0][0]
            assert "private ai-gateway fail" in error_call



    @pytest.mark.asyncio
    async def test_async_chat_stream_exception(self):
        """测试async_chat_stream异常处理（覆盖lines 505-507）"""
        messages = [Message(role="USER", content="Test async stream exception")]
        params = PrivateChatParams(model="test-model", stream=True, messages=messages)
        
        with patch('commons.llm_gateway.models.private_model_gateway.aiohttp.ClientSession', side_effect=Exception("Async stream error")), \
             patch('commons.llm_gateway.models.private_model_gateway.logging') as mock_logging:
            
            results = []
            async for result in self.gateway.async_chat_stream(params):
                results.append(result)
                break
            
            assert len(results) == 1
            assert results[0].code == PrivateCode.FAILED
            assert results[0].message == "Async stream error"
            # 验证异常日志 (line 506)
            mock_logging.error.assert_called()
