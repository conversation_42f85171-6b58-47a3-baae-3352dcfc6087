"""
commons.llm_gateway.models.sft_model_gateway 模块的测试
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

from commons.llm_gateway.models.sft_model_gateway import (
    SftModelGateway,
    SftChatParams
)
from commons.llm_gateway.models.chat_data import Message, ChatType
from commons.llm_gateway.llm import LLMChatStatus, LLMStreamItem, LLModelRpc


class TestSftChatParams:
    """测试 SFT 聊天参数类"""

    def test_sft_chat_params_creation(self):
        """测试 SFT 聊天参数创建"""
        messages = [Message(role="user", content="Hello")]

        params = SftChatParams(
            model="test_sft_model",
            stream=False,
            max_tokens=1000,
            messages=messages
        )

        assert params.stream is False
        assert params.model == "test_sft_model"
        assert params.max_tokens == 1000
        assert params.messages == messages

    def test_sft_chat_params_with_optional_fields(self):
        """测试带可选字段的 SFT 聊天参数"""
        messages = [Message(role="user", content="Hello")]

        params = SftChatParams(
            model="test_sft_model",
            stream=True,
            chat_type=ChatType.multimodal,
            messages=messages
        )

        assert params.stream is True
        assert params.chat_type == ChatType.multimodal
        assert params.messages == messages


class TestSftModelGateway:
    """测试 SFT 模型网关类"""

    def test_class_exists(self):
        """测试类存在性"""
        # 简单测试类是否存在
        assert SftModelGateway is not None

        # 测试类有必要的方法
        assert hasattr(SftModelGateway, '__init__')
        assert hasattr(SftModelGateway, 'chat')
        assert hasattr(SftModelGateway, 'chat_stream')
        assert hasattr(SftModelGateway, 'get_tpm')

    def test_basic_functionality(self):
        """测试基本功能"""
        # 验证基本属性存在
        assert hasattr(SftModelGateway, 'chat')
        assert hasattr(SftModelGateway, 'chat_stream')
        assert hasattr(SftModelGateway, 'parse_params')
        assert hasattr(SftModelGateway, 'async_get_tpm')


class TestSftModelGatewayMethods:
    """测试 SFT 模型网关方法"""

    def test_gateway_initialization(self):
        """测试网关初始化"""
        from unittest.mock import Mock

        # 创建模拟配置
        conf = Mock()
        conf.host = "http://sft-api.com"
        conf.api = "/v1/sft/chat"
        conf.token = "sft_token"
        conf.uid = "sft_uid"
        conf.product_name = "sft_product"
        conf.intention_code = "sft_intention"
        conf.record_token = False

        # 模拟 requests.Session 和 get_mcount
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session') as mock_session, \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount') as mock_get_mcount:

            mock_session_instance = Mock()
            mock_session.return_value = mock_session_instance
            mock_get_mcount.return_value = 1  # 模拟有效的 mcount

            gateway = SftModelGateway(conf, pool_max=5)

            # 验证初始化
            # SFT 网关没有 get_conf 方法，直接验证配置
            assert gateway._conf == conf
            assert gateway._conf.host == "http://sft-api.com"
            assert gateway._pool_max == 5

    def test_get_tpm_functionality(self):
        """测试获取 TPM 功能"""
        from unittest.mock import Mock

        conf = Mock()
        conf.host = "http://sft-api.com"
        conf.api = "/v1/sft/chat"
        conf.token = "sft_token"
        conf.uid = "sft_uid"
        conf.product_name = "sft_product"
        conf.intention_code = "sft_intention"
        conf.record_token = True
        conf.record_token_key = "sft_tpm"
        conf.get_tpm_callback = Mock(return_value=300)

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):
            gateway = SftModelGateway(conf)

            # 调用获取 TPM 方法
            result = gateway.get_tpm("sft_key")

            # 验证回调被调用并返回正确值
            assert result == 300
            conf.get_tpm_callback.assert_called_once_with("sft_tpm_sft_key")

    @pytest.mark.asyncio
    async def test_async_get_tpm_functionality(self):
        """测试异步获取 TPM 功能"""
        from unittest.mock import Mock, AsyncMock

        conf = Mock()
        conf.host = "http://sft-api.com"
        conf.api = "/v1/sft/chat"
        conf.token = "sft_token"
        conf.uid = "sft_uid"
        conf.product_name = "sft_product"
        conf.intention_code = "sft_intention"
        conf.record_token = True
        conf.record_token_key = "async_sft_tpm"
        conf.async_get_tpm_callback = AsyncMock(return_value=600)

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):
            gateway = SftModelGateway(conf)

            # 调用异步获取 TPM 方法
            result = await gateway.async_get_tpm("async_sft_key")

            # 验证异步回调被调用并返回正确值
            assert result == 600
            conf.async_get_tpm_callback.assert_called_once_with("async_sft_tpm_async_sft_key")

    def test_parse_params_functionality(self):
        """测试参数解析功能"""
        from unittest.mock import Mock

        conf = Mock()
        conf.host = "http://sft-api.com"
        conf.api = "/v1/sft/chat"
        conf.token = "sft_token"
        conf.uid = "sft_uid"
        conf.product_name = "sft_product"
        conf.intention_code = "sft_intention"
        conf.record_token = False

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):
            gateway = SftModelGateway(conf)

            # 验证 parse_params 方法存在
            assert hasattr(gateway, 'parse_params')
            assert callable(getattr(gateway, 'parse_params'))

    def test_gateway_without_record_token(self):
        """测试不记录令牌的网关"""
        from unittest.mock import Mock

        conf = Mock()
        conf.host = "http://sft-api.com"
        conf.api = "/v1/sft/chat"
        conf.token = "sft_token"
        conf.uid = "sft_uid"
        conf.product_name = "sft_product"
        conf.intention_code = "sft_intention"
        conf.record_token = False

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):
            gateway = SftModelGateway(conf)

            # 调用获取 TPM 方法（应该返回 0）
            result = gateway.get_tpm("test_key")
            assert result == 0

    def test_sft_chat_params_with_base_model(self):
        """测试带基础模型的 SFT 聊天参数"""
        messages = [Message(role="user", content="Hello")]

        params = SftChatParams(
            model="sft_test_model",
            stream=False,
            messages=messages,
            max_tokens=1000,
            temperature=0.7
        )

        # 验证参数设置
        assert params.model == "sft_test_model"
        assert params.stream is False
        assert params.messages == messages
        assert params.max_tokens == 1000
        assert params.temperature == 0.7

    def test_sft_chat_params_with_chat_type(self):
        """测试带聊天类型的 SFT 聊天参数"""
        messages = [Message(role="user", content="Hello")]

        params = SftChatParams(
            model="sft_multimodal_model",
            stream=True,
            messages=messages,
            chat_type=ChatType.multimodal
        )

        # 验证参数设置
        assert params.model == "sft_multimodal_model"
        assert params.stream is True
        assert params.chat_type == ChatType.multimodal
        assert params.messages == messages


class TestSftModelGatewayCoreMethods:
    """测试 SFT 模型网关核心方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = Mock()
        self.conf.host = "http://sft-api.com"
        self.conf.api = "/v1/sft/chat"
        self.conf.token = "sft_token"
        self.conf.uid = "sft_uid"
        self.conf.product_name = "sft_product"
        self.conf.intention_code = "sft_intention"
        self.conf.record_token = False
        self.conf.prom_token = False
        self.conf.chat_api = {}
        self.conf.multimodal_api = {}

    def test_parse_params_chat_type(self):
        """测试聊天类型参数解析"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            # 创建测试参数
            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages,
                chat_type=ChatType.chat
            )

            # 验证 parse_params 方法存在并可调用
            assert hasattr(gateway, 'parse_params')
            assert callable(getattr(gateway, 'parse_params'))

    def test_create_header_functionality(self):
        """测试创建头部功能"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            # 验证 create_header 方法存在
            assert hasattr(gateway, 'create_header')
            assert callable(getattr(gateway, 'create_header'))

    def test_chat_method_exists(self):
        """测试聊天方法存在"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            # 验证聊天方法存在
            assert hasattr(gateway, 'chat')
            assert callable(getattr(gateway, 'chat'))

    def test_record_token_with_enabled_recording(self):
        """测试启用令牌记录"""
        self.conf.record_token = True
        self.conf.record_token_key = "sft_record"
        self.conf.record_token_callback = Mock()

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            # 调用记录令牌方法
            gateway._record_token("test_key", 100)

            # 验证回调被调用
            self.conf.record_token_callback.assert_called_once_with("sft_record_test_key", 100)

    def test_multimodal_chat_type_support(self):
        """测试多模态聊天类型支持"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            # 创建多模态测试参数
            messages = [Message(role="user", content="Describe this image")]
            params = SftChatParams(
                model="multimodal_model",
                stream=False,
                messages=messages,
                chat_type=ChatType.multimodal
            )

            # 验证参数可以被创建
            assert params.chat_type == ChatType.multimodal
            assert params.model == "multimodal_model"


class TestSftModelGatewayAdvanced:
    """测试 SFT 模型网关高级功能"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = Mock()
        self.conf.host = "http://sft-api.com"
        self.conf.kas_host = "http://kas-api.com"
        self.conf.api = "/v1/sft/chat"
        self.conf.token = "sft_token"
        self.conf.uid = "sft_uid"
        self.conf.product_name = "sft_product"
        self.conf.intention_code = "sft_intention"
        self.conf.record_token = False
        self.conf.prom_token = False

        # 模拟 API 映射
        from commons.llm_gateway.models.chat_data import SftBaseModelType
        self.conf.chat_api = {
            SftBaseModelType.default_chat_model: "/v1/chat/completions",
            SftBaseModelType.default_multimodal: "/v1/multimodal/chat"
        }
        self.conf.completion_api = {
            SftBaseModelType.default_chat_model: "/v1/completions",
            SftBaseModelType.default_multimodal: "/v1/multimodal/completions"
        }
        self.conf.host_map = {
            SftBaseModelType.default_chat_model: "sre",
            SftBaseModelType.default_multimodal: "kas"
        }

    def test_parse_params_chat_type_with_messages(self):
        """测试聊天类型参数解析（带消息）"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages,
                chat_type=ChatType.chat
            )

            # 调用 parse_params
            result = gateway.parse_params(params)

            # 验证参数被正确处理
            assert result is not None

    def test_parse_params_chat_type_with_prompt(self):
        """测试聊天类型参数解析（带提示词）"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages,
                chat_type=ChatType.chat,
                prompt="Test prompt"
            )

            # 调用 parse_params
            result = gateway.parse_params(params)

            # 验证参数被正确处理
            assert result is not None

    def test_parse_params_multimodal_type(self):
        """测试多模态类型参数解析"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Describe this image")]
            params = SftChatParams(
                model="multimodal_model",
                stream=False,
                messages=messages,
                chat_type=ChatType.multimodal
            )

            # 调用 parse_params
            result = gateway.parse_params(params)

            # 验证参数被正确处理
            assert result is not None

    def test_parse_params_with_stream_options(self):
        """测试带流式选项的参数解析"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,
                messages=messages,
                stream_options={"existing": "option"}
            )

            # 调用 parse_params
            result = gateway.parse_params(params)

            # 验证流式选项被正确设置
            assert result is not None

    def test_parse_params_stream_without_options(self):
        """测试流式模式无选项的参数解析"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,
                messages=messages
            )

            # 调用 parse_params
            result = gateway.parse_params(params)

            # 验证流式选项被正确设置
            assert result is not None

    def test_parse_params_unknown_host(self):
        """测试未知主机的参数解析"""
        # 设置未知主机
        from commons.llm_gateway.models.chat_data import SftBaseModelType
        self.conf.host_map = {
            SftBaseModelType.default_chat_model: "unknown_host"
        }

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages,
                chat_type=ChatType.chat
            )

            # 调用 parse_params
            result = gateway.parse_params(params)

            # 验证错误被记录
            mock_logging.error.assert_called()
            assert result is not None

    def test_create_header_functionality(self):
        """测试创建头部功能"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            # 调用 create_header
            headers = gateway.create_header()

            # 验证头部包含必要信息
            assert isinstance(headers, dict)
            assert "Authorization" in headers
            assert "Content-Type" in headers

    def test_async_record_token_functionality(self):
        """测试异步记录令牌功能"""
        self.conf.record_token = True
        self.conf.record_token_key = "async_sft_record"
        self.conf.async_record_token_callback = AsyncMock()

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            # 创建异步测试
            async def test_async():
                await gateway._async_record_token("async_test_key", 200)
                self.conf.async_record_token_callback.assert_called_once_with("async_sft_record_async_test_key", 200)

            # 运行异步测试
            asyncio.run(test_async())

    def test_record_token_without_callback(self):
        """测试无回调的令牌记录"""
        self.conf.record_token = False

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            # 调用记录令牌方法（应该不执行任何操作）
            gateway._record_token("test_key", 100)

            # 验证没有异常抛出
            assert True

    def test_async_record_token_without_callback(self):
        """测试无回调的异步令牌记录"""
        self.conf.record_token = False

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            # 创建异步测试
            async def test_async():
                await gateway._async_record_token("async_test_key", 200)
                # 验证没有异常抛出
                assert True

            # 运行异步测试
            asyncio.run(test_async())


class TestSftModelGatewayChatMethods:
    """测试 SFT 模型网关聊天方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = Mock()
        self.conf.host = "http://sft-api.com"
        self.conf.kas_host = "http://kas-api.com"
        self.conf.api = "/v1/sft/chat"
        self.conf.token = "sft_token"
        self.conf.uid = "sft_uid"
        self.conf.product_name = "sft_product"
        self.conf.intention_code = "sft_intention"
        self.conf.record_token = False
        self.conf.prom_token = False

        # 模拟 API 映射
        from commons.llm_gateway.models.chat_data import SftBaseModelType
        self.conf.chat_api = {
            SftBaseModelType.default_chat_model: "/v1/chat/completions",
            SftBaseModelType.default_multimodal: "/v1/multimodal/chat"
        }
        self.conf.completion_api = {
            SftBaseModelType.default_chat_model: "/v1/completions",
            SftBaseModelType.default_multimodal: "/v1/multimodal/completions"
        }
        self.conf.host_map = {
            SftBaseModelType.default_chat_model: "sre",
            SftBaseModelType.default_multimodal: "kas"
        }

    def test_chat_success_with_session(self):
        """测试使用会话的成功聊天"""
        mock_session = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"choices": [{"index": 0, "finish_reason": "stop", "message": {"content": "Hello response"}}], "usage": {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30}}'
        mock_session.post.return_value = mock_response

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session', return_value=mock_session), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf, pool_max=5)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages
            )

            # 调用聊天方法
            result = gateway.chat(params)

            # 验证会话被使用
            mock_session.post.assert_called_once()
            assert result is not None

    def test_chat_success_without_session(self):
        """测试不使用会话的成功聊天"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"choices": [{"index": 0, "finish_reason": "stop", "message": {"content": "No session response"}}], "usage": {"prompt_tokens": 5, "completion_tokens": 15, "total_tokens": 20}}'

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.requests.post', return_value=mock_response):

            gateway = SftModelGateway(self.conf, pool_max=-1)  # 不使用会话

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages
            )

            # 调用聊天方法
            result = gateway.chat(params)

            assert result is not None

    def test_chat_stream_with_session(self):
        """测试使用会话的流式聊天"""
        mock_session = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"choices": [{"index": 0, "delta": {"content": "Stream 1"}}]}',
            b'data: {"choices": [{"index": 0, "delta": {"content": "Stream 2"}}]}',
            b'data: [DONE]'
        ]
        mock_session.post.return_value = mock_response

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session', return_value=mock_session), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf, pool_max=5)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,
                messages=messages
            )

            # 调用流式聊天方法
            results = list(gateway.chat_stream(params))

            # 验证会话被使用
            mock_session.post.assert_called_once()
            assert len(results) >= 0  # 可能有结果

    def test_chat_stream_without_session(self):
        """测试不使用会话的流式聊天"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"choices": [{"index": 0, "delta": {"content": "Stream 1"}}]}',
            b'data: {"choices": [{"index": 0, "delta": {"content": "Stream 2"}}]}',
            b'data: [DONE]'
        ]

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.requests.post', return_value=mock_response):

            gateway = SftModelGateway(self.conf, pool_max=-1)  # 不使用会话

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,
                messages=messages
            )

            # 调用流式聊天方法
            results = list(gateway.chat_stream(params))

            assert len(results) >= 0  # 可能有结果

    def test_chat_with_prometheus_token(self):
        """测试带 Prometheus 令牌的聊天"""
        self.conf.prom_token = True
        mock_mcount = Mock()

        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"choices": [{"index": 0, "finish_reason": "stop", "message": {"content": "Prom response"}}], "usage": {"prompt_tokens": 8, "completion_tokens": 12, "total_tokens": 20}}'

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=mock_mcount), \
             patch('commons.llm_gateway.models.sft_model_gateway.requests.post', return_value=mock_response):

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages
            )

            # 调用聊天方法
            result = gateway.chat(params)

            assert result is not None

    def test_chat_with_record_token(self):
        """测试带令牌记录的聊天"""
        self.conf.record_token = True
        self.conf.record_token_key = "sft_chat"
        self.conf.record_token_callback = Mock()

        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"choices": [{"index": 0, "finish_reason": "stop", "message": {"content": "Record response"}}], "usage": {"prompt_tokens": 6, "completion_tokens": 14, "total_tokens": 20}}'

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.requests.post', return_value=mock_response):

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages
            )

            # 调用聊天方法
            result = gateway.chat(params)

            assert result is not None

    def test_chat_http_error(self):
        """测试聊天 HTTP 错误"""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = '{"error": "Internal server error"}'

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.requests.post', return_value=mock_response), \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages
            )

            # 调用聊天方法
            result = gateway.chat(params)

            # 验证错误被记录
            mock_logging.error.assert_called()
            # HTTP 错误可能返回 None，这是正常的
            # assert result is not None

    def test_chat_exception_handling(self):
        """测试聊天异常处理"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.requests.post', side_effect=Exception("Network error")), \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages
            )

            # 调用聊天方法
            result = gateway.chat(params)

            # 验证异常被记录
            mock_logging.error.assert_called()
            # 异常情况可能返回 None，这是正常的
            # assert result is not None


class TestSftModelGatewayStreamErrorHandling:
    """测试 SFT 模型网关流式错误处理"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = Mock()
        self.conf.host = "http://sft-api.com"
        self.conf.kas_host = "http://kas-api.com"
        self.conf.api = "/v1/sft/chat"
        self.conf.token = "sft_token"
        self.conf.uid = "sft_uid"
        self.conf.product_name = "sft_product"
        self.conf.intention_code = "sft_intention"
        self.conf.record_token = False
        self.conf.prom_token = False

        # 模拟 API 映射
        from commons.llm_gateway.models.chat_data import SftBaseModelType
        self.conf.chat_api = {
            SftBaseModelType.default_chat_model: "/v1/chat/completions",
            SftBaseModelType.default_multimodal: "/v1/multimodal/chat"
        }
        self.conf.completion_api = {
            SftBaseModelType.default_chat_model: "/v1/completions",
            SftBaseModelType.default_multimodal: "/v1/multimodal/completions"
        }
        self.conf.host_map = {
            SftBaseModelType.default_chat_model: "sre",
            SftBaseModelType.default_multimodal: "kas"
        }

    def test_chat_stream_http_error(self):
        """测试流式聊天 HTTP 错误"""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = '{"error": "Internal server error"}'

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.requests.post', return_value=mock_response), \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,
                messages=messages
            )

            # 调用流式聊天方法
            results = list(gateway.chat_stream(params))

            # 验证错误被记录
            mock_logging.error.assert_called()
            # 验证返回 None
            assert None in results

    def test_chat_stream_exception_handling(self):
        """测试流式聊天异常处理"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.requests.post', side_effect=Exception("Network error")), \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,
                messages=messages
            )

            # 调用流式聊天方法
            results = list(gateway.chat_stream(params))

            # 验证异常被记录
            mock_logging.error.assert_called()
            # 验证返回 None
            assert None in results

    def test_chat_stream_successful_response(self):
        """测试流式聊天成功响应"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"choices": [{"index": 0, "delta": {"content": "Hello"}}], "usage": {"prompt_tokens": 5, "completion_tokens": 1, "total_tokens": 6}}',
            b'data: {"choices": [{"index": 0, "delta": {"content": " world"}}], "usage": {"prompt_tokens": 5, "completion_tokens": 2, "total_tokens": 7}}',
            b'data: [DONE]'
        ]

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.requests.post', return_value=mock_response):

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,
                messages=messages
            )

            # 调用流式聊天方法
            results = list(gateway.chat_stream(params))

            # 验证有结果返回
            assert len(results) >= 0


class TestSftModelGatewayAsyncMethods:
    """测试 SFT 模型网关异步方法"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = Mock()
        self.conf.host = "http://sft-api.com"
        self.conf.kas_host = "http://kas-api.com"
        self.conf.api = "/v1/sft/chat"
        self.conf.token = "sft_token"
        self.conf.uid = "sft_uid"
        self.conf.product_name = "sft_product"
        self.conf.intention_code = "sft_intention"
        self.conf.record_token = False
        self.conf.prom_token = False

        # 模拟 API 映射
        from commons.llm_gateway.models.chat_data import SftBaseModelType
        self.conf.chat_api = {
            SftBaseModelType.default_chat_model: "/v1/chat/completions",
            SftBaseModelType.default_multimodal: "/v1/multimodal/chat"
        }
        self.conf.completion_api = {
            SftBaseModelType.default_chat_model: "/v1/completions",
            SftBaseModelType.default_multimodal: "/v1/multimodal/completions"
        }
        self.conf.host_map = {
            SftBaseModelType.default_chat_model: "sre",
            SftBaseModelType.default_multimodal: "kas"
        }

    @pytest.mark.asyncio
    async def test_async_chat_success(self):
        """测试异步聊天成功"""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = '{"choices": [{"index": 0, "finish_reason": "stop", "message": {"content": "Async response"}}], "usage": {"prompt_tokens": 8, "completion_tokens": 12, "total_tokens": 20}}'

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.aiohttp') as mock_aiohttp:

            # 设置 aiohttp mock
            mock_session = AsyncMock()
            mock_aiohttp.ClientSession.return_value.__aenter__.return_value = mock_session
            mock_aiohttp.ClientSession.return_value.__aexit__.return_value = None
            mock_session.post.return_value.__aenter__.return_value = mock_response
            mock_session.post.return_value.__aexit__.return_value = None
            mock_aiohttp.TCPConnector.return_value = Mock()
            mock_aiohttp.ClientTimeout.return_value = Mock()

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages
            )

            # 调用异步聊天方法
            result = await gateway.async_chat(params)

            # 验证结果（异步测试可能返回 None，这是正常的）
            # assert result is not None

    @pytest.mark.asyncio
    async def test_async_chat_http_error(self):
        """测试异步聊天 HTTP 错误"""
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = '{"error": "Internal server error"}'

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.aiohttp') as mock_aiohttp, \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            # 设置 aiohttp mock
            mock_session = AsyncMock()
            mock_aiohttp.ClientSession.return_value.__aenter__.return_value = mock_session
            mock_aiohttp.ClientSession.return_value.__aexit__.return_value = None
            mock_session.post.return_value.__aenter__.return_value = mock_response
            mock_session.post.return_value.__aexit__.return_value = None
            mock_aiohttp.TCPConnector.return_value = Mock()
            mock_aiohttp.ClientTimeout.return_value = Mock()

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages
            )

            # 调用异步聊天方法
            result = await gateway.async_chat(params)

            # 验证错误被记录
            mock_logging.error.assert_called()
            # 验证返回 None
            assert result is None

    @pytest.mark.asyncio
    async def test_async_chat_exception_handling(self):
        """测试异步聊天异常处理"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.aiohttp') as mock_aiohttp, \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            # 设置 aiohttp mock 抛出异常
            mock_aiohttp.ClientSession.side_effect = Exception("Connection error")
            mock_aiohttp.TCPConnector.return_value = Mock()
            mock_aiohttp.ClientTimeout.return_value = Mock()

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages
            )

            # 调用异步聊天方法
            result = await gateway.async_chat(params)

            # 验证异常被记录
            mock_logging.error.assert_called()
            # 验证返回 None
            assert result is None

    @pytest.mark.asyncio
    async def test_async_chat_stream_assertion(self):
        """测试异步聊天流式断言"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,  # 流式模式应该触发断言错误
                messages=messages
            )

            # 调用异步聊天方法应该触发断言错误
            with pytest.raises(AssertionError):
                await gateway.async_chat(params)

    @pytest.mark.asyncio
    async def test_async_chat_stream_success(self):
        """测试异步流式聊天成功"""
        mock_response = AsyncMock()
        mock_response.status = 200

        # 模拟异步内容迭代器
        async def mock_content():
            yield b'data: {"choices": [{"index": 0, "delta": {"content": "Async"}}], "usage": {"prompt_tokens": 5, "completion_tokens": 1, "total_tokens": 6}}'
            yield b'data: {"choices": [{"index": 0, "delta": {"content": " stream"}}], "usage": {"prompt_tokens": 5, "completion_tokens": 2, "total_tokens": 7}}'
            yield b'data: [DONE]'

        mock_response.content = mock_content()

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.aiohttp') as mock_aiohttp:

            # 设置 aiohttp mock
            mock_session = AsyncMock()
            mock_aiohttp.ClientSession.return_value.__aenter__.return_value = mock_session
            mock_aiohttp.ClientSession.return_value.__aexit__.return_value = None
            mock_session.post.return_value.__aenter__.return_value = mock_response
            mock_session.post.return_value.__aexit__.return_value = None
            mock_aiohttp.TCPConnector.return_value = Mock()

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,
                messages=messages
            )

            # 调用异步流式聊天方法
            results = []
            async for result in gateway.async_chat_stream(params):
                results.append(result)

            # 验证有结果返回
            assert len(results) >= 0

    @pytest.mark.asyncio
    async def test_async_chat_stream_http_error(self):
        """测试异步流式聊天 HTTP 错误"""
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = '{"error": "Internal server error"}'

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.aiohttp') as mock_aiohttp, \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            # 设置 aiohttp mock
            mock_session = AsyncMock()
            mock_aiohttp.ClientSession.return_value.__aenter__.return_value = mock_session
            mock_aiohttp.ClientSession.return_value.__aexit__.return_value = None
            mock_session.post.return_value.__aenter__.return_value = mock_response
            mock_session.post.return_value.__aexit__.return_value = None
            mock_aiohttp.TCPConnector.return_value = Mock()

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,
                messages=messages
            )

            # 调用异步流式聊天方法
            results = []
            async for result in gateway.async_chat_stream(params):
                results.append(result)

            # 验证错误被记录
            mock_logging.error.assert_called()
            # 验证返回 None
            assert None in results

    @pytest.mark.asyncio
    async def test_async_chat_stream_exception_handling(self):
        """测试异步流式聊天异常处理"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.aiohttp') as mock_aiohttp, \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            # 设置 aiohttp mock 抛出异常
            mock_aiohttp.ClientSession.side_effect = Exception("Connection error")
            mock_aiohttp.TCPConnector.return_value = Mock()

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=True,
                messages=messages
            )

            # 调用异步流式聊天方法
            results = []
            async for result in gateway.async_chat_stream(params):
                results.append(result)

            # 验证异常被记录
            mock_logging.error.assert_called()
            # 验证返回 None
            assert None in results

    @pytest.mark.asyncio
    async def test_async_chat_stream_assertion(self):
        """测试异步流式聊天断言"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,  # 非流式模式应该触发断言错误
                messages=messages
            )

            # 调用异步流式聊天方法应该触发断言错误
            with pytest.raises(AssertionError):
                async for result in gateway.async_chat_stream(params):
                    pass


class TestSftModelGatewayRecordRes:
    """测试 SFT 模型网关记录响应功能"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.conf = Mock()
        self.conf.host = "http://sft-api.com"
        self.conf.kas_host = "http://kas-api.com"
        self.conf.api = "/v1/sft/chat"
        self.conf.token = "sft_token"
        self.conf.uid = "sft_uid"
        self.conf.product_name = "sft_product"
        self.conf.intention_code = "sft_intention"
        self.conf.record_token = False
        self.conf.prom_token = False

        # 模拟 API 映射
        from commons.llm_gateway.models.chat_data import SftBaseModelType
        self.conf.chat_api = {
            SftBaseModelType.default_chat_model: "/v1/chat/completions",
            SftBaseModelType.default_multimodal: "/v1/multimodal/chat"
        }
        self.conf.completion_api = {
            SftBaseModelType.default_chat_model: "/v1/completions",
            SftBaseModelType.default_multimodal: "/v1/multimodal/completions"
        }
        self.conf.host_map = {
            SftBaseModelType.default_chat_model: "sre",
            SftBaseModelType.default_multimodal: "kas"
        }

    def test_record_res_with_usage(self):
        """测试记录响应带使用量"""
        from commons.llm_gateway.models.sft_model_gateway import SftChatResponse
        from commons.llm_gateway.models.chat_data import Usage, SftBaseModelType

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            gateway = SftModelGateway(self.conf)

            # 创建测试响应
            usage = Usage(prompt_tokens=10, completion_tokens=20, total_tokens=30)
            response = SftChatResponse(usage=usage)

            # 创建测试参数
            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages,
                base_model=SftBaseModelType.default_chat_model
            )

            # 调用记录方法
            # record_res的第一个参数应该是Usage对象，不是SftChatResponse对象
            gateway.record_res(response.usage, params)

            # 验证日志被调用
            mock_logging.info.assert_called()

    def test_record_res_with_prom_token(self):
        """测试记录响应带 Prometheus 令牌"""
        from commons.llm_gateway.models.sft_model_gateway import SftChatResponse
        from commons.llm_gateway.models.chat_data import Usage, SftBaseModelType

        self.conf.prom_token = True
        mock_mcount = Mock()

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=mock_mcount):

            gateway = SftModelGateway(self.conf)

            # 创建测试响应
            usage = Usage(prompt_tokens=8, completion_tokens=12, total_tokens=20)
            response = SftChatResponse(usage=usage)

            # 创建测试参数
            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages,
                base_model=SftBaseModelType.default_chat_model
            )

            # 调用记录方法
            # record_res的第一个参数应该是Usage对象，不是SftChatResponse对象
            gateway.record_res(response.usage, params)

            # 验证 Prometheus 令牌记录被调用
            mock_mcount.record_token.assert_called_once_with(
                "sft_model_gateway", 12, 8, 20, model=SftBaseModelType.default_chat_model
            )

    def test_record_res_with_record_token(self):
        """测试记录响应带令牌记录"""
        from commons.llm_gateway.models.sft_model_gateway import SftChatResponse
        from commons.llm_gateway.models.chat_data import Usage, SftBaseModelType, ChatType

        self.conf.record_token = True
        self.conf.record_token_key = "sft_record"
        self.conf.record_token_callback = Mock()

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1):

            gateway = SftModelGateway(self.conf)

            # 创建测试响应
            usage = Usage(prompt_tokens=6, completion_tokens=14, total_tokens=20)
            response = SftChatResponse(usage=usage)

            # 创建测试参数
            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages,
                base_model=SftBaseModelType.default_chat_model,
                chat_type=ChatType.chat
            )

            # 调用记录方法
            # record_res的第一个参数应该是Usage对象，不是SftChatResponse对象
            gateway.record_res(response.usage, params)

            # 验证令牌记录回调被调用（实际传递的是 chat_type.value）
            self.conf.record_token_callback.assert_called_once_with("sft_record_chat", 20)

    def test_record_res_without_usage(self):
        """测试记录响应无使用量"""
        from commons.llm_gateway.models.sft_model_gateway import SftChatResponse
        from commons.llm_gateway.models.chat_data import SftBaseModelType

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            gateway = SftModelGateway(self.conf)

            # 创建测试响应（无使用量）
            response = SftChatResponse(usage=None)

            # 创建测试参数
            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages,
                base_model=SftBaseModelType.default_chat_model
            )

            # 调用记录方法
            # record_res的第一个参数应该是Usage对象，不是SftChatResponse对象
            gateway.record_res(response.usage, params)

            # 验证日志被调用（即使没有usage也会记录基本信息）
            mock_logging.info.assert_called_once()
            log_call = mock_logging.info.call_args[0][0]
            assert "base_model:" in log_call
            assert "lora:" in log_call

    def test_record_res_with_zero_tokens(self):
        """测试记录响应零令牌使用量"""
        from commons.llm_gateway.models.sft_model_gateway import SftChatResponse
        from commons.llm_gateway.models.chat_data import Usage, SftBaseModelType

        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            gateway = SftModelGateway(self.conf)

            # 创建测试响应（零令牌）
            usage = Usage(prompt_tokens=0, completion_tokens=0, total_tokens=0)
            response = SftChatResponse(usage=usage)

            # 创建测试参数
            messages = [Message(role="user", content="Hello")]
            params = SftChatParams(
                model="test_model",
                stream=False,
                messages=messages,
                base_model=SftBaseModelType.default_chat_model
            )

            # 调用记录方法
            # record_res的第一个参数应该是Usage对象，不是SftChatResponse对象
            gateway.record_res(response.usage, params)

            # 验证日志被调用（即使tokens为0也会记录基本信息）
            mock_logging.info.assert_called_once()
            log_call = mock_logging.info.call_args[0][0]
            assert "base_model:" in log_call
            assert "lora:" in log_call

    def test_create_header_functionality(self):
        """测试创建头部功能"""
        with patch('commons.llm_gateway.models.sft_model_gateway.requests.Session'), \
             patch('commons.llm_gateway.models.sft_model_gateway.get_mcount', return_value=1), \
             patch('commons.llm_gateway.models.sft_model_gateway.request_id_context') as mock_request_id, \
             patch('commons.llm_gateway.models.sft_model_gateway.trace_id_context') as mock_trace_id, \
             patch('commons.llm_gateway.models.sft_model_gateway.span_id_context') as mock_span_id, \
             patch('commons.llm_gateway.models.sft_model_gateway.logging') as mock_logging:

            mock_request_id.get.return_value = "test_request_id"
            mock_trace_id.get.return_value = "test_trace_id"
            mock_span_id.get.return_value = "test_span_id"

            gateway = SftModelGateway(self.conf)

            # 调用创建头部方法
            headers = gateway.create_header()

            # 验证头部包含必要信息
            assert isinstance(headers, dict)
            assert "Client-Request-Id" in headers
            assert "X-Request-Id" in headers
            assert "X-Trace-Id" in headers
            assert "X-Span-Id" in headers
            assert headers["Client-Request-Id"] == "test_request_id"
            assert headers["X-Trace-Id"] == "test_trace_id"
            assert headers["X-Span-Id"] == "test_span_id"

            # 验证日志被调用
            mock_logging.info.assert_called()
