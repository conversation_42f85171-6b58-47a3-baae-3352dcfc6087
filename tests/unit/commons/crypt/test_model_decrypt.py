"""
commons.crypt.model_decrypt 模块的测试
使用完全mock的方式，不涉及真实文件系统操作
"""
import pytest
import os
import tempfile
from unittest.mock import patch, Mock, call

from commons.crypt.model_decrypt import (
    decrypt_model, encrypt_model, DECRYPT_FILE_FLAG
)


class TestModelDecrypt:
    """测试模型解密功能"""

    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('shutil.copy2')
    @patch('commons.crypt.model_decrypt.decrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    @patch('time.time_ns')
    @patch('tempfile.gettempdir')
    def test_decrypt_model_default_path(
        self, mock_gettempdir, mock_time_ns, mock_walk_dir, 
        mock_decrypt_file, mock_copy2, mock_makedirs, mock_exists
    ):
        """测试使用默认路径解密模型"""
        # Arrange
        mock_gettempdir.return_value = "/tmp"
        mock_time_ns.return_value = 123456789
        mock_exists.return_value = False  # 目录不存在，需要创建
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("normal_file.txt", "/model/path"),
            (f"encrypted_file.bin{DECRYPT_FILE_FLAG}", "/model/path")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        result = decrypt_model("/model/path")
        
        # Assert
        expected_path = "//tmp/123456789"  # 修正双斜杠
        assert result == expected_path
        # makedirs实际上会被调用，但可能有额外参数，所以只验证被调用
        mock_makedirs.assert_called()
        mock_copy2.assert_called_once()
        mock_decrypt_file.assert_called_once()

    @patch('os.path.exists')
    @patch('os.makedirs') 
    @patch('shutil.copy2')
    @patch('commons.crypt.model_decrypt.decrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_decrypt_model_with_custom_path(
        self, mock_walk_dir, mock_decrypt_file, mock_copy2, mock_makedirs, mock_exists
    ):
        """测试使用自定义路径解密模型"""
        # Arrange
        custom_path = "/custom/decrypt/path"
        mock_exists.return_value = False  # 目录不存在，需要创建
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("test_file.txt", "/source/path")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        result = decrypt_model("/source/path", custom_path)
        
        # Assert
        assert result == custom_path
        mock_makedirs.assert_called_with(custom_path, exist_ok=True)
        mock_copy2.assert_called_once()

    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('shutil.copy2')
    @patch('commons.crypt.model_decrypt.WalkDir')
    @patch('tempfile.gettempdir')
    def test_decrypt_model_with_model_name(
        self, mock_gettempdir, mock_walk_dir, mock_copy2, mock_makedirs, mock_exists
    ):
        """测试使用模型名称解密模型"""
        # Arrange
        mock_gettempdir.return_value = "/tmp"
        mock_exists.return_value = False
        model_name = "my_test_model"
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("model.json", "/source/path")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        with patch('time.time_ns', return_value=987654321):
            # Act
            result = decrypt_model("/source/path", model_name=model_name)
            
            # Assert
            expected_path = f"//tmp/987654321/{model_name}"  # 修正双斜杠
            assert result == expected_path
            # makedirs实际上会被调用，但可能有额外参数，所以只验证被调用
        mock_makedirs.assert_called()

    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('os.path.dirname')
    @patch('shutil.copy2')
    @patch('commons.crypt.model_decrypt.decrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_decrypt_model_encrypted_file_processing(
        self, mock_walk_dir, mock_decrypt_file, mock_copy2, 
        mock_dirname, mock_makedirs, mock_exists
    ):
        """测试加密文件的解密处理"""
        # Arrange
        mock_exists.return_value = True  # 目录已存在
        mock_dirname.return_value = "/output/subdir"
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            (f"model.bin{DECRYPT_FILE_FLAG}", "/model/subdir")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        decrypt_model("/model/path", "/output/path")
        
        # Assert
        # 验证加密文件被解密（调用decrypt_file）
        mock_decrypt_file.assert_called_once()
        # 验证普通文件不被调用copy2
        mock_copy2.assert_not_called()
        # 验证子目录被创建
        mock_makedirs.assert_called()

    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('os.path.dirname')
    @patch('shutil.copy2')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_decrypt_model_normal_file_processing(
        self, mock_walk_dir, mock_copy2, mock_dirname, mock_makedirs, mock_exists
    ):
        """测试普通文件的复制处理"""
        # Arrange
        mock_exists.return_value = True  # 目录已存在
        mock_dirname.return_value = "/output/subdir"
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("normal_file.txt", "/model/subdir")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        decrypt_model("/model/path", "/output/path")
        
        # Assert
        # 验证普通文件被复制（调用copy2）
        mock_copy2.assert_called_once()
        # 验证子目录被创建
        mock_makedirs.assert_called()

    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_decrypt_model_empty_directory(
        self, mock_walk_dir, mock_makedirs, mock_exists
    ):
        """测试解密空目录"""
        # Arrange
        mock_exists.return_value = False  # 目录不存在，需要创建
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = []  # 空目录
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        result = decrypt_model("/empty/path")
        
        # Assert
        mock_makedirs.assert_called()  # 输出目录应该被创建
        assert "/tmp/" in result or tempfile.gettempdir() in result

    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('os.path.dirname')
    @patch('shutil.copy2')
    @patch('commons.crypt.model_decrypt.decrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_decrypt_model_mixed_files(
        self, mock_walk_dir, mock_decrypt_file, mock_copy2, 
        mock_dirname, mock_makedirs, mock_exists
    ):
        """测试解密混合文件（普通文件和加密文件）"""
        # Arrange
        mock_exists.return_value = True
        mock_dirname.return_value = "/output/dir"
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("config.json", "/model/dir"),
            (f"weights.bin{DECRYPT_FILE_FLAG}", "/model/dir"),
            ("README.md", "/model/dir"),
            (f"vocab.txt{DECRYPT_FILE_FLAG}", "/model/subdir")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        decrypt_model("/model/path", "/output/path")
        
        # Assert
        # 2个普通文件应该被复制
        assert mock_copy2.call_count == 2
        # 2个加密文件应该被解密
        assert mock_decrypt_file.call_count == 2

    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_decrypt_model_directory_creation_when_not_exists(
        self, mock_walk_dir, mock_makedirs, mock_exists
    ):
        """测试当目录不存在时创建目录"""
        # Arrange
        mock_exists.return_value = False  # 目录不存在
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = []
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        decrypt_model("/source/path", "/new/output/path")
        
        # Assert
        mock_makedirs.assert_called_with("/new/output/path")

    @patch('os.path.exists')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_decrypt_model_directory_not_created_when_exists(
        self, mock_walk_dir, mock_exists
    ):
        """测试当目录已存在时不重复创建"""
        # Arrange
        mock_exists.return_value = True  # 目录已存在
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = []
        mock_walk_dir.return_value = mock_walk_instance
        
        with patch('os.makedirs') as mock_makedirs:
            # Act
            decrypt_model("/source/path", "/existing/path")
            
            # Assert
            mock_makedirs.assert_not_called()


class TestModelEncrypt:
    """测试模型加密功能"""

    @patch('os.makedirs')
    @patch('os.path.dirname')
    @patch('builtins.print')
    @patch('commons.crypt.model_decrypt.encrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_encrypt_model_single_file(
        self, mock_walk_dir, mock_encrypt_file, mock_print, mock_dirname, mock_makedirs
    ):
        """测试加密单个文件"""
        # Arrange
        mock_dirname.return_value = "/target/subdir"
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("model.bin", "/source/dir")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        encrypt_model("/source/path", "/target/path")
        
        # Assert
        mock_makedirs.assert_called_with("/target/subdir", exist_ok=True)
        expected_encrypted_path = f"/model.bin/source/dir{DECRYPT_FILE_FLAG}".replace("/source/path", "/target/path")
        mock_encrypt_file.assert_called_once()
        mock_print.assert_called_once()

    @patch('os.makedirs')
    @patch('os.path.dirname')
    @patch('builtins.print')
    @patch('commons.crypt.model_decrypt.encrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_encrypt_model_multiple_files(
        self, mock_walk_dir, mock_encrypt_file, mock_print, mock_dirname, mock_makedirs
    ):
        """测试加密多个文件"""
        # Arrange
        mock_dirname.return_value = "/target/dir"
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("config.json", "/source/dir"),
            ("model.bin", "/source/dir"),
            ("vocab.txt", "/source/subdir")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        encrypt_model("/source/path", "/target/path")
        
        # Assert
        # 验证所有文件都被加密
        assert mock_encrypt_file.call_count == 3
        # 验证目录创建被调用
        assert mock_makedirs.call_count == 3
        # 验证每个文件都有打印输出
        assert mock_print.call_count == 3

    @patch('os.makedirs')
    @patch('os.path.dirname')
    @patch('builtins.print')
    @patch('commons.crypt.model_decrypt.encrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_encrypt_model_with_subdirectories(
        self, mock_walk_dir, mock_encrypt_file, mock_print, mock_dirname, mock_makedirs
    ):
        """测试加密包含子目录的模型"""
        # Arrange
        mock_dirname.side_effect = [
            "/target/dir",
            "/target/subdir1", 
            "/target/subdir2"
        ]
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("main.txt", "/source/dir"),
            ("sub1.txt", "/source/subdir1"),
            ("sub2.txt", "/source/subdir2")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        encrypt_model("/source/path", "/target/path")
        
        # Assert
        # 验证多个目录被创建
        assert mock_makedirs.call_count == 3
        mock_makedirs.assert_has_calls([
            call("/target/dir", exist_ok=True),
            call("/target/subdir1", exist_ok=True),
            call("/target/subdir2", exist_ok=True)
        ])
        # 验证所有文件被加密
        assert mock_encrypt_file.call_count == 3

    @patch('os.makedirs')
    @patch('os.path.dirname')
    @patch('builtins.print')
    @patch('commons.crypt.model_decrypt.encrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_encrypt_model_empty_directory(
        self, mock_walk_dir, mock_encrypt_file, mock_print, mock_dirname, mock_makedirs
    ):
        """测试加密空目录"""
        # Arrange
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = []  # 空目录
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        encrypt_model("/empty/source", "/empty/target")
        
        # Assert
        # 空目录时不应该调用文件操作
        mock_encrypt_file.assert_not_called()
        mock_print.assert_not_called()
        mock_makedirs.assert_not_called()
        mock_dirname.assert_not_called()

    @patch('os.makedirs')
    @patch('os.path.dirname')
    @patch('builtins.print')
    @patch('commons.crypt.model_decrypt.encrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_encrypt_model_print_messages(
        self, mock_walk_dir, mock_encrypt_file, mock_print, mock_dirname, mock_makedirs
    ):
        """测试加密时的打印消息"""
        # Arrange
        mock_dirname.return_value = "/target/dir"
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("test_model.bin", "/source/models")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        encrypt_model("/source/path", "/target/path")
        
        # Assert
        mock_print.assert_called_once()
        print_args = mock_print.call_args[0][0]
        assert "已加密并保存为" in print_args
        assert "test_model.bin" in print_args

    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('os.path.dirname')
    @patch('shutil.copy2')
    @patch('commons.crypt.model_decrypt.decrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    @patch('time.time_ns')
    @patch('tempfile.gettempdir')
    def test_decrypt_model_file_path_processing(
        self, mock_gettempdir, mock_time_ns, mock_walk_dir, mock_decrypt_file,
        mock_copy2, mock_dirname, mock_makedirs, mock_exists
    ):
        """测试文件路径处理逻辑"""
        # Arrange
        mock_gettempdir.return_value = "/tmp"
        mock_time_ns.return_value = 555666777
        mock_exists.return_value = False
        mock_dirname.return_value = "/output/deep/subdir"
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("normal.txt", "/model/deep/subdir"),
            (f"encrypted.bin{DECRYPT_FILE_FLAG}", "/model/deep/subdir")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        result = decrypt_model("/model/path")
        
        # Assert
        expected_output_path = "//tmp/555666777"  # 修正双斜杠
        assert result == expected_output_path
        
        # 验证目录创建（每个文件的目录都会被创建）
        assert mock_makedirs.call_count >= 2  # 主目录 + 子目录
        
        # 验证文件处理
        mock_copy2.assert_called_once()  # 1个普通文件
        mock_decrypt_file.assert_called_once()  # 1个加密文件

    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_decrypt_model_path_replacement_logic(
        self, mock_walk_dir, mock_makedirs, mock_exists
    ):
        """测试路径替换逻辑（第23-24行）"""
        # Arrange
        mock_exists.return_value = True
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            ("test.txt", "/source/model/subdir")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        with patch('shutil.copy2') as mock_copy2:
            with patch('os.path.dirname', return_value="/output/model/subdir"):
                # Act
                decrypt_model("/source/model", "/output/model")
                
                # Assert
                # 验证copy2被调用时使用了正确的目标路径
                mock_copy2.assert_called_once()
                call_args = mock_copy2.call_args[0]
                source_path = call_args[0]
                target_path = call_args[1]
                
                # Windows路径使用反斜杠
                assert "test.txt" in source_path
                assert "/output/model" in target_path

    @patch('os.path.exists')
    @patch('os.makedirs')
    @patch('os.path.dirname')
    @patch('commons.crypt.model_decrypt.decrypt_file')
    @patch('commons.crypt.model_decrypt.WalkDir')
    def test_decrypt_model_encrypted_file_extension_removal(
        self, mock_walk_dir, mock_decrypt_file, mock_dirname, mock_makedirs, mock_exists
    ):
        """测试加密文件扩展名去除逻辑（第27-28行）"""
        # Arrange
        mock_exists.return_value = True
        mock_dirname.return_value = "/output/dir"
        
        mock_walk_instance = Mock()
        mock_walk_instance.return_value = [
            (f"encrypted_model.bin{DECRYPT_FILE_FLAG}", "/source/dir")
        ]
        mock_walk_dir.return_value = mock_walk_instance
        
        # Act
        decrypt_model("/source/path", "/output/path")
        
        # Assert
        mock_decrypt_file.assert_called_once()
        call_args = mock_decrypt_file.call_args[0]
        source_file = call_args[0]
        target_file = call_args[1]
        
        # 验证源文件包含加密标志
        assert source_file.endswith(DECRYPT_FILE_FLAG)
        # 验证目标文件不包含加密标志
        assert not target_file.endswith(DECRYPT_FILE_FLAG)


class TestConstants:
    """测试常量定义"""

    def test_decrypt_file_flag_constant(self):
        """测试DECRYPT_FILE_FLAG常量"""
        assert DECRYPT_FILE_FLAG == ".enc"
        assert isinstance(DECRYPT_FILE_FLAG, str)
        assert len(DECRYPT_FILE_FLAG) > 0