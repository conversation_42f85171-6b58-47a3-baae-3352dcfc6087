# -*- coding: utf-8 -*-
"""
Unit tests for commons.crypt.aes_crypt module
"""

import pytest
import os
import tempfile
from unittest.mock import patch, mock_open
from commons.crypt.aes_crypt import (
    encrypt_buffer, decrypt_buffer, pad, unpad,
    encrypt_file, decrypt_file, ENCRYPT_HEADER, ENCRYPT_RECORD,
    key, IV_LEN, UINT16_MAX, BLOCK_SIZE, HEADER_LEN, HEADER_STR
)


class TestAesCrypt:
    """测试 AES 加密解密功能"""

    def test_key_initialization(self):
        """测试密钥初始化"""
        assert isinstance(key, bytes)
        assert len(key) > 0

    def test_key_from_environment(self):
        """测试从环境变量获取密钥"""
        with patch.dict(os.environ, {'BUILD_KEY': 'test_key_123'}):
            # 重新导入模块以获取新的密钥
            import importlib
            import commons.crypt.aes_crypt
            importlib.reload(commons.crypt.aes_crypt)
            
            assert commons.crypt.aes_crypt.key == b'test_key_123'

    def test_pad_function(self):
        """测试填充函数"""
        # 测试不同长度的数据
        test_data = b"test data"
        padded = pad(test_data)
        
        assert len(padded) % 16 == 0  # AES block size
        assert padded.startswith(test_data)
        assert padded[-1] == len(padded) - len(test_data)

    def test_pad_function_empty_data(self):
        """测试空数据的填充"""
        test_data = b""
        padded = pad(test_data)
        
        assert len(padded) == 16  # 一个完整的 AES block
        assert padded[-1] == 16  # 填充大小

    def test_pad_function_exact_block_size(self):
        """测试恰好是块大小的数据"""
        test_data = b"x" * 16
        padded = pad(test_data)
        
        assert len(padded) == 32  # 需要额外的块
        assert padded.startswith(test_data)

    def test_unpad_function(self):
        """测试去填充函数"""
        original_data = b"test data"
        padded = pad(original_data)
        unpadded = unpad(padded)
        
        assert unpadded == original_data

    def test_unpad_function_empty_data(self):
        """测试空数据的去填充"""
        padded = pad(b"")
        unpadded = unpad(padded)
        
        assert unpadded == b""

    def test_encrypt_decrypt_buffer(self):
        """测试缓冲区加密解密"""
        test_data = b"Hello, World! This is a test message."
        valid_key = b"MODEL_BUILD_KEY1"  # 16 bytes for AES-128
        
        # 加密
        encrypted = encrypt_buffer(test_data, valid_key)
        
        # 验证加密结果
        assert len(encrypted) > len(test_data)
        assert encrypted[:IV_LEN] != test_data[:IV_LEN]  # IV 应该不同
        
        # 解密
        decrypted = decrypt_buffer(encrypted, valid_key)
        
        assert decrypted == test_data

    def test_encrypt_decrypt_buffer_with_custom_key(self):
        """测试使用自定义密钥的加密解密"""
        test_data = b"Custom key test"
        custom_key = b"custom_key_16byt"  # 16 bytes for AES-128
        
        # 加密
        encrypted = encrypt_buffer(test_data, custom_key)
        
        # 解密
        decrypted = decrypt_buffer(encrypted, custom_key)
        
        assert decrypted == test_data

    def test_encrypt_decrypt_buffer_empty_data(self):
        """测试空数据的加密解密"""
        test_data = b""
        valid_key = b"MODEL_BUILD_KEY1"  # 16 bytes for AES-128
        
        encrypted = encrypt_buffer(test_data, valid_key)
        decrypted = decrypt_buffer(encrypted, valid_key)
        
        assert decrypted == test_data

    def test_encrypt_file_basic(self):
        """测试文件加密的基本功能"""
        with tempfile.NamedTemporaryFile(mode='wb', delete=False) as src_file:
            src_file.write(b"Test file content")
            src_path = src_file.name
        
        dst_path = src_path + ".encrypted"
        
        try:
            # 使用mock来避免实际的加密操作
            with patch('commons.crypt.aes_crypt.encrypt_buffer') as mock_encrypt:
                mock_encrypt.return_value = b"mock_encrypted_data"
                
                # 加密文件
                encrypt_file(src_path, dst_path)
                
                # 验证加密文件存在
                assert os.path.exists(dst_path)
                
        finally:
            # 清理文件
            if os.path.exists(src_path):
                os.unlink(src_path)
            if os.path.exists(dst_path):
                os.unlink(dst_path)

    def test_encrypt_file_empty(self):
        """测试空文件加密"""
        with tempfile.NamedTemporaryFile(mode='wb', delete=False) as src_file:
            src_path = src_file.name
        
        dst_path = src_path + ".encrypted"
        
        try:
            # 使用mock来避免实际的加密操作
            with patch('commons.crypt.aes_crypt.encrypt_buffer') as mock_encrypt:
                mock_encrypt.return_value = b"mock_encrypted_data"
                
                # 加密空文件
                encrypt_file(src_path, dst_path)
                
                # 验证加密文件存在
                assert os.path.exists(dst_path)
                
        finally:
            # 清理文件
            if os.path.exists(src_path):
                os.unlink(src_path)
            if os.path.exists(dst_path):
                os.unlink(dst_path)

    def test_decrypt_file_basic(self):
        """测试文件解密的基本功能"""
        # 创建测试文件
        with tempfile.NamedTemporaryFile(mode='wb', delete=False) as src_file:
            src_file.write(b"Test file content")
            src_path = src_file.name
        
        encrypted_path = src_path + ".encrypted"
        decrypted_path = src_path + ".decrypted"
        
        try:
            # 使用mock来避免实际的加密解密操作
            with patch('commons.crypt.aes_crypt.encrypt_buffer') as mock_encrypt:
                with patch('commons.crypt.aes_crypt.decrypt_buffer') as mock_decrypt:
                    mock_encrypt.return_value = b"mock_encrypted_data"
                    mock_decrypt.return_value = b"Test file content"
                    
                    # 加密文件
                    encrypt_file(src_path, encrypted_path)
                    
                    # 解密文件
                    decrypt_file(encrypted_path, decrypted_path)
                    
                    # 验证解密结果
                    assert os.path.exists(decrypted_path)
                    
        finally:
            # 清理文件
            for path in [src_path, encrypted_path, decrypted_path]:
                if os.path.exists(path):
                    os.unlink(path)

    def test_encrypt_header_class(self):
        """测试 ENCRYPT_HEADER 类"""
        header = ENCRYPT_HEADER()
        header.init()
        
        assert header.batch == 0

    def test_encrypt_record_class(self):
        """测试 ENCRYPT_RECORD 类"""
        record = ENCRYPT_RECORD()
        record.init()
        
        assert record.block_size == 0

    def test_constants(self):
        """测试常量定义"""
        assert UINT16_MAX == 0xFFFF
        assert BLOCK_SIZE == 1024
        assert HEADER_LEN == 12
        assert HEADER_STR == b"kna_decrypt"
        assert IV_LEN == 16



    def test_encrypt_buffer_with_different_data_sizes(self):
        """测试不同大小数据的加密"""
        test_cases = [
            b"",  # 空数据
            b"a",  # 1字节
            b"ab",  # 2字节
            b"abc",  # 3字节
            b"abcd",  # 4字节
            b"x" * 15,  # 15字节
            b"x" * 16,  # 16字节
            b"x" * 17,  # 17字节
            b"x" * 1024,  # 1024字节
        ]
        
        valid_key = b"MODEL_BUILD_KEY1"  # 16 bytes for AES-128
        
        for test_data in test_cases:
            encrypted = encrypt_buffer(test_data, valid_key)
            decrypted = decrypt_buffer(encrypted, valid_key)
            
            assert decrypted == test_data
            assert len(encrypted) >= len(test_data) + IV_LEN

    def test_decrypt_buffer_invalid_data(self):
        """测试解密无效数据"""
        # 测试数据太短（小于IV长度）
        valid_key = b"MODEL_BUILD_KEY1"  # 16 bytes for AES-128
        with pytest.raises(ValueError):
            decrypt_buffer(b"short", valid_key)

    def test_pad_unpad_edge_cases(self):
        """测试填充和去填充的边界情况"""
        # 测试各种长度的数据
        for i in range(20):
            test_data = b"x" * i
            padded = pad(test_data)
            unpadded = unpad(padded)
            
            assert unpadded == test_data
            assert len(padded) % 16 == 0
