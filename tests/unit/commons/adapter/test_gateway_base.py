# Author: linqi
# Date: 2025/8/21
# Time: 10:44

import pytest
from unittest.mock import Mock, AsyncMock
from commons.adapter.gateway_base import MultiModelArgs, AsyncChatTextArgs, GatewayBase
from commons.llm_gateway.models.chat_data import Message


class TestMultiModelArgs:
    """测试MultiModelArgs类"""
    
    def test_multi_model_args_default_initialization(self):
        """测试MultiModelArgs默认初始化"""
        args = MultiModelArgs()
        
        assert args.messages is None
        assert args.stream is False
        assert args.temperature == 0.7
        assert args.max_token == 2000
        assert args.adapter == ""
        assert args.return_usage is False
        assert args.extra_dict == {}

    def test_multi_model_args_custom_initialization(self):
        """测试MultiModelArgs自定义初始化"""
        messages = [Message(role="user", content="test")]
        args = MultiModelArgs(
            messages=messages,
            stream=True,
            temperature=0.5,
            max_token=1000,
            adapter="test_adapter",
            return_usage=True,
            extra_param="extra_value"
        )
        
        assert args.messages == messages
        assert args.stream is True
        assert args.temperature == 0.5
        assert args.max_token == 1000
        assert args.adapter == "test_adapter"
        assert args.return_usage is True
        assert args.extra_dict == {"extra_param": "extra_value"}

    def test_multi_model_args_with_kwargs(self):
        """测试MultiModelArgs使用kwargs参数"""
        args = MultiModelArgs(
            messages=[],
            custom_param1="value1",
            custom_param2=123
        )
        
        assert args.extra_dict == {
            "custom_param1": "value1",
            "custom_param2": 123
        }


class TestAsyncChatTextArgs:
    """测试AsyncChatTextArgs类"""
    
    def test_async_chat_text_args_default_initialization(self):
        """测试AsyncChatTextArgs默认初始化"""
        args = AsyncChatTextArgs()
        
        assert args.prompt is None
        assert args.messages is None
        assert args.query == ""
        assert args.temperature == 0.7
        assert args.max_token == 2000
        assert args.repetition_penalty == 1.0
        assert args.adapter == ""
        assert args.return_usage is False
        assert args.extra_dict == {}

    def test_async_chat_text_args_custom_initialization(self):
        """测试AsyncChatTextArgs自定义初始化"""
        messages = [Message(role="user", content="test")]
        args = AsyncChatTextArgs(
            prompt="test prompt",
            messages=messages,
            query="test query",
            temperature=0.3,
            max_token=1500,
            repetition_penalty=1.2,
            adapter="test_adapter",
            return_usage=True,
            extra_param="extra_value"
        )
        
        assert args.prompt == "test prompt"
        assert args.messages == messages
        assert args.query == "test query"
        assert args.temperature == 0.3
        assert args.max_token == 1500
        assert args.repetition_penalty == 1.2
        assert args.adapter == "test_adapter"
        assert args.return_usage is True
        assert args.extra_dict == {"extra_param": "extra_value"}

    def test_async_chat_text_args_with_different_prompt_types(self):
        """测试AsyncChatTextArgs使用不同类型的prompt"""
        # 字符串类型
        args1 = AsyncChatTextArgs(prompt="string prompt")
        assert args1.prompt == "string prompt"
        
        # 列表类型
        args2 = AsyncChatTextArgs(prompt=["token1", "token2"])
        assert args2.prompt == ["token1", "token2"]
        
        # 嵌套列表类型
        args3 = AsyncChatTextArgs(prompt=[[1, 2], [3, 4]])
        assert args3.prompt == [[1, 2], [3, 4]]

    def test_async_chat_text_args_with_kwargs(self):
        """测试AsyncChatTextArgs使用kwargs参数"""
        args = AsyncChatTextArgs(
            prompt="test",
            custom_param1="value1",
            custom_param2=456
        )
        
        assert args.extra_dict == {
            "custom_param1": "value1",
            "custom_param2": 456
        }


class TestGatewayBase:
    """测试GatewayBase抽象基类"""
    
    def test_gateway_base_is_abstract(self):
        """测试GatewayBase是抽象类，不能直接实例化"""
        with pytest.raises(TypeError):
            GatewayBase()

    def test_gateway_base_has_abstract_methods(self):
        """测试GatewayBase有抽象方法"""
        # 检查类是否有抽象方法
        assert hasattr(GatewayBase, 'async_multimodal')
        assert hasattr(GatewayBase, 'async_chat_text')
        
        # 检查方法是否是抽象的
        assert GatewayBase.async_multimodal.__isabstractmethod__
        assert GatewayBase.async_chat_text.__isabstractmethod__

    def test_gateway_base_inheritance(self):
        """测试GatewayBase继承自ABC"""
        from abc import ABC
        assert issubclass(GatewayBase, ABC)


class TestConcreteGatewayAdapter:
    """测试具体的网关适配器实现"""
    
    class MockGatewayAdapter(GatewayBase):
        """用于测试的具体实现"""
        
        async def async_multimodal(self, model_args: MultiModelArgs):
            return "multimodal_result"
        
        async def async_chat_text(self, model_args: AsyncChatTextArgs):
            return "chat_text_result"
    
    @pytest.mark.asyncio
    async def test_concrete_adapter_async_multimodal(self):
        """测试具体适配器的async_multimodal方法"""
        adapter = self.MockGatewayAdapter()
        args = MultiModelArgs(messages=[])
        
        result = await adapter.async_multimodal(args)
        assert result == "multimodal_result"

    @pytest.mark.asyncio
    async def test_concrete_adapter_async_chat_text(self):
        """测试具体适配器的async_chat_text方法"""
        adapter = self.MockGatewayAdapter()
        args = AsyncChatTextArgs(prompt="test")
        
        result = await adapter.async_chat_text(args)
        assert result == "chat_text_result"

    def test_abstract_methods_are_abstract(self):
        """测试抽象方法确实是抽象的"""
        # 验证抽象方法存在且是抽象的
        assert hasattr(GatewayBase, 'async_multimodal')
        assert hasattr(GatewayBase, 'async_chat_text')
        assert GatewayBase.async_multimodal.__isabstractmethod__
        assert GatewayBase.async_chat_text.__isabstractmethod__

    def test_abstract_methods_cannot_be_called_directly(self):
        """测试抽象方法不能直接调用"""
        # 创建一个继承自GatewayBase的类，但不实现抽象方法
        class IncompleteAdapter(GatewayBase):
            pass
        
        # 应该不能实例化
        with pytest.raises(TypeError):
            IncompleteAdapter()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
