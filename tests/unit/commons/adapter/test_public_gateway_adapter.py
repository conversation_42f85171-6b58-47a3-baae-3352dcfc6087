# Author: linqi
# Date: 2025/8/21
# Time: 10:45

import pytest
from unittest.mock import Mock, AsyncMock, patch
from commons.adapter.public_gateway_adapter import PublicGatewayAdapter
from commons.adapter.gateway_base import MultiModelArgs, AsyncChatTextArgs
from commons.llm_gateway.models.chat_data import <PERSON>MConfig, PublicGatewayHeader, AIHubModel, Message
from commons.llm_gateway.llm import LLModelRpc


class TestPublicGatewayAdapter:
    """测试PublicGatewayAdapter类"""
    
    def test_public_gateway_adapter_initialization(self):
        """测试PublicGatewayAdapter初始化"""
        llm_config = Mock(spec=LLMConfig)
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        
        assert adapter.llm_config == llm_config
        assert adapter.public_gateway_header == public_gateway_header

    def test_choice_ai_hub_model_by_config_success(self):
        """测试_choice_ai_hub_model_by_config方法成功匹配"""
        # 创建模拟配置
        llm_config = Mock(spec=LLMConfig)
        llm_config.provider = "test_provider"
        llm_config.model = "test_model"
        llm_config.version = "1.0"
        
        # 创建匹配的AIHubModel
        matching_model = Mock(spec=AIHubModel)
        matching_model.provider = "test_provider"
        matching_model.name = "test_model"
        matching_model.version = "1.0"
        
        # 创建不匹配的AIHubModel
        non_matching_model = Mock(spec=AIHubModel)
        non_matching_model.provider = "other_provider"
        non_matching_model.name = "other_model"
        non_matching_model.version = "2.0"
        
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = [non_matching_model, matching_model]
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        
        # 验证匹配的模型被设置
        assert adapter.ai_hub_model == matching_model

    def test_choice_ai_hub_model_by_config_no_match(self):
        """测试_choice_ai_hub_model_by_config方法没有匹配"""
        # 创建模拟配置
        llm_config = Mock(spec=LLMConfig)
        llm_config.provider = "test_provider"
        llm_config.model = "test_model"
        llm_config.version = "1.0"
        
        # 创建不匹配的AIHubModel
        non_matching_model = Mock(spec=AIHubModel)
        non_matching_model.provider = "other_provider"
        non_matching_model.name = "other_model"
        non_matching_model.version = "2.0"
        
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = [non_matching_model]
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        
        # 验证没有匹配的模型
        assert not hasattr(adapter, 'ai_hub_model') or adapter.ai_hub_model is None

    def test_choice_ai_hub_model_by_config_empty_models(self):
        """测试_choice_ai_hub_model_by_config方法空模型列表"""
        llm_config = Mock(spec=LLMConfig)
        llm_config.provider = "test_provider"
        llm_config.model = "test_model"
        llm_config.version = "1.0"
        
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        
        # 验证没有模型
        assert not hasattr(adapter, 'ai_hub_model') or adapter.ai_hub_model is None

    def test_check_config_success(self):
        """测试_check_config方法成功情况"""
        llm_config = Mock(spec=LLMConfig)
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []
        ai_hub_model = Mock(spec=AIHubModel)
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        adapter.ai_hub_model = ai_hub_model
        
        # 应该不抛出异常
        adapter._check_config()

    def test_check_config_no_llm_config(self):
        """测试_check_config方法LLM配置为空时抛出异常"""
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []
        ai_hub_model = Mock(spec=AIHubModel)
        
        adapter = PublicGatewayAdapter(None, public_gateway_header)
        adapter.ai_hub_model = ai_hub_model
        
        with pytest.raises(Exception, match="模型配置未加载"):
            adapter._check_config()

    def test_check_config_no_ai_hub_model(self):
        """测试_check_config方法AIHub模型为空时抛出异常"""
        llm_config = Mock(spec=LLMConfig)
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        adapter.ai_hub_model = None
        
        with pytest.raises(Exception, match="AIHub模型未找到"):
            adapter._check_config()

    def test_check_config_no_public_gateway_header(self):
        """测试_check_config方法PublicGatewayHeader为空时创建新的"""
        llm_config = Mock(spec=LLMConfig)
        ai_hub_model = Mock(spec=AIHubModel)
        
        # 跳过初始化时的_choice_ai_hub_model_by_config调用
        with patch.object(PublicGatewayAdapter, '_choice_ai_hub_model_by_config'):
            adapter = PublicGatewayAdapter(llm_config, None)
            adapter.ai_hub_model = ai_hub_model
            
            # 应该创建新的PublicGatewayHeader并调用load_by_local_llm_config
            with patch.object(PublicGatewayHeader, 'load_by_local_llm_config') as mock_load:
                adapter._check_config()
                mock_load.assert_called_once_with(llm_config=llm_config)

    def test_check_config_load_by_local_llm_config(self):
        """测试_check_config方法调用load_by_local_llm_config"""
        llm_config = Mock(spec=LLMConfig)
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []
        ai_hub_model = Mock(spec=AIHubModel)
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        adapter.ai_hub_model = ai_hub_model
        
        # 验证load_by_local_llm_config被调用
        with patch.object(public_gateway_header, 'load_by_local_llm_config') as mock_load:
            adapter._check_config()
            mock_load.assert_called_once_with(llm_config=llm_config)

    @pytest.mark.asyncio
    async def test_async_multimodal_success(self):
        """测试async_multimodal方法成功调用"""
        # 创建模拟配置
        llm_config = Mock(spec=LLMConfig)
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []
        ai_hub_model = Mock(spec=AIHubModel)
        ai_hub_model.name = "test_model"
        ai_hub_model.provider = "test_provider"
        ai_hub_model.version = "1.0"
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        adapter.ai_hub_model = ai_hub_model
        
        # 创建模拟参数
        messages = [Message(role="user", content="test")]
        model_args = MultiModelArgs(
            messages=messages,
            stream=True,
            temperature=0.5,
            max_token=1000,
            adapter="test_adapter",
            return_usage=True,
            extra_param="extra_value"
        )
        
        # 模拟LLModelRpc
        mock_result = ["status", "response_data"]
        with patch('commons.adapter.public_gateway_adapter.LLModelRpc') as mock_llm_rpc:
            mock_instance = Mock()
            mock_instance.async_multimodal = AsyncMock(return_value=mock_result)
            mock_llm_rpc.return_value = mock_instance
            
            result = await adapter.async_multimodal(model_args)
            
            # 验证结果
            assert result == mock_result
            
            # 验证LLModelRpc被正确调用
            mock_instance.async_multimodal.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_text_success(self):
        """测试async_chat_text方法成功调用"""
        # 创建模拟配置
        llm_config = Mock(spec=LLMConfig)
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []
        ai_hub_model = Mock(spec=AIHubModel)
        ai_hub_model.name = "test_model"
        ai_hub_model.provider = "test_provider"
        ai_hub_model.version = "1.0"
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        adapter.ai_hub_model = ai_hub_model
        
        # 创建模拟参数
        messages = [Message(role="user", content="test")]
        model_args = AsyncChatTextArgs(
            prompt="test prompt",
            messages=messages,
            query="test query",
            temperature=0.3,
            max_token=1500,
            repetition_penalty=1.2,
            adapter="test_adapter",
            return_usage=True,
            extra_param="extra_value"
        )
        
        # 模拟LLModelRpc
        mock_result = ["status", "chat_response"]
        with patch('commons.adapter.public_gateway_adapter.LLModelRpc') as mock_llm_rpc:
            mock_instance = Mock()
            mock_instance.async_chat_text = AsyncMock(return_value=mock_result)
            mock_llm_rpc.return_value = mock_instance
            
            result = await adapter.async_chat_text(model_args)
            
            # 验证结果
            assert result == mock_result
            
            # 验证LLModelRpc被正确调用
            mock_instance.async_chat_text.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_multimodal_config_check(self):
        """测试async_multimodal方法会调用配置检查"""
        llm_config = Mock(spec=LLMConfig)
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []
        ai_hub_model = Mock(spec=AIHubModel)
        ai_hub_model.name = "test_model"
        ai_hub_model.provider = "test_provider"
        ai_hub_model.version = "1.0"
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        adapter.ai_hub_model = ai_hub_model
        
        # 模拟_check_config方法
        with patch.object(adapter, '_check_config') as mock_check_config:
            with patch('commons.adapter.public_gateway_adapter.LLModelRpc') as mock_llm_rpc:
                mock_instance = Mock()
                mock_instance.async_multimodal = AsyncMock(return_value=["status", "response"])
                mock_llm_rpc.return_value = mock_instance
                
                model_args = MultiModelArgs()
                await adapter.async_multimodal(model_args)
                
                # 验证_check_config被调用
                mock_check_config.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_text_config_check(self):
        """测试async_chat_text方法会调用配置检查"""
        llm_config = Mock(spec=LLMConfig)
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []
        ai_hub_model = Mock(spec=AIHubModel)
        ai_hub_model.name = "test_model"
        ai_hub_model.provider = "test_provider"
        ai_hub_model.version = "1.0"
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        adapter.ai_hub_model = ai_hub_model
        
        # 模拟_check_config方法
        with patch.object(adapter, '_check_config') as mock_check_config:
            with patch('commons.adapter.public_gateway_adapter.LLModelRpc') as mock_llm_rpc:
                mock_instance = Mock()
                mock_instance.async_chat_text = AsyncMock(return_value=["status", "response"])
                mock_llm_rpc.return_value = mock_instance
                
                model_args = AsyncChatTextArgs()
                await adapter.async_chat_text(model_args)
                
                # 验证_check_config被调用
                mock_check_config.assert_called_once()

    def test_public_gateway_adapter_inheritance(self):
        """测试PublicGatewayAdapter继承自GatewayBase"""
        from commons.adapter.gateway_base import GatewayBase
        assert issubclass(PublicGatewayAdapter, GatewayBase)

    @pytest.mark.asyncio
    async def test_async_multimodal_with_real_config(self):
        """测试使用真实配置对象"""
        # 创建真实的配置对象
        llm_config = LLMConfig(
            gateway=LLModelRpc.Gateway.Public,
            provider="test_provider",
            model="test_model",
            version="1.0"
        )
        
        public_gateway_header = PublicGatewayHeader()
        public_gateway_header.ai_hub_models = []
        ai_hub_model = AIHubModel(
            name="test_model",
            provider="test_provider",
            version="1.0"
        )
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        adapter.ai_hub_model = ai_hub_model
        
        model_args = MultiModelArgs(messages=[])
        
        mock_result = ["status", "real_config_response"]
        with patch('commons.adapter.public_gateway_adapter.LLModelRpc') as mock_llm_rpc:
            mock_instance = Mock()
            mock_instance.async_multimodal = AsyncMock(return_value=mock_result)
            mock_llm_rpc.return_value = mock_instance
            
            result = await adapter.async_multimodal(model_args)
            
            assert result == mock_result

    def test_choice_ai_hub_model_by_config_multiple_matches(self):
        """测试_choice_ai_hub_model_by_config方法多个匹配时选择第一个"""
        llm_config = Mock(spec=LLMConfig)
        llm_config.provider = "test_provider"
        llm_config.model = "test_model"
        llm_config.version = "1.0"
        
        # 创建多个匹配的AIHubModel
        matching_model1 = Mock(spec=AIHubModel)
        matching_model1.provider = "test_provider"
        matching_model1.name = "test_model"
        matching_model1.version = "1.0"
        
        matching_model2 = Mock(spec=AIHubModel)
        matching_model2.provider = "test_provider"
        matching_model2.name = "test_model"
        matching_model2.version = "1.0"
        
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = [matching_model1, matching_model2]
        
        adapter = PublicGatewayAdapter(llm_config, public_gateway_header)
        
        # 验证选择第一个匹配的模型
        assert adapter.ai_hub_model == matching_model1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
