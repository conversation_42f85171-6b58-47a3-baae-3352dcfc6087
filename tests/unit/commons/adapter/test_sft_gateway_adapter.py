# Author: linqi
# Date: 2025/8/21
# Time: 10:45

import pytest
from unittest.mock import Mock, AsyncMock, patch
from commons.adapter.sft_gateway_adapter import SftGatewayAdapter
from commons.adapter.gateway_base import MultiModelArgs, AsyncChatTextArgs
from commons.llm_gateway.models.chat_data import LLMConfig, Message
from commons.llm_gateway.llm import LLModelRpc


class TestSftGatewayAdapter:
    """测试SftGatewayAdapter类"""
    
    def test_sft_gateway_adapter_initialization(self):
        """测试SftGatewayAdapter初始化"""
        llm_config = Mock(spec=LLMConfig)
        adapter = SftGatewayAdapter(llm_config)
        
        assert adapter.llm_config == llm_config

    def test_check_config_success(self):
        """测试_check_config方法成功情况"""
        llm_config = Mock(spec=LLMConfig)
        adapter = SftGatewayAdapter(llm_config)
        
        # 应该不抛出异常
        adapter._check_config()

    def test_check_config_no_config(self):
        """测试_check_config方法配置为空时抛出异常"""
        adapter = SftGatewayAdapter(None)
        
        with pytest.raises(Exception, match="模型配置未加载"):
            adapter._check_config()

    @pytest.mark.asyncio
    async def test_async_multimodal_success(self):
        """测试async_multimodal方法成功调用"""
        # 创建模拟配置
        llm_config = Mock(spec=LLMConfig)
        llm_config.sft = Mock()
        llm_config.sft.lora_model = "test_lora_model"
        llm_config.sft.base_model = "test_base_model"
        
        adapter = SftGatewayAdapter(llm_config)
        
        # 创建模拟参数
        messages = [Message(role="user", content="test")]
        model_args = MultiModelArgs(
            messages=messages,
            stream=True,
            temperature=0.5,
            max_token=1000,
            adapter="test_adapter",
            return_usage=True,
            extra_param="extra_value"
        )
        
        # 模拟LLModelRpc
        mock_result = ["status", "response_data"]
        with patch('commons.adapter.sft_gateway_adapter.LLModelRpc') as mock_llm_rpc:
            mock_instance = Mock()
            mock_instance.async_multimodal = AsyncMock(return_value=mock_result)
            mock_llm_rpc.return_value = mock_instance
            
            result = await adapter.async_multimodal(model_args)
            
            # 验证结果
            assert result == mock_result
            
            # 验证LLModelRpc被正确调用
            mock_instance.async_multimodal.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_multimodal_with_default_args(self):
        """测试async_multimodal方法使用默认参数"""
        llm_config = Mock(spec=LLMConfig)
        llm_config.sft = Mock()
        llm_config.sft.lora_model = "test_lora_model"
        llm_config.sft.base_model = "test_base_model"
        
        adapter = SftGatewayAdapter(llm_config)
        
        # 使用默认参数
        model_args = MultiModelArgs()
        
        mock_result = ["status", "default_response"]
        with patch('commons.adapter.sft_gateway_adapter.LLModelRpc') as mock_llm_rpc:
            mock_instance = Mock()
            mock_instance.async_multimodal = AsyncMock(return_value=mock_result)
            mock_llm_rpc.return_value = mock_instance
            
            result = await adapter.async_multimodal(model_args)
            
            assert result == mock_result
            
            # 验证默认参数
            call_args = mock_instance.async_multimodal.call_args
            assert call_args[1]['stream'] is False
            assert call_args[1]['temperature'] == 0.7
            assert call_args[1]['max_token'] == 2000
            assert call_args[1]['adapter'] == ""
            assert call_args[1]['return_usage'] is False

    @pytest.mark.asyncio
    async def test_async_chat_text_success(self):
        """测试async_chat_text方法成功调用"""
        # 创建模拟配置
        llm_config = Mock(spec=LLMConfig)
        llm_config.sft = Mock()
        llm_config.sft.lora_model = "test_lora_model"
        llm_config.sft.base_model = "test_base_model"
        
        adapter = SftGatewayAdapter(llm_config)
        
        # 创建模拟参数
        messages = [Message(role="user", content="test")]
        model_args = AsyncChatTextArgs(
            prompt="test prompt",
            messages=messages,
            query="test query",
            temperature=0.3,
            max_token=1500,
            repetition_penalty=1.2,
            adapter="test_adapter",
            return_usage=True,
            extra_param="extra_value"
        )
        
        # 模拟LLModelRpc
        mock_result = ["status", "chat_response"]
        with patch('commons.adapter.sft_gateway_adapter.LLModelRpc') as mock_llm_rpc:
            mock_instance = Mock()
            mock_instance.async_chat_text = AsyncMock(return_value=mock_result)
            mock_llm_rpc.return_value = mock_instance
            
            result = await adapter.async_chat_text(model_args)
            
            # 验证结果
            assert result == mock_result
            
            # 验证LLModelRpc被正确调用
            mock_instance.async_chat_text.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_text_with_default_args(self):
        """测试async_chat_text方法使用默认参数"""
        llm_config = Mock(spec=LLMConfig)
        llm_config.sft = Mock()
        llm_config.sft.lora_model = "test_lora_model"
        llm_config.sft.base_model = "test_base_model"
        
        adapter = SftGatewayAdapter(llm_config)
        
        # 使用默认参数
        model_args = AsyncChatTextArgs()
        
        mock_result = ["status", "default_chat_response"]
        with patch('commons.adapter.sft_gateway_adapter.LLModelRpc') as mock_llm_rpc:
            mock_instance = Mock()
            mock_instance.async_chat_text = AsyncMock(return_value=mock_result)
            mock_llm_rpc.return_value = mock_instance
            
            result = await adapter.async_chat_text(model_args)
            
            assert result == mock_result
            
            # 验证默认参数
            call_args = mock_instance.async_chat_text.call_args
            assert call_args[1]['query'] is None
            assert call_args[1]['temperature'] == 0.7
            assert call_args[1]['max_token'] == 2000
            assert call_args[1]['repetition_penalty'] == 1.0
            assert call_args[1]['adapter'] == ""
            assert call_args[1]['return_usage'] is False

    @pytest.mark.asyncio
    async def test_async_multimodal_config_check(self):
        """测试async_multimodal方法会调用配置检查"""
        llm_config = Mock(spec=LLMConfig)
        llm_config.sft = Mock()
        llm_config.sft.lora_model = "test_lora_model"
        llm_config.sft.base_model = "test_base_model"
        
        adapter = SftGatewayAdapter(llm_config)
        
        # 模拟_check_config方法
        with patch.object(adapter, '_check_config') as mock_check_config:
            with patch('commons.adapter.sft_gateway_adapter.LLModelRpc') as mock_llm_rpc:
                mock_instance = Mock()
                mock_instance.async_multimodal = AsyncMock(return_value=["status", "response"])
                mock_llm_rpc.return_value = mock_instance
                
                model_args = MultiModelArgs()
                await adapter.async_multimodal(model_args)
                
                # 验证_check_config被调用
                mock_check_config.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_chat_text_config_check(self):
        """测试async_chat_text方法会调用配置检查"""
        llm_config = Mock(spec=LLMConfig)
        llm_config.sft = Mock()
        llm_config.sft.lora_model = "test_lora_model"
        llm_config.sft.base_model = "test_base_model"
        
        adapter = SftGatewayAdapter(llm_config)
        
        # 模拟_check_config方法
        with patch.object(adapter, '_check_config') as mock_check_config:
            with patch('commons.adapter.sft_gateway_adapter.LLModelRpc') as mock_llm_rpc:
                mock_instance = Mock()
                mock_instance.async_chat_text = AsyncMock(return_value=["status", "response"])
                mock_llm_rpc.return_value = mock_instance
                
                model_args = AsyncChatTextArgs()
                await adapter.async_chat_text(model_args)
                
                # 验证_check_config被调用
                mock_check_config.assert_called_once()

    def test_sft_gateway_adapter_inheritance(self):
        """测试SftGatewayAdapter继承自GatewayBase"""
        from commons.adapter.gateway_base import GatewayBase
        assert issubclass(SftGatewayAdapter, GatewayBase)

    @pytest.mark.asyncio
    async def test_async_multimodal_with_real_config(self):
        """测试使用真实配置对象"""
        # 创建真实的LLMConfig
        from commons.llm_gateway.models.chat_data import SftLLMConfig
        
        llm_config = LLMConfig(
            gateway=LLModelRpc.Gateway.Sft,
            provider="test_provider",
            model="test_model",
            version="1.0"
        )
        llm_config.sft = SftLLMConfig(
            base_model="real_base_model",
            lora_model="real_lora_model"
        )
        
        adapter = SftGatewayAdapter(llm_config)
        
        model_args = MultiModelArgs(messages=[])
        
        mock_result = ["status", "real_config_response"]
        with patch('commons.adapter.sft_gateway_adapter.LLModelRpc') as mock_llm_rpc:
            mock_instance = Mock()
            mock_instance.async_multimodal = AsyncMock(return_value=mock_result)
            mock_llm_rpc.return_value = mock_instance
            
            result = await adapter.async_multimodal(model_args)
            
            assert result == mock_result


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
