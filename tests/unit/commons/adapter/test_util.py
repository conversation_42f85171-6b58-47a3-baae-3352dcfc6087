# Author: linqi
# Date: 2025/8/21
# Time: 10:45

import pytest
from unittest.mock import Mock, patch
from commons.adapter.util import create_gateway_adapter
from commons.llm_gateway.models.chat_data import LLMConfig, PublicGatewayHeader
from commons.llm_gateway.llm import LLModelRpc


class TestCreateGatewayAdapter:
    """测试create_gateway_adapter函数"""
    
    def test_create_gateway_adapter_sft_gateway(self):
        """测试创建SFT网关适配器"""
        # 创建模拟配置
        llm_config = Mock(spec=LLMConfig)
        llm_config.gateway = LLModelRpc.Gateway.Sft
        
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        
        # 测试函数调用
        adapter = create_gateway_adapter(llm_config, public_gateway_header)
        
        # 验证返回的是SftGatewayAdapter实例
        from commons.adapter.sft_gateway_adapter import SftGatewayAdapter
        assert isinstance(adapter, SftGatewayAdapter)
        assert adapter.llm_config == llm_config

    def test_create_gateway_adapter_public_gateway(self):
        """测试创建Public网关适配器"""
        # 创建模拟配置
        llm_config = Mock(spec=LLMConfig)
        llm_config.gateway = LLModelRpc.Gateway.Public
        
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []  # 添加ai_hub_models属性
        
        # 测试函数调用
        adapter = create_gateway_adapter(llm_config, public_gateway_header)
        
        # 验证返回的是PublicGatewayAdapter实例
        from commons.adapter.public_gateway_adapter import PublicGatewayAdapter
        assert isinstance(adapter, PublicGatewayAdapter)
        assert adapter.llm_config == llm_config
        assert adapter.public_gateway_header == public_gateway_header

    def test_create_gateway_adapter_private_gateway(self):
        """测试创建Private网关适配器（应该返回PublicGatewayAdapter）"""
        # 创建模拟配置
        llm_config = Mock(spec=LLMConfig)
        llm_config.gateway = LLModelRpc.Gateway.Private
        
        public_gateway_header = Mock(spec=PublicGatewayHeader)
        public_gateway_header.ai_hub_models = []  # 添加ai_hub_models属性
        
        # 测试函数调用
        adapter = create_gateway_adapter(llm_config, public_gateway_header)
        
        # 验证返回的是PublicGatewayAdapter实例（非SFT网关都返回PublicGatewayAdapter）
        from commons.adapter.public_gateway_adapter import PublicGatewayAdapter
        assert isinstance(adapter, PublicGatewayAdapter)
        assert adapter.llm_config == llm_config
        assert adapter.public_gateway_header == public_gateway_header

    def test_create_gateway_adapter_with_real_configs(self):
        """测试使用真实配置对象创建适配器"""
        # 创建真实的LLMConfig
        llm_config = LLMConfig(
            gateway=LLModelRpc.Gateway.Sft,
            provider="test_provider",
            model="test_model",
            version="1.0"
        )
        
        public_gateway_header = PublicGatewayHeader()
        public_gateway_header.ai_hub_models = []  # 添加ai_hub_models属性
        
        # 测试SFT网关
        adapter = create_gateway_adapter(llm_config, public_gateway_header)
        from commons.adapter.sft_gateway_adapter import SftGatewayAdapter
        assert isinstance(adapter, SftGatewayAdapter)
        
        # 测试Public网关
        llm_config.gateway = LLModelRpc.Gateway.Public
        adapter = create_gateway_adapter(llm_config, public_gateway_header)
        from commons.adapter.public_gateway_adapter import PublicGatewayAdapter
        assert isinstance(adapter, PublicGatewayAdapter)

    def test_create_gateway_adapter_imports(self):
        """测试函数正确导入了所需的类"""
        # 验证函数可以正常导入所需的适配器类
        from commons.adapter.sft_gateway_adapter import SftGatewayAdapter
        from commons.adapter.public_gateway_adapter import PublicGatewayAdapter
        
        assert SftGatewayAdapter is not None
        assert PublicGatewayAdapter is not None

    def test_create_gateway_adapter_edge_cases(self):
        """测试边界情况"""
        # 测试None配置
        with pytest.raises(Exception):
            create_gateway_adapter(None, Mock(spec=PublicGatewayHeader))
        
        # 测试None header（对于非SFT网关）
        llm_config = Mock(spec=LLMConfig)
        llm_config.gateway = LLModelRpc.Gateway.Public
        
        with pytest.raises(Exception):
            create_gateway_adapter(llm_config, None)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
