# -*- coding: utf-8 -*-
"""
Unit tests for modules.flows.callback module
"""

import json
import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from datetime import datetime

from modules.flows.callback import (
    callback_parse_background,
    upload_result_and_set_status,
    set_parse_target_status,
    get_parse_target_status
)
from modules.entity.parse_entity import (
    GeneralParseStatus,
    RespGeneralParseData,
    ParseTarget,
    PARSE_BACKGROUND_STATUS_KEY,
    PARSE_BACKGROUND_CONTENT_PATH_KEY,
    PARSE_BACKGROUND_PARSE_TARGET_KEY,
    DAY_7
)
from modules.entity.chunk_entity import Chunk, LabelType


class TestCallbackParseBackground:
    """测试 callback_parse_background 函数"""

    @pytest.mark.asyncio
    async def test_callback_parse_background_success_with_ks3_url(self):
        """测试成功回调并返回KS3 URL"""
        # Mock dependencies
        with patch('modules.flows.callback.upload_result_and_set_status') as mock_upload, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            # Setup mocks
            mock_upload.return_value = "https://ks3.example.com/file.json"
            mock_get_host.return_value = ("example.com", "/callback")
            mock_client_instance = AsyncMock()
            mock_rpc_client.return_value = mock_client_instance
            
            # Create test result
            result = MagicMock()
            result.dict.return_value = {"test": "data"}
            
            # Call function
            await callback_parse_background(
                result=result,
                step_name="test_step",
                token="test_token",
                need_callback=True,
                return_ks3_url=True,
                callback_url="https://example.com/callback"
            )
            
            # Verify calls
            mock_upload.assert_called_once_with(result, "test_step", "test_token")
            mock_get_host.assert_called_once_with("https://example.com/callback")
            mock_client_instance.send_callback.assert_called_once()

    @pytest.mark.asyncio
    async def test_callback_parse_background_success_with_parse_res(self):
        """测试成功回调并返回解析结果"""
        with patch('modules.flows.callback.upload_result_and_set_status') as mock_upload, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            mock_upload.return_value = "https://ks3.example.com/file.json"
            mock_get_host.return_value = ("example.com", "/callback")
            mock_client_instance = AsyncMock()
            mock_rpc_client.return_value = mock_client_instance
            
            result = MagicMock()
            result.dict.return_value = {"test": "data"}
            
            await callback_parse_background(
                result=result,
                step_name="test_step",
                token="test_token",
                need_callback=True,
                return_ks3_url=False,
                callback_url="https://example.com/callback"
            )
            
            mock_client_instance.send_callback.assert_called_once()

    @pytest.mark.asyncio
    async def test_callback_parse_background_no_callback(self):
        """测试不需要回调的情况"""
        with patch('modules.flows.callback.upload_result_and_set_status') as mock_upload, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            mock_upload.return_value = "https://ks3.example.com/file.json"
            mock_get_host.return_value = ("example.com", "/callback")
            mock_client_instance = AsyncMock()
            mock_rpc_client.return_value = mock_client_instance
            
            result = MagicMock()
            result.dict.return_value = {"test": "data"}
            
            await callback_parse_background(
                result=result,
                step_name="test_step",
                token="test_token",
                need_callback=False,
                return_ks3_url=True,
                callback_url="https://example.com/callback"
            )
            
            # Should not call send_callback
            mock_client_instance.send_callback.assert_not_called()

    @pytest.mark.asyncio
    async def test_callback_parse_background_with_none_result(self):
        """测试结果为None的情况"""
        with patch('modules.flows.callback.upload_result_and_set_status') as mock_upload, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            mock_upload.return_value = None
            mock_get_host.return_value = ("example.com", "/callback")
            mock_client_instance = AsyncMock()
            mock_rpc_client.return_value = mock_client_instance
            
            await callback_parse_background(
                result=None,
                step_name="test_step",
                token="test_token",
                need_callback=True,
                return_ks3_url=True,
                callback_url="https://example.com/callback"
            )
            
            mock_upload.assert_called_once_with(None, "test_step", "test_token")

    @pytest.mark.asyncio
    async def test_callback_parse_background_rpc_exception(self):
        """测试RPC调用异常的情况"""
        with patch('modules.flows.callback.upload_result_and_set_status') as mock_upload, \
             patch('modules.flows.callback.get_host_and_uri') as mock_get_host, \
             patch('modules.flows.callback.RpcCallbackClient') as mock_rpc_client:
            
            mock_upload.return_value = "https://ks3.example.com/file.json"
            mock_get_host.return_value = ("example.com", "/callback")
            mock_client_instance = AsyncMock()
            mock_client_instance.send_callback.side_effect = Exception("RPC Error")
            mock_rpc_client.return_value = mock_client_instance
            
            result = MagicMock()
            result.dict.return_value = {"test": "data"}
            
            with pytest.raises(Exception, match="Failed to callback result"):
                await callback_parse_background(
                    result=result,
                    step_name="test_step",
                    token="test_token",
                    need_callback=True,
                    return_ks3_url=True,
                    callback_url="https://example.com/callback"
                )


class TestUploadResultAndSetStatus:
    """测试 upload_result_and_set_status 函数"""

    def test_upload_result_and_set_status_success(self):
        """测试成功上传结果"""
        with patch('modules.flows.callback.StoreDao') as mock_store_dao, \
             patch('modules.flows.callback.Redis5Dao') as mock_redis_dao, \
             patch('modules.flows.callback.ConfStore') as mock_conf_store, \
             patch('modules.flows.callback.datetime') as mock_datetime:
            
            # Setup mocks
            mock_conf_store.back_store_dir = "/test/store"
            mock_datetime.now.return_value.strftime.return_value = "20240101"
            
            mock_store_instance = MagicMock()
            mock_store_dao.return_value = mock_store_instance
            mock_store_instance.upload_from_bytes.return_value = True
            mock_store_instance.generate_url.return_value = "https://ks3.example.com/file.json"
            
            mock_redis_instance = MagicMock()
            mock_redis_dao.return_value = mock_redis_instance
            
            # Create test result
            result = MagicMock()
            result.dict.return_value = {"test": "data"}
            
            # Call function
            url = upload_result_and_set_status(result, "test_step", "test_token")
            
            # Verify calls
            assert url == "https://ks3.example.com/file.json"
            mock_store_instance.upload_from_bytes.assert_called_once()
            mock_store_instance.generate_url.assert_called_once()
            mock_redis_instance.set.assert_called_once()

    def test_upload_result_and_set_status_upload_failure(self):
        """测试上传失败的情况"""
        with patch('modules.flows.callback.StoreDao') as mock_store_dao, \
             patch('modules.flows.callback.ConfStore') as mock_conf_store, \
             patch('modules.flows.callback.datetime') as mock_datetime:
            
            mock_conf_store.back_store_dir = "/test/store"
            mock_datetime.now.return_value.strftime.return_value = "20240101"
            
            mock_store_instance = MagicMock()
            mock_store_dao.return_value = mock_store_instance
            mock_store_instance.upload_from_bytes.return_value = False
            
            result = MagicMock()
            result.dict.return_value = {"test": "data"}
            
            with pytest.raises(Exception, match="Failed to upload parsing result to KS3"):
                upload_result_and_set_status(result, "test_step", "test_token")

    def test_upload_result_and_set_status_none_result(self):
        """测试结果为None的情况"""
        with patch('modules.flows.callback.Redis5Dao') as mock_redis_dao:
            mock_redis_instance = MagicMock()
            mock_redis_dao.return_value = mock_redis_instance
            
            url = upload_result_and_set_status(None, "test_step", "test_token")
            
            assert url is None
            mock_redis_instance.set.assert_called_once_with(
                f"{PARSE_BACKGROUND_STATUS_KEY}_test_token",
                GeneralParseStatus.fail,
                ex=DAY_7
            )


class TestParseTargetStatus:
    """测试 parse target 状态相关函数"""

    def test_set_parse_target_status(self):
        """测试设置解析目标状态"""
        with patch('modules.flows.callback.Redis5Dao') as mock_redis_dao:
            mock_redis_instance = MagicMock()
            mock_redis_dao.return_value = mock_redis_instance
            
            parse_targets = [ParseTarget.chunk, ParseTarget.fake_title]
            result = set_parse_target_status("test_token", parse_targets)
            
            assert result is True
            mock_redis_instance.set.assert_called_once()
            
            # Verify the JSON data
            call_args = mock_redis_instance.set.call_args
            assert call_args[0][0] == f"{PARSE_BACKGROUND_PARSE_TARGET_KEY}_test_token"
            assert call_args[1]['ex'] == DAY_7
            
            # Verify JSON content
            stored_data = json.loads(call_args[0][1])
            assert stored_data == ["chunk", "fake_title"]

    def test_get_parse_target_status_with_data(self):
        """测试获取解析目标状态（有数据）"""
        with patch('modules.flows.callback.Redis5Dao') as mock_redis_dao:
            mock_redis_instance = MagicMock()
            mock_redis_dao.return_value = mock_redis_instance
            
            # Mock Redis returning JSON data
            mock_redis_instance.get.return_value = '["chunk", "fake_title"]'
            
            result = get_parse_target_status("test_token")
            
            assert len(result) == 2
            assert result[0] == ParseTarget.chunk
            assert result[1] == ParseTarget.fake_title
            mock_redis_instance.get.assert_called_once_with(
                f"{PARSE_BACKGROUND_PARSE_TARGET_KEY}_test_token"
            )

    def test_get_parse_target_status_no_data(self):
        """测试获取解析目标状态（无数据）"""
        with patch('modules.flows.callback.Redis5Dao') as mock_redis_dao:
            mock_redis_instance = MagicMock()
            mock_redis_dao.return_value = mock_redis_instance
            
            # Mock Redis returning None
            mock_redis_instance.get.return_value = None
            
            result = get_parse_target_status("test_token")
            
            assert result == []
            mock_redis_instance.get.assert_called_once_with(
                f"{PARSE_BACKGROUND_PARSE_TARGET_KEY}_test_token"
            )

    def test_get_parse_target_status_empty_data(self):
        """测试获取解析目标状态（空数据）"""
        with patch('modules.flows.callback.Redis5Dao') as mock_redis_dao:
            mock_redis_instance = MagicMock()
            mock_redis_dao.return_value = mock_redis_instance
            
            # Mock Redis returning empty string
            mock_redis_instance.get.return_value = ""
            
            result = get_parse_target_status("test_token")
            
            assert result == []