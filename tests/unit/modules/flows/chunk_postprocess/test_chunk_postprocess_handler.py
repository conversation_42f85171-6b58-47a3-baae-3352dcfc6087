# Author: linqi
# Date: 2025/8/13
# Time: 12:03

import pytest
from unittest.mock import AsyncMock, patch

from modules.flows.chunk_postprocess.chunk_postprocess_handler import ChunkPostProcessNode
from modules.pipeline.context import PipelineContext, FileInfo
from modules.entity.parse_entity import Image, ImageType
from modules.entity.chunk_entity import Chunk, LabelType
from modules.entity.dst_entity import DST, DSTType, DSTAttribute
from services.datamodel import FileType
from conf import ConfH<PERSON>lerName


def _dst(id_, parent, level, page, text, dst_type: DSTType = DSTType.TEXT):
    return DST(
        id=id_, parent=parent, order=0, dst_type=dst_type,
        attributes=DSTAttribute(level=level, position="p", page=page, hash="h" * 32),
        content=[text] if dst_type != DSTType.IMAGE else ["url", text],
    )


def _chunk(cid: str, text: str, pages, blocks, dsts):
    return Chunk(
        chunk_id=cid, page_size=1, content=text, label=LabelType.TEXT,
        page_num=pages, block=blocks, dsts=dsts,
    )


@pytest.mark.asyncio
async def test_postprocess_adds_image_descriptions_and_links(monkeypatch):
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    # stub async callback to no-op
    async def _cb(*args, **kwargs):
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    # Prepare context
    file_info = FileInfo(file_type=FileType.DOCX, page_size=2, word_count=10, width=100, height=200, is_scan=False)
    ctx = PipelineContext(file_info=file_info)

    # Prepare chunks: one with image dst and matching id for description
    img_dst = _dst("img1", "-1", 10, 0, "ocr_desc", DSTType.IMAGE)
    text_dst = _dst("t1", "-1", 10, 0, "text")
    ch = _chunk("0", "凶☒冈 some text", [0], ["img1", "t1"], [img_dst, text_dst])
    ctx.chunks = [ch]

    # images: TABLE_IMAGE should link to chunk containing the dst id; CHECK_BOX_IMAGE should link by page if content matches
    table_image = Image(page_num=0, url="u1", chunk_ids=None, dst_ids=["img1"], image_type=ImageType.TABLE_IMAGE)
    checkbox_image = Image(page_num=0, url="u2", chunk_ids=None, dst_ids=None, image_type=ImageType.CHECK_BOX_IMAGE)
    ctx.handler_results[ConfHandlerName.screenshot_handler] = [table_image, checkbox_image]

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # TABLE_IMAGE linked via dst id
    assert table_image.chunk_ids == ["0"]
    # CHECK_BOX_IMAGE linked via page and specific chars detection (content includes [x])
    assert checkbox_image.chunk_ids == ["0"]


@pytest.mark.asyncio
async def test_postprocess_no_images_or_chunks_calls_noop(monkeypatch):
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    called = {"n": 0}

    async def _cb(*args, **kwargs):
        called["n"] += 1
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    # neither chunks nor images present
    out = await ChunkPostProcessNode("cpp").process(ctx)
    assert out is ctx
    # no callback should be invoked
    assert called["n"] == 0


@pytest.mark.asyncio
async def test_postprocess_with_table_pics(monkeypatch):
    """测试处理table_pics的情况"""
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    async def _cb(*args, **kwargs):
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    
    # 添加table_pics
    table_pic = Image(page_num=0, url="table_url", chunk_ids=None, dst_ids=["table1"], image_type=ImageType.TABLE_IMAGE)
    ctx.table_pics = [table_pic]
    
    # 添加匹配的chunk
    chunk = _chunk("0", "table content", [0], ["table1"], [])
    ctx.chunks = [chunk]

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # 验证table_pic被正确链接
    assert table_pic.chunk_ids == ["0"]


@pytest.mark.asyncio
async def test_postprocess_table_image_no_dst_ids(monkeypatch):
    """测试TABLE_IMAGE没有dst_ids的情况"""
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    async def _cb(*args, **kwargs):
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    
    # TABLE_IMAGE没有dst_ids
    table_image = Image(page_num=0, url="u1", chunk_ids=None, dst_ids=None, image_type=ImageType.TABLE_IMAGE)
    ctx.handler_results[ConfHandlerName.screenshot_handler] = [table_image]
    
    chunk = _chunk("0", "content", [0], ["block1"], [])
    ctx.chunks = [chunk]

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # 应该没有链接
    assert table_image.chunk_ids is None


@pytest.mark.asyncio
async def test_postprocess_table_image_no_matching_chunk(monkeypatch):
    """测试TABLE_IMAGE没有匹配chunk的情况"""
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    async def _cb(*args, **kwargs):
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    
    # TABLE_IMAGE有dst_ids但不匹配任何chunk
    table_image = Image(page_num=0, url="u1", chunk_ids=None, dst_ids=["nonexistent"], image_type=ImageType.TABLE_IMAGE)
    ctx.handler_results[ConfHandlerName.screenshot_handler] = [table_image]
    
    chunk = _chunk("0", "content", [0], ["block1"], [])
    ctx.chunks = [chunk]

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # 应该没有链接
    assert table_image.chunk_ids is None


@pytest.mark.asyncio
async def test_postprocess_checkbox_image_no_specific_chars(monkeypatch):
    """测试CHECK_BOX_IMAGE没有特定字符的情况"""
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    async def _cb(*args, **kwargs):
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    
    # CHECK_BOX_IMAGE
    checkbox_image = Image(page_num=0, url="u2", chunk_ids=None, dst_ids=None, image_type=ImageType.CHECK_BOX_IMAGE)
    ctx.handler_results[ConfHandlerName.screenshot_handler] = [checkbox_image]
    
    # chunk内容没有特定字符
    chunk = _chunk("0", "normal text content", [0], ["block1"], [])
    ctx.chunks = [chunk]

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # 应该没有链接
    assert checkbox_image.chunk_ids is None


@pytest.mark.asyncio
async def test_postprocess_checkbox_image_wrong_page(monkeypatch):
    """测试CHECK_BOX_IMAGE页面不匹配的情况"""
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    async def _cb(*args, **kwargs):
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    
    # CHECK_BOX_IMAGE在页面1
    checkbox_image = Image(page_num=1, url="u2", chunk_ids=None, dst_ids=None, image_type=ImageType.CHECK_BOX_IMAGE)
    ctx.handler_results[ConfHandlerName.screenshot_handler] = [checkbox_image]
    
    # chunk在页面0
    chunk = _chunk("0", "凶☒冈 some text", [0], ["block1"], [])
    ctx.chunks = [chunk]

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # 应该没有链接
    assert checkbox_image.chunk_ids is None


@pytest.mark.asyncio
async def test_postprocess_with_images_only(monkeypatch):
    """测试只有images没有chunks的情况"""
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    called = {"n": 0}

    async def _cb(*args, **kwargs):
        called["n"] += 1
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    
    # 只有images
    image = Image(page_num=0, url="u1", chunk_ids=None, dst_ids=None, image_type=ImageType.TABLE_IMAGE)
    ctx.handler_results[ConfHandlerName.screenshot_handler] = [image]
    ctx.chunks = None

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # 应该调用callback但不会链接chunks
    assert called["n"] == 1


@pytest.mark.asyncio
async def test_postprocess_with_chunks_only(monkeypatch):
    """测试只有chunks没有images的情况"""
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    called = {"n": 0}

    async def _cb(*args, **kwargs):
        called["n"] += 1
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    
    # 只有chunks
    chunk = _chunk("0", "content", [0], ["block1"], [])
    ctx.chunks = [chunk]
    ctx.handler_results[ConfHandlerName.screenshot_handler] = []

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # 应该调用callback
    assert called["n"] == 1


@pytest.mark.asyncio
async def test_postprocess_table_image_existing_chunk_ids(monkeypatch):
    """测试TABLE_IMAGE已有chunk_ids的情况"""
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    async def _cb(*args, **kwargs):
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    
    # TABLE_IMAGE已有chunk_ids
    table_image = Image(page_num=0, url="u1", chunk_ids=["existing"], dst_ids=["img1"], image_type=ImageType.TABLE_IMAGE)
    ctx.handler_results[ConfHandlerName.screenshot_handler] = [table_image]
    
    chunk = _chunk("0", "content", [0], ["img1"], [])
    ctx.chunks = [chunk]

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # 应该添加新的chunk_id到现有列表
    assert "existing" in table_image.chunk_ids
    assert "0" in table_image.chunk_ids


@pytest.mark.asyncio
async def test_postprocess_checkbox_image_existing_chunk_ids(monkeypatch):
    """测试CHECK_BOX_IMAGE已有chunk_ids的情况"""
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    async def _cb(*args, **kwargs):
        return None

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    
    # CHECK_BOX_IMAGE已有chunk_ids
    checkbox_image = Image(page_num=0, url="u2", chunk_ids=["existing"], dst_ids=None, image_type=ImageType.CHECK_BOX_IMAGE)
    ctx.handler_results[ConfHandlerName.screenshot_handler] = [checkbox_image]
    
    chunk = _chunk("0", "凶☒冈 some text", [0], ["block1"], [])
    ctx.chunks = [chunk]

    node = ChunkPostProcessNode("chunk_postprocess")
    out = await node.process(ctx)

    # 应该添加新的chunk_id到现有列表
    assert "existing" in checkbox_image.chunk_ids
    assert "0" in checkbox_image.chunk_ids


@pytest.mark.asyncio
async def test_postprocess_callback_exception_handling(monkeypatch):
    """测试callback异常处理"""
    import modules.flows.chunk_postprocess.chunk_postprocess_handler as mod

    async def _cb(*args, **kwargs):
        raise Exception("Callback failed")

    monkeypatch.setattr(mod, "callback_parse_background", _cb)

    file_info = FileInfo(file_type=FileType.PDF, page_size=1)
    ctx = PipelineContext(file_info=file_info)
    
    # 有images和chunks
    image = Image(page_num=0, url="u1", chunk_ids=None, dst_ids=None, image_type=ImageType.TABLE_IMAGE)
    ctx.handler_results[ConfHandlerName.screenshot_handler] = [image]
    chunk = _chunk("0", "content", [0], ["block1"], [])
    ctx.chunks = [chunk]

    node = ChunkPostProcessNode("chunk_postprocess")
    
    # 应该抛出异常
    with pytest.raises(Exception, match="Callback failed"):
        await node.process(ctx)


def test_chunk_postprocess_node_initialization():
    """测试ChunkPostProcessNode初始化"""
    node = ChunkPostProcessNode("test_name")
    assert node.name == "test_name"
    assert isinstance(node, ChunkPostProcessNode)

