# Author: linqi
# Date: 2025/8/15
# Time: 11:17

import pytest
from modules.flows.pre_check.pre_check_template import PreCheckTemplate
from modules.pipeline.context import PipelineContext


def test_pre_check_template_is_abstract():
    """Test that PreCheckTemplate is an abstract class"""
    import inspect
    assert inspect.isabstract(PreCheckTemplate)


def test_pre_check_template_abstract_method():
    """Test that PreCheckTemplate has the required abstract method"""
    assert hasattr(PreCheckTemplate, 'precheck_process')
    assert PreCheckTemplate.precheck_process.__name__ == 'precheck_process'


def test_pre_check_template_missing_implementation():
    """Test that trying to instantiate PreCheckTemplate raises TypeError"""
    with pytest.raises(TypeError, match="Can't instantiate abstract class"):
        PreCheckTemplate()


def test_concrete_implementation_works():
    """Test that a concrete implementation of PreCheckTemplate works"""
    class ConcretePreCheck(PreCheckTemplate):
        async def precheck_process(self, context: PipelineContext):
            return []
    
    # Should be able to instantiate
    instance = ConcretePreCheck()
    assert isinstance(instance, PreCheckTemplate)
    assert isinstance(instance, ConcretePreCheck)


def test_abstract_method_signature():
    """Test that the abstract method has the correct signature"""
    import inspect
    
    # Get the signature of the abstract method
    sig = inspect.signature(PreCheckTemplate.precheck_process)
    
    # Check that it takes self and context parameters
    params = list(sig.parameters.keys())
    assert len(params) == 2
    assert params[0] == 'self'
    assert params[1] == 'context'
    
    # Check that context parameter is typed as PipelineContext
    context_param = sig.parameters['context']
    assert context_param.annotation == PipelineContext