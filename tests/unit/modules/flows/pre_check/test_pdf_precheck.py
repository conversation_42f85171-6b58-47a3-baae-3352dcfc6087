# Author: linqi
# Date: 2025/8/22
# Time: 14:10

import pytest
import asyncio
from unittest.mock import <PERSON><PERSON>, <PERSON><PERSON>ock, AsyncMock, patch
from io import BytesIO
from PIL import Image
import fitz
import base64

from modules.flows.pre_check.pdf import (
    extract_and_upload_full_page_images,
    FileLoader,
    PageAnalyzer,
    AnalyzeResult,
    PDFPreCheck,
    scan_text_limit
)
from modules.entity.base import FileURL
from modules.entity.pre_check_entity import ParserConfig, ParserName
from modules.pipeline.context import PipelineContext, FileInfo, FileType
from commons.rpc.normal_model.rpc_interface import TableClsEnum


class TestExtractAndUploadFullPageImages:
    """测试 extract_and_upload_full_page_images 函数"""

    @pytest.mark.asyncio
    async def test_extract_and_upload_full_page_images_success(self):
        """测试成功提取和上传图片"""
        with patch('modules.flows.pre_check.pdf._upload_image') as mock_upload:
            mock_upload.return_value = "https://example.com/image.png"
            
            # 创建模拟的PDF文档
            mock_document = MagicMock()
            mock_document.page_count = 1
            
            # 创建模拟的页面
            mock_page = MagicMock()
            mock_page.number = 0
            mock_page.rect.width = 800
            mock_page.rect.height = 600
            mock_page.parent = mock_document
            
            # 模拟图片信息
            mock_page.get_images.return_value = [(123, 0, 800, 600, 8, "DeviceRGB", "", "Im1", "DCTDecode")]
            
            # 模拟图片数据
            # 创建一个简单的PNG图片
            img = Image.new('RGB', (800, 600), color='red')
            img_buffer = BytesIO()
            img.save(img_buffer, format='PNG')
            img_bytes = img_buffer.getvalue()
            
            mock_document.extract_image.return_value = {"image": img_bytes}
            mock_document.load_page.return_value = mock_page
            
            result = await extract_and_upload_full_page_images(mock_document)
            
            assert len(result) == 1
            assert result[0]["page_num"] == 0
            assert result[0]["image_url"] == "https://example.com/image.png"
            mock_upload.assert_called_once()

    @pytest.mark.asyncio
    async def test_extract_and_upload_full_page_images_exception(self):
        """测试异常处理"""
        mock_document = MagicMock()
        mock_document.page_count = 1
        mock_document.load_page.side_effect = Exception("PDF processing error")
        
        with patch('modules.flows.pre_check.pdf.logging') as mock_logging:
            result = await extract_and_upload_full_page_images(mock_document)
            
            assert result == []
            mock_logging.error.assert_called_once()


class TestFileLoader:
    """测试 FileLoader 类"""

    def test_file_loader_init(self):
        """测试 FileLoader 初始化"""
        loader = FileLoader("https://example.com/test.pdf", timeout=60)
        assert loader.url_or_bytes == "https://example.com/test.pdf"
        assert loader.timeout == 60
        assert loader.document is None

    @pytest.mark.asyncio
    async def test_load_document_from_bytes(self):
        """测试从字节数据加载文档"""
        with patch('modules.flows.pre_check.pdf.fitz.open') as mock_fitz_open:
            mock_document = MagicMock()
            mock_fitz_open.return_value = mock_document
            
            pdf_bytes = b"fake_pdf_content"
            loader = FileLoader(pdf_bytes)
            result = await loader.load_document()
            
            assert result == mock_document
            assert loader.document == mock_document
            mock_fitz_open.assert_called_once_with(stream=pdf_bytes, filetype="pdf")

    def test_get_page_count(self):
        """测试获取页数"""
        loader = FileLoader("test.pdf")
        mock_document = MagicMock()
        mock_document.page_count = 10
        loader.document = mock_document
        
        assert loader.get_page_count() == 10

    def test_close_document(self):
        """测试关闭文档"""
        loader = FileLoader("test.pdf")
        mock_document = MagicMock()
        loader.document = mock_document
        
        loader.close_document()
        
        mock_document.close.assert_called_once()
        assert loader.document is None


class TestAnalyzeResult:
    """测试 AnalyzeResult 类"""

    def test_analyze_result_default_values(self):
        """测试默认值"""
        result = AnalyzeResult()
        assert result.image_only_pages == []
        assert result.pages_with_tables == []
        assert result.full_text == []
        assert result.file_info is None


class TestPageAnalyzer:
    """测试 PageAnalyzer 类"""

    def create_mock_document(self):
        """创建模拟文档"""
        mock_document = MagicMock()
        mock_document.page_count = 3
        return mock_document

    def create_mock_page(self, page_num=0, width=800, height=600, rotation=0, text="Sample text", has_images=True, has_tables=False):
        """创建模拟页面"""
        mock_page = MagicMock()
        mock_page.number = page_num
        mock_page.rect.width = width
        mock_page.rect.height = height
        mock_page.rotation = rotation
        mock_page.get_text.return_value = text
        
        # 模拟图片
        if has_images:
            mock_page.get_images.return_value = [(123, 0, 100, 100, 8, "DeviceRGB", "", "Im1", "DCTDecode")]
        else:
            mock_page.get_images.return_value = []
        
        # 模拟表格
        mock_tables = MagicMock()
        if has_tables:
            mock_tables.tables = [MagicMock()]  # 至少一个表格
        else:
            mock_tables.tables = []
        mock_page.find_tables.return_value = mock_tables
        
        return mock_page

    def test_page_analyzer_init(self):
        """测试 PageAnalyzer 初始化"""
        mock_document = self.create_mock_document()
        analyzer = PageAnalyzer(mock_document)
        
        assert analyzer.document == mock_document
        assert isinstance(analyzer.analyze_result, AnalyzeResult)
        assert analyzer.analyze_result.file_info.file_type == FileType.PDF

    def test_analyze_success(self):
        """测试成功分析"""
        mock_document = self.create_mock_document()
        mock_page1 = self.create_mock_page(0, text="Page 1 text", has_tables=True)
        mock_page2 = self.create_mock_page(1, text="", has_images=True, has_tables=False)  # 只有图片的页面
        mock_page3 = self.create_mock_page(2, text="Page 3 text", has_images=False, has_tables=False)
        
        mock_document.load_page.side_effect = [mock_page1, mock_page2, mock_page3]
        
        analyzer = PageAnalyzer(mock_document)
        result = analyzer.analyze(0, 3)
        
        assert len(result.full_text) == 2  # 只有page1和page3有文本
        assert result.pages_with_tables == [0]  # 只有page1有表格
        assert result.image_only_pages == [1]  # 只有page2是纯图片页面
        assert result.file_info.page_size == 3


class TestPDFPreCheck:
    """测试 PDFPreCheck 类"""

    def create_mock_context(self):
        """创建模拟上下文"""
        context = MagicMock()
        context.kdc_input.file_url_or_bytes = "https://example.com/test.pdf"
        context.page_count = 100
        context.file_info = FileInfo(file_type=FileType.PDF)
        context.business_log = MagicMock()
        return context

    def create_mock_analyze_result(self, image_only_pages=None, pages_with_tables=None, full_text=None, file_page=3):
        """创建模拟分析结果"""
        if image_only_pages is None:
            image_only_pages = []
        if pages_with_tables is None:
            pages_with_tables = []
        if full_text is None:
            full_text = ["text1", "text2", "text3"]
        
        result = AnalyzeResult()
        result.image_only_pages = image_only_pages
        result.pages_with_tables = pages_with_tables
        result.full_text = full_text
        result.file_info = FileInfo(
            file_type=FileType.PDF,
            page_size=file_page,
            width=800,
            height=600,
            rotate_page={}
        )
        return result

    @pytest.mark.asyncio
    async def test_precheck_process_scan_document(self):
        """测试扫描文档处理"""
        with patch('modules.flows.pre_check.pdf.FileLoader') as mock_loader_class, \
             patch('modules.flows.pre_check.pdf.PageAnalyzer') as mock_analyzer_class:
            
            # 模拟FileLoader
            mock_loader = MagicMock()
            mock_document = MagicMock()
            mock_loader.load_document = AsyncMock(return_value=mock_document)
            mock_loader.get_page_count.return_value = 3
            mock_loader_class.return_value = mock_loader
            
            # 模拟PageAnalyzer - 全图片页面（扫描文档）
            mock_analyzer = MagicMock()
            analyze_result = self.create_mock_analyze_result(
                image_only_pages=[0, 1, 2],  # 全部都是图片页面
                full_text=["", "", ""]  # 没有文本
            )
            mock_analyzer.analyze.return_value = analyze_result
            mock_analyzer_class.return_value = mock_analyzer
            
            context = self.create_mock_context()
            precheck = PDFPreCheck()
            
            result = await precheck.precheck_process(context)
            
            assert len(result) == 1
            assert result[0].parser_name == ParserName.KdcParser
            assert result[0].is_all is True
            assert context.file_info.is_scan is True
            mock_loader.close_document.assert_called_once()

    @pytest.mark.asyncio
    async def test_precheck_process_mixed_content(self):
        """测试混合内容文档"""
        with patch('modules.flows.pre_check.pdf.FileLoader') as mock_loader_class, \
             patch('modules.flows.pre_check.pdf.PageAnalyzer') as mock_analyzer_class, \
             patch('modules.flows.pre_check.pdf.extract_and_upload_full_page_images') as mock_extract:
            
            # 模拟FileLoader
            mock_loader = MagicMock()
            mock_document = MagicMock()
            mock_loader.load_document = AsyncMock(return_value=mock_document)
            mock_loader.get_page_count.return_value = 3
            mock_loader_class.return_value = mock_loader
            
            # 模拟PageAnalyzer - 混合内容
            mock_analyzer = MagicMock()
            analyze_result = self.create_mock_analyze_result(
                image_only_pages=[0],  # 第一页是图片页
                pages_with_tables=[1],  # 第二页有表格
                full_text=["text"] * 30  # 足够的文本，避免被识别为扫描文档
            )
            mock_analyzer.analyze.return_value = analyze_result
            mock_analyzer_class.return_value = mock_analyzer
            
            # 模拟图片提取
            mock_extract.return_value = []
            
            context = self.create_mock_context()
            precheck = PDFPreCheck()
            
            result = await precheck.precheck_process(context)
            
            assert len(result) == 2  # KDC和MuPDF配置
            kdc_config = next(config for config in result if config.parser_name == ParserName.KdcParser)
            mupdf_config = next(config for config in result if config.parser_name == ParserName.MuPdfParser)
            
            assert kdc_config.is_all is False
            assert set(kdc_config.processing_pages) == {0, 1}  # 图片页和表格页
            assert mupdf_config.is_all is False
            assert 2 in mupdf_config.processing_pages  # 剩余页面

    @pytest.mark.asyncio
    async def test_precheck_process_only_mupdf_pages(self):
        """测试只有MuPDF页面的情况"""
        with patch('modules.flows.pre_check.pdf.FileLoader') as mock_loader_class, \
             patch('modules.flows.pre_check.pdf.PageAnalyzer') as mock_analyzer_class, \
             patch('modules.flows.pre_check.pdf.extract_and_upload_full_page_images') as mock_extract:
            
            # 模拟FileLoader
            mock_loader = MagicMock()
            mock_document = MagicMock()
            mock_loader.load_document = AsyncMock(return_value=mock_document)
            mock_loader.get_page_count.return_value = 3
            mock_loader_class.return_value = mock_loader
            
            # 模拟PageAnalyzer - 没有特殊页面
            mock_analyzer = MagicMock()
            analyze_result = self.create_mock_analyze_result(
                image_only_pages=[],  # 没有纯图片页
                pages_with_tables=[],  # 没有表格页
                full_text=["text"] * 30  # 足够的文本
            )
            mock_analyzer.analyze.return_value = analyze_result
            mock_analyzer_class.return_value = mock_analyzer
            
            # 模拟图片提取
            mock_extract.return_value = []
            
            context = self.create_mock_context()
            precheck = PDFPreCheck()
            
            result = await precheck.precheck_process(context)
            
            assert len(result) == 1
            assert result[0].parser_name == ParserName.MuPdfParser
            assert set(result[0].processing_pages) == {0, 1, 2}



    @pytest.mark.asyncio
    async def test_precheck_process_page_limit_exceeded(self):
        """测试页面数量超过限制"""
        with patch('modules.flows.pre_check.pdf.FileLoader') as mock_loader_class, \
             patch('modules.flows.pre_check.pdf.PageAnalyzer') as mock_analyzer_class:
            
            # 模拟FileLoader
            mock_loader = MagicMock()
            mock_document = MagicMock()
            mock_loader.load_document = AsyncMock(return_value=mock_document)
            mock_loader.get_page_count.return_value = 150  # 超过限制
            mock_loader_class.return_value = mock_loader
            
            # 模拟PageAnalyzer
            mock_analyzer = MagicMock()
            analyze_result = self.create_mock_analyze_result(file_page=150)
            mock_analyzer.analyze.return_value = analyze_result
            mock_analyzer_class.return_value = mock_analyzer
            
            context = self.create_mock_context()
            context.page_count = 100  # 设置限制为100页
            precheck = PDFPreCheck()
            
            with pytest.raises(ValueError, match="File page count .* exceeds limit"):
                await precheck.precheck_process(context)

    @pytest.mark.asyncio
    async def test_precheck_process_with_wired_table_classification(self):
        """测试带有wired表格分类的处理"""
        with patch('modules.flows.pre_check.pdf.FileLoader') as mock_loader_class, \
             patch('modules.flows.pre_check.pdf.PageAnalyzer') as mock_analyzer_class, \
             patch('modules.flows.pre_check.pdf.extract_and_upload_full_page_images') as mock_extract, \
             patch('modules.flows.pre_check.pdf.RpcFactory') as mock_rpc_factory:
            
            # 模拟FileLoader
            mock_loader = MagicMock()
            mock_document = MagicMock()
            mock_loader.load_document = AsyncMock(return_value=mock_document)
            mock_loader.get_page_count.return_value = 3
            mock_loader_class.return_value = mock_loader
            
            # 模拟PageAnalyzer
            mock_analyzer = MagicMock()
            analyze_result = self.create_mock_analyze_result(
                full_text=["text"] * 30  # 足够的文本
            )
            mock_analyzer.analyze.return_value = analyze_result
            mock_analyzer_class.return_value = mock_analyzer
            
            # 模拟图片提取
            mock_extract.return_value = [{"page_num": 1, "image_url": "https://example.com/img.png"}]
            
            # 模拟RPC调用
            mock_rpc = MagicMock()
            mock_response = MagicMock()
            mock_response.is_success.return_value = True
            mock_response.data.table_type = TableClsEnum.wired
            mock_rpc.arequest_table_cls = AsyncMock(return_value=mock_response)
            mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
            
            context = self.create_mock_context()
            precheck = PDFPreCheck()
            
            result = await precheck.precheck_process(context)
            
            assert len(result) == 2
            kdc_config = next(config for config in result if config.parser_name == ParserName.KdcParser)
            assert 1 in kdc_config.processing_pages  # wired表格页面被添加

    @pytest.mark.asyncio
    async def test_precheck_process_rpc_failure(self):
        """测试RPC调用失败"""
        with patch('modules.flows.pre_check.pdf.FileLoader') as mock_loader_class, \
             patch('modules.flows.pre_check.pdf.PageAnalyzer') as mock_analyzer_class, \
             patch('modules.flows.pre_check.pdf.extract_and_upload_full_page_images') as mock_extract, \
             patch('modules.flows.pre_check.pdf.RpcFactory') as mock_rpc_factory:
            
            # 模拟FileLoader
            mock_loader = MagicMock()
            mock_document = MagicMock()
            mock_loader.load_document = AsyncMock(return_value=mock_document)
            mock_loader.get_page_count.return_value = 3
            mock_loader_class.return_value = mock_loader
            
            # 模拟PageAnalyzer
            mock_analyzer = MagicMock()
            analyze_result = self.create_mock_analyze_result(
                full_text=["text"] * 30
            )
            mock_analyzer.analyze.return_value = analyze_result
            mock_analyzer_class.return_value = mock_analyzer
            
            # 模拟图片提取
            mock_extract.return_value = [{"page_num": 1, "image_url": "https://example.com/img.png"}]
            
            # 模拟RPC调用失败
            mock_rpc = MagicMock()
            mock_response = MagicMock()
            mock_response.is_success.return_value = False
            mock_rpc.arequest_table_cls = AsyncMock(return_value=mock_response)
            mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
            
            context = self.create_mock_context()
            precheck = PDFPreCheck()
            
            result = await precheck.precheck_process(context)
            
            # RPC失败不应该影响整体流程
            assert len(result) == 1  # 只有MuPDF配置，因为没有KDC页面
            assert result[0].parser_name == ParserName.MuPdfParser

    @pytest.mark.asyncio
    async def test_load_document_from_url_with_validation_error(self):
        """测试URL验证失败"""
        with patch('modules.entity.base.FileURL') as mock_file_url:
            mock_file_url_instance = MagicMock()
            mock_file_url_instance.validate.side_effect = ValueError("Invalid URL")
            mock_file_url.return_value = mock_file_url_instance
            
            loader = FileLoader("invalid_url")
            
            with pytest.raises(ValueError):
                await loader.load_document()

    @pytest.mark.asyncio
    async def test_load_document_from_url_success(self):
        """测试从URL成功加载文档"""
        with patch('modules.entity.base.FileURL') as mock_file_url, \
             patch('modules.flows.pre_check.pdf.aiohttp.ClientSession') as mock_session_class, \
             patch('modules.flows.pre_check.pdf.fitz.open') as mock_fitz_open:
            
            # 模拟FileURL验证
            mock_file_url_instance = MagicMock()
            mock_file_url_instance.validate.return_value = None
            mock_file_url.return_value = mock_file_url_instance
            
            # 模拟HTTP响应
            mock_response = AsyncMock()
            mock_response.read = AsyncMock(return_value=b"fake_pdf_content")
            mock_response.raise_for_status = MagicMock()
            
            # 正确模拟async context manager
            mock_session = MagicMock()
            mock_get_context = AsyncMock()
            mock_get_context.__aenter__ = AsyncMock(return_value=mock_response)
            mock_get_context.__aexit__ = AsyncMock(return_value=None)
            mock_session.get.return_value = mock_get_context
            
            mock_session_context = AsyncMock()
            mock_session_context.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session_context.__aexit__ = AsyncMock(return_value=None)
            mock_session_class.return_value = mock_session_context
            
            # 模拟fitz文档
            mock_document = MagicMock()
            mock_fitz_open.return_value = mock_document
            
            loader = FileLoader("https://example.com/test.pdf")
            result = await loader.load_document()
            
            assert result == mock_document
            assert loader.document == mock_document
            mock_fitz_open.assert_called_once()

    def test_analyze_with_rotation_90(self):
        """测试90度旋转的页面分析"""
        mock_document = MagicMock()
        mock_document.page_count = 1
        
        mock_page = MagicMock()
        mock_page.number = 0
        mock_page.rect.width = 800
        mock_page.rect.height = 600
        mock_page.rotation = 90
        mock_page.get_text.return_value = "Sample text"
        mock_page.get_images.return_value = []
        mock_tables = MagicMock()
        mock_tables.tables = []
        mock_page.find_tables.return_value = mock_tables
        
        mock_document.load_page.return_value = mock_page
        
        analyzer = PageAnalyzer(mock_document)
        result = analyzer.analyze(0, 1)
        
        # 旋转90度后，宽高应该交换
        assert result.file_info.width == 600  # 原height
        assert result.file_info.height == 800  # 原width
        assert 90 in result.file_info.rotate_page
        assert result.file_info.rotate_page[90] == [0]

    def test_analyze_with_rotation_180(self):
        """测试180度旋转的页面分析"""
        mock_document = MagicMock()
        mock_document.page_count = 1
        
        mock_page = MagicMock()
        mock_page.number = 0
        mock_page.rect.width = 800
        mock_page.rect.height = 600
        mock_page.rotation = 180
        mock_page.get_text.return_value = "Sample text"
        mock_page.get_images.return_value = []
        mock_tables = MagicMock()
        mock_tables.tables = []
        mock_page.find_tables.return_value = mock_tables
        
        mock_document.load_page.return_value = mock_page
        
        analyzer = PageAnalyzer(mock_document)
        result = analyzer.analyze(0, 1)
        
        # 180度旋转，宽高不变
        assert result.file_info.width == 800
        assert result.file_info.height == 600
        assert 180 in result.file_info.rotate_page
        assert result.file_info.rotate_page[180] == [0]

    def test_analyze_page_with_exception(self):
        """测试页面分析中的异常处理"""
        mock_document = MagicMock()
        mock_document.page_count = 1
        
        mock_page = MagicMock()
        mock_page.get_text.side_effect = Exception("Text extraction error")
        
        mock_document.load_page.return_value = mock_page
        
        analyzer = PageAnalyzer(mock_document)
        
        with pytest.raises(Exception):
            analyzer.analyze(0, 1)

    def test_analyze_with_none_end_page(self):
        """测试analyze方法中end_page为None的情况"""
        mock_document = MagicMock()
        mock_document.page_count = 2
        
        mock_page1 = MagicMock()
        mock_page1.number = 0
        mock_page1.rect.width = 800
        mock_page1.rect.height = 600
        mock_page1.rotation = 0
        mock_page1.get_text.return_value = "Page 1"
        mock_page1.get_images.return_value = []
        mock_tables1 = MagicMock()
        mock_tables1.tables = []
        mock_page1.find_tables.return_value = mock_tables1
        
        mock_page2 = MagicMock()
        mock_page2.number = 1
        mock_page2.rect.width = 800
        mock_page2.rect.height = 600
        mock_page2.rotation = 0
        mock_page2.get_text.return_value = "Page 2"
        mock_page2.get_images.return_value = []
        mock_tables2 = MagicMock()
        mock_tables2.tables = []
        mock_page2.find_tables.return_value = mock_tables2
        
        mock_document.load_page.side_effect = [mock_page1, mock_page2]
        
        analyzer = PageAnalyzer(mock_document)
        result = analyzer.analyze()  # 不传end_page参数
        
        assert len(result.full_text) == 2
        assert result.file_info.page_size == 2

    def test_analyze_with_exception_in_analyze_method(self):
        """测试analyze方法中的异常处理"""
        mock_document = MagicMock()
        mock_document.page_count = 1
        mock_document.load_page.side_effect = Exception("Page loading error")
        
        analyzer = PageAnalyzer(mock_document)
        
        with pytest.raises(Exception):
            analyzer.analyze(0, 1)

    def test_pages_result(self):
        """测试pages_result方法"""
        mock_document = MagicMock()
        analyzer = PageAnalyzer(mock_document)
        
        result = analyzer.pages_result()
        assert isinstance(result, AnalyzeResult)
        assert result == analyzer.analyze_result





    @pytest.mark.asyncio
    async def test_precheck_process_low_text_content(self):
        """测试文本内容过少的情况"""
        with patch('modules.flows.pre_check.pdf.FileLoader') as mock_loader_class, \
             patch('modules.flows.pre_check.pdf.PageAnalyzer') as mock_analyzer_class:
            
            # 模拟FileLoader
            mock_loader = MagicMock()
            mock_document = MagicMock()
            mock_loader.load_document = AsyncMock(return_value=mock_document)
            mock_loader.get_page_count.return_value = 10
            mock_loader_class.return_value = mock_loader
            
            # 模拟PageAnalyzer - 文本内容过少
            mock_analyzer = MagicMock()
            analyze_result = self.create_mock_analyze_result(
                full_text=["short"] * 5,  # 5个文本，10页，平均0.5 < scan_text_limit(10)
                file_page=10
            )
            mock_analyzer.analyze.return_value = analyze_result
            mock_analyzer_class.return_value = mock_analyzer
            
            context = self.create_mock_context()
            precheck = PDFPreCheck()
            
            result = await precheck.precheck_process(context)
            
            assert len(result) == 1
            assert result[0].parser_name == ParserName.KdcParser
            assert result[0].is_all is True  # 文本过少，当作扫描文档处理
            assert context.file_info.is_scan is True