# Author: linqi
# Date: 2025/8/12
# Time: 18:53

import asyncio
import pytest
from unittest.mock import MagicMock, AsyncMock

from modules.flows.chunk.chunk_handler import (
    _chunk_content_embedding_task,
    ChunkNode,
    ChunkFactory,
)
from modules.pipeline.context import PipelineContext, FileInfo, ChunkInfo, FileType
from modules.entity.chunk_entity import Chunk, LabelType





class FakeMultiCoroutine:
    def __init__(self):
        self.tasks = {}

    def add_task(self, key, coro):
        self.tasks[key] = coro

    async def run_limit(self, limit: int):
        results = {}
        for k, c in self.tasks.items():
            results[k] = await c
        return results

    async def run(self):
        results = {}
        for k, c in self.tasks.items():
            results[k] = await c
        return results


class DummyChunkProcessor:
    def process_chunks(self, dst_list, page_size, chunks_info):
        return [
            Chunk(
                chunk_id="x",
                page_size=page_size or 1,
                content="Hello <b>world</b>",
                label=LabelType.TEXT,
                page_num=[0],
                block=["b1"],
            )
        ]

    def merge_chunk(self, chunk_size, chunks):
        return chunks

    def renumber_chunk_ids(self, chunks):
        if chunks is None:
            return None
        for i, c in enumerate(chunks):
            c.chunk_id = str(i)
        return chunks


@pytest.mark.asyncio
async def test_chunk_content_embedding_task_cleans_html_and_sets_embeddings(monkeypatch):
    from modules.flows.chunk import chunk_handler as ch

    # Mock RpcFactory and its get_normal_model_rpc method
    mock_rpc_factory = MagicMock()
    mock_normal_model_rpc = AsyncMock()
    mock_rpc_factory.get_normal_model_rpc.return_value = mock_normal_model_rpc
    
    # Mock the embedding response
    mock_response = MagicMock()
    mock_response.is_success.return_value = True
    mock_response.data.embedding = [1]  # For table chunk
    mock_normal_model_rpc.arequest_text_embedding.return_value = mock_response
    
    monkeypatch.setattr(ch, "RpcFactory", lambda: mock_rpc_factory)

    # prepare chunks: one table with html, one text
    table_chunk = Chunk(
        chunk_id="t1",
        page_size=1,
        content="<table><tr> X </tr></table>",
        label=LabelType.TABLE,
        page_num=[0],
        block=[],
    )
    text_chunk = Chunk(
        chunk_id="t2",
        page_size=1,
        content="abc",
        label=LabelType.TEXT,
        page_num=[0],
        block=[],
    )

    await _chunk_content_embedding_task([table_chunk, text_chunk])

    # After cleaning, "<table><tr> X </tr></table>" -> "X"
    assert table_chunk.content_embedding == [1]
    assert text_chunk.content_embedding == [1]  # Both get the same mock embedding


@pytest.mark.asyncio
async def test_chunk_node_process_happy_path(monkeypatch):
    # Patch MultiCoroutine to synchronous fake
    import modules.flows.chunk.chunk_handler as ch
    monkeypatch.setattr(ch, "MultiCoroutine", FakeMultiCoroutine)
    
    # Mock RpcFactory and its get_normal_model_rpc method
    mock_rpc_factory = MagicMock()
    mock_normal_model_rpc = AsyncMock()
    mock_rpc_factory.get_normal_model_rpc.return_value = mock_normal_model_rpc
    
    # Mock the embedding response
    mock_response = MagicMock()
    mock_response.is_success.return_value = True
    mock_response.data.embedding = [1]
    mock_normal_model_rpc.arequest_text_embedding.return_value = mock_response
    
    monkeypatch.setattr(ch, "RpcFactory", lambda: mock_rpc_factory)

    # Patch TableProcessorContext to passthrough
    class DummyTPC:
        def __init__(self, *_args, **_kwargs):
            pass

        def process_chunks(self, chunks):
            return chunks

    monkeypatch.setattr(ch, "TableProcessorContext", DummyTPC)
    monkeypatch.setattr(ch, "TitleProcessor", lambda: None)

    # Patch ChunkFactory to return our dummy processor
    monkeypatch.setattr(ChunkFactory, "get_chunk_processor", staticmethod(lambda _ft: DummyChunkProcessor()))

    # Build context
    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, page_size=2),
        chunks_info=ChunkInfo(chunk_size=100),
        embed_enabled=True,
        dst=[],  # 添加空的dst列表
    )

    node = ChunkNode("chunk")
    out_ctx = await node.process(ctx)

    # chunks produced and renumbered
    assert out_ctx.chunks and out_ctx.chunks[0].chunk_id == "0"
    # embeddings set by background task
    assert isinstance(out_ctx.chunks[0].content_embedding, list)
    # handler_results populated
    assert node.name in out_ctx.handler_results


def test_chunk_factory_raises_on_unknown_type(monkeypatch):
    # Temporarily clear mapping
    from modules.flows.chunk import chunk_handler as ch
    original = ch.ChunkFactory._chunk_processors.copy()
    try:
        ch.ChunkFactory._chunk_processors.clear()
        with pytest.raises(ValueError, match="No parser available for file type"):
            ChunkFactory.get_chunk_processor(FileType.PDF)
    finally:
        ch.ChunkFactory._chunk_processors = original

def test_chunk_factory_returns_processor_for_known_type():
    """测试ChunkFactory为已知文件类型返回处理器"""
    processor = ChunkFactory.get_chunk_processor(FileType.PDF)
    assert processor is not None


@pytest.mark.asyncio
async def test_chunk_node_process_propagates_exceptions(monkeypatch):
    import modules.flows.chunk.chunk_handler as ch

    class BadProcessor(DummyChunkProcessor):
        def process_chunks(self, *_args, **_kwargs):
            raise RuntimeError("boom")

    # Patch dependencies to minimal
    monkeypatch.setattr(ch, "MultiCoroutine", FakeMultiCoroutine)
    monkeypatch.setattr(ChunkFactory, "get_chunk_processor", staticmethod(lambda _ft: BadProcessor()))
    monkeypatch.setattr(ch, "TableProcessorContext", lambda *a, **k: None)
    monkeypatch.setattr(ch, "TitleProcessor", lambda: None)

    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, page_size=1),
        chunks_info=ChunkInfo(chunk_size=100),
        embed_enabled=False,
        dst=[],  # 添加空的dst列表
    )

    node = ChunkNode("chunk")
    with pytest.raises(RuntimeError):
        await node.process(ctx)

@pytest.mark.asyncio
async def test_chunk_node_process_with_otl_file_type(monkeypatch):
    """测试ChunkNode处理OTL文件类型的情况"""
    import modules.flows.chunk.chunk_handler as ch
    monkeypatch.setattr(ch, "MultiCoroutine", FakeMultiCoroutine)
    
    # Mock RpcFactory and its get_normal_model_rpc method
    mock_rpc_factory = MagicMock()
    mock_normal_model_rpc = AsyncMock()
    mock_rpc_factory.get_normal_model_rpc.return_value = mock_normal_model_rpc
    
    # Mock the embedding response
    mock_response = MagicMock()
    mock_response.is_success.return_value = True
    mock_response.data.embedding = [1]
    mock_normal_model_rpc.arequest_text_embedding.return_value = mock_response
    
    monkeypatch.setattr(ch, "RpcFactory", lambda: mock_rpc_factory)

    class DummyTPC:
        def __init__(self, *_args, **_kwargs):
            pass
        def process_chunks(self, chunks):
            return chunks

    monkeypatch.setattr(ch, "TableProcessorContext", DummyTPC)
    monkeypatch.setattr(ch, "TitleProcessor", lambda: None)

    class OTLChunkProcessor(DummyChunkProcessor):
        def process_chunks(self, dst_list, page_size, chunks_info):
            return [
                Chunk(
                    chunk_id="x",
                    page_size=5,  # 设置不同的page_size
                    content="OTL content",
                    label=LabelType.TEXT,
                    page_num=[0],
                    block=["b1"],
                )
            ]

    monkeypatch.setattr(ChunkFactory, "get_chunk_processor", staticmethod(lambda _ft: OTLChunkProcessor()))

    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.OTL, page_size=2),
        chunks_info=ChunkInfo(chunk_size=100),
        embed_enabled=True,
        dst=[],
    )

    node = ChunkNode("chunk")
    out_ctx = await node.process(ctx)

    # 验证OTL文件类型的page_size被更新
    assert out_ctx.file_info.page_size == 5
    assert out_ctx.chunks and len(out_ctx.chunks) > 0

@pytest.mark.asyncio
async def test_chunk_node_process_with_none_chunks(monkeypatch):
    """测试ChunkNode处理返回None chunks的情况"""
    import modules.flows.chunk.chunk_handler as ch
    monkeypatch.setattr(ch, "MultiCoroutine", FakeMultiCoroutine)

    class DummyTPC:
        def __init__(self, *_args, **_kwargs):
            pass
        def process_chunks(self, chunks):
            return chunks

    monkeypatch.setattr(ch, "TableProcessorContext", DummyTPC)
    monkeypatch.setattr(ch, "TitleProcessor", lambda: None)

    class NoneChunkProcessor(DummyChunkProcessor):
        def process_chunks(self, dst_list, page_size, chunks_info):
            return None  # 返回None

    monkeypatch.setattr(ChunkFactory, "get_chunk_processor", staticmethod(lambda _ft: NoneChunkProcessor()))

    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, page_size=2),
        chunks_info=ChunkInfo(chunk_size=100),
        embed_enabled=True,
        dst=[],
    )

    node = ChunkNode("chunk")
    with pytest.raises(ValueError, match="Failed to generate chunks"):
        await node.process(ctx)

@pytest.mark.asyncio
async def test_chunk_task_with_empty_chunks(monkeypatch):
    """测试_chunk_task函数处理空chunks的情况"""
    import modules.flows.chunk.chunk_handler as ch
    monkeypatch.setattr(ch, "MultiCoroutine", FakeMultiCoroutine)

    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, page_size=2, file_id="test_file"),
        chunks_info=ChunkInfo(chunk_size=100),
        embed_enabled=True,
        chunks=[],  # 空chunks
    )

    result = await ch._chunk_task(ctx)
    assert result == []

@pytest.mark.asyncio
async def test_chunk_task_with_exception_in_pool(monkeypatch):
    """测试_chunk_task函数处理pool任务异常的情况"""
    import modules.flows.chunk.chunk_handler as ch

    class ExceptionMultiCoroutine:
        def __init__(self):
            self.tasks = {}

        def add_task(self, key, coro):
            # 正确处理协程对象，避免RuntimeWarning
            if asyncio.iscoroutine(coro):
                # 关闭协程以避免警告
                coro.close()
            self.tasks[key] = coro

        async def run_limit(self, limit: int):
            return {"pool2_chunk_content_embedding_key_0": RuntimeError("Pool task failed")}

    monkeypatch.setattr(ch, "MultiCoroutine", ExceptionMultiCoroutine)

    ctx = PipelineContext(
        file_info=FileInfo(file_type=FileType.PDF, page_size=2),
        chunks_info=ChunkInfo(chunk_size=100),
        embed_enabled=True,
        chunks=[
            Chunk(
                chunk_id="x",
                page_size=1,
                content="test content",
                label=LabelType.TEXT,
                page_num=[0],
                block=["b1"],
            )
        ],
    )

    result = await ch._chunk_task(ctx)
    assert result == ctx.chunks
