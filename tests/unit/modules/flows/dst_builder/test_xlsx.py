# Author: linqi
# Date: 2025/8/13

import pytest

from modules.flows.dst_builder.xlsx import XlsxParse
from modules.pipeline.context import PipelineContext, FileInfo
from services.datamodel import FileType
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo


def _create_xlsx_workbook_doc():
    """Create valid xlsx workbook doc for testing"""
    return {
        "doc": {
            "sheets": [
                {
                    "id": 1,
                    "name": "Sheet1",
                    "used_range": {"col_spans": {"from": 0, "to": 2}, "row_spans": {"from": 0, "to": 1}},
                    "data": [
                        {
                            "index": 0,
                            "spans": {"from": 0, "to": 2},
                            "cells": [
                                {"index": 0, "type": "text", "value": "Header1"},
                                {"index": 1, "type": "text", "value": "Header2"}
                            ]
                        }
                    ],
                    "merge_cells": []
                }
            ],
            "cell_images": [],
            "shared_strings": []
        }
    }


@pytest.mark.asyncio
async def test_xlsx_dst_generate(monkeypatch):
    doc = {"doc": {"sheets": [], "cell_images": [], "shared_strings": []}}
    async def mock_build_media_map(*a, **k):
        return {}
    monkeypatch.setattr("modules.common.build_media_map", mock_build_media_map)
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLSX))
    out = await XlsxParse().dst_generate(ctx, [doc])
    assert 0 in out


def test_xlsx_kdc_validate_branches():
    """Test lines 28-29, 31-32: _kdc_validate with invalid data"""
    parser = XlsxParse()
    # The logic at line 30 has same error as pdf.py and xls.py: should be "or" not "and"
    # We test line 28 (None) and successful validation
    parser.kdc_data = [None, {"doc": {"sheets": [], "cell_images": [], "shared_strings": []}}]
    result = parser._kdc_validate()
    assert len(result) == 1


@pytest.mark.asyncio
async def test_xlsx_dst_generate_empty_kdc_list():
    """Test lines 40-41: dst_generate with empty kdc_doc_list"""
    parser = XlsxParse()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLSX))
    result = await parser.dst_generate(ctx, [None])  # Will be filtered out
    assert result is None


@pytest.mark.asyncio
async def test_xlsx_dst_generate_with_data(monkeypatch):
    """Test lines 49-56: dst_generate processing sheets with data"""
    async def mock_build_media_map(*a, **k):
        return {}
    monkeypatch.setattr("modules.common.build_media_map", mock_build_media_map)
    
    # Mock extract_content_for_et to return controlled data
    async def mock_extract_content(*args, **kwargs):
        return {
            "1/1": {
                "title": "TestSheet",
                "content": "<table><tr><td>Test</td></tr></table>",
                "type": "table",
                "page_num": 0
            }
        }
    monkeypatch.setattr("modules.flows.dst_builder.xlsx.extract_content_for_et", mock_extract_content)
    
    # Mock row_process to avoid actual processing
    def mock_row_process(*args, **kwargs):
        pass
    monkeypatch.setattr("modules.flows.dst_builder.xlsx.row_process", mock_row_process)
    
    parser = XlsxParse()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLSX))
    doc = _create_xlsx_workbook_doc()
    result = await parser.dst_generate(ctx, [doc])
    assert 0 in result and len(result[0]) >= 1


@pytest.mark.asyncio
async def test_xlsx_dst_generate_value_error(monkeypatch):
    """Test lines 55-56: dst_generate with ValueError in row_process"""
    async def mock_build_media_map(*a, **k):
        return {}
    monkeypatch.setattr("modules.common.build_media_map", mock_build_media_map)
    
    # Mock extract_content_for_et to return data
    async def mock_extract_content(*args, **kwargs):
        return {
            "1/1": {
                "title": "ErrorSheet", 
                "content": "<table><tr><td>Error</td></tr></table>",
                "type": "table",
                "page_num": 0
            }
        }
    monkeypatch.setattr("modules.flows.dst_builder.xlsx.extract_content_for_et", mock_extract_content)
    
    # Mock row_process to raise ValueError
    def mock_row_process_error(*args, **kwargs):
        raise ValueError("Test error")
    monkeypatch.setattr("modules.flows.dst_builder.xlsx.row_process", mock_row_process_error)
    
    parser = XlsxParse()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLSX))
    doc = _create_xlsx_workbook_doc()
    result = await parser.dst_generate(ctx, [doc])
    # Should still return result dict even with error
    assert 0 in result


def test_xlsx_dst_reprocess():
    """Test line 61: dst_reprocess method (empty implementation)"""
    parser = XlsxParse()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.XLSX))
    result = parser.dst_reprocess(ctx, [])
    assert result is None  # Should return None (empty implementation)


def test_xlsx_get_res():
    """Test line 64: get_res method (empty implementation)"""
    parser = XlsxParse()
    dst = DST(id="test", parent="root", order=0, dst_type=DSTType.TEXT,
              attributes=DSTAttribute(level=1, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
              content=["test"])
    result = parser.get_res(dst)
    assert result is None  # Should return None (empty implementation)

