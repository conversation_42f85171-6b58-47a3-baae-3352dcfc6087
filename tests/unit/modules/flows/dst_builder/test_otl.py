# Author: linqi
# Date: 2025/8/13
# Time: 19:55

import pytest

@pytest.mark.asyncio
async def test_otl_traverse_covers_all(monkeypatch):
    # dummy processors for otl.traverse
    class D:
        def __init__(self, *a, **k): pass
        def process(self, *a, **k): return "nid"
    class AD:
        def __init__(self,*a,**k): pass
        async def process(self, *a, **k): return "nid"
    monkeypatch.setattr("modules.flows.dst_builder.otl.ParaNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.TextboxNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.ComponentNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.HighLightNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.TableNode", AD)

    from modules.flows.dst_builder.otl import traverse
    from modules.entity.kdc_enttiy import Node, Block, BlockType
    from modules.pipeline.context import Pipeline<PERSON>ontext, FileInfo
    from services.datamodel import FileType
    # build node with all block types and a child
    blocks = [
        Block(type=BlockType.para),
        Block(type=BlockType.table),
        Block(type=BlockType.textbox),
        Block(type=BlockType.component),
        Block(type=BlockType.highlight),
    ]
    child = Node(blocks=[], children=[])
    root = Node(blocks=blocks, children=[child], outline_level=1)
    ls = []
    context = PipelineContext(file_info=FileInfo(file_type=FileType.OTL))
    # first with empty id2url so component path checks continue condition inside traverse
    await traverse(context, {}, {}, root, parent_id="p", ls=ls)
    # then with non-empty id2url to execute component branch fully
    await traverse(context, {"any": ["u", [1,1]]}, {}, root, parent_id="p", ls=ls)
    assert isinstance(ls, list)


@pytest.mark.asyncio
async def test_otl_dst_generate_minimal(monkeypatch):
    from modules.flows.dst_builder.otl import OTLParse
    # stub node processors (used by traverse)
    class D:
        def __init__(self,*a,**k): pass
        def process(self,*a,**k): return "nid"
    class AD:
        def __init__(self,*a,**k): pass
        async def process(self,*a,**k): return "nid"
    monkeypatch.setattr("modules.flows.dst_builder.otl.ParaNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.TextboxNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.ComponentNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.HighLightNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.TableNode", AD)
    # stub common helpers
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", lambda: type("D", (), {"id": "root"})())
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    # minimal valid kdc doc
    doc = {"doc": {"tree": {"blocks": []}, "medias": [], "hyperlinks": []}}
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.OTL))
    out = await OTLParse().dst_generate(ctx, [doc])
    assert 0 in out