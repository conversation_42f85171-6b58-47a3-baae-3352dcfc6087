# Author: linqi
# Date: 2025/8/13

import pytest

from modules.flows.dst_builder.pdf import PdfParse
from modules.pipeline.context import PipelineContext, FileInfo
from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
from services.datamodel import FileType


def _dst_root():
    return DST(id="root", parent="-1", order=0, dst_type=DSTType.ROOT,
               attributes=DSTAttribute(level=0, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
               content=["root"]) 


def test_pdf_dst_reprocess(monkeypatch):
    from modules.flows.dst_builder.pdf import PdfParse
    monkeypatch.setattr("modules.flows.filters.catalog_filter.get_catalog_pages", lambda lst: -1)
    # Return non-empty temp list to cover optional branch
    monkeypatch.setattr("modules.layout.typesetting.typesetting_correct", lambda root, page_dst: ([root], "top-bottom"))
    monkeypatch.setattr("modules.entity.dst_entity.get_page_dst", lambda lst: {0: lst})
    monkeypatch.setattr("modules.flows.filters.filter.dst_mark", lambda lst, page_dst, fi: lst)
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.analyze_format", lambda outline: ({}, False))
    monkeypatch.setattr("modules.layout.outline.is_main_text", lambda dst, size: False)
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.validate_against_groups", lambda groups, content, match_prefix=True: [])
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.add_to_group", lambda groups, did, content, prefix: None)
    monkeypatch.setattr("modules.layout.outline.add_parent", lambda lst: lst)
    monkeypatch.setattr("modules.entity.dst_entity.assign_order_to_dst", lambda lst: lst)
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    dsts = [_dst_root(), DST(id="t", parent="root", order=0, dst_type=DSTType.TEXT,
               attributes=DSTAttribute(level=10, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
               content=["text"]) ]
    out = PdfParse().dst_reprocess(ctx, dsts)
    assert ctx.layout == "top-bottom" and out == dsts


@pytest.mark.asyncio
async def test_pdf_dst_generate_validation_and_extract(monkeypatch):
    # Build minimal valid pdf kdc data
    doc = {"doc": {"tree": {"blocks": []}, "medias": [], "hyperlinks": []}}
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    # stub media map and hyperlink
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    # stub _extract_kdc_node to avoid deep logic (must be async since original is awaited)
    async def _dummy_extract(self, *args, **kwargs):
        return None
    monkeypatch.setattr(PdfParse, "_extract_kdc_node", _dummy_extract)
    out = await PdfParse().dst_generate(ctx, [doc])
    assert out is not None and 0 in out


def test_pdf_get_outline_executes():
    pp = PdfParse()
    dsts = [
        DST(id="r", parent="-1", order=0, dst_type=DSTType.ROOT,
            attributes=DSTAttribute(level=0, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
            content=["root"]) ,
        DST(id="t1", parent="r", order=1, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=1, position=PositionInfo(bbox=BBox(x1=1,y1=1,x2=2,y2=2)), page=0, hash="h"*32),
            content=["Title"]) ,
    ]
    ol = pp.get_outline(dsts)
    assert isinstance(ol, list)


def test_pdf_dst_reprocess_outline_flow(monkeypatch):
    from modules.flows.dst_builder.pdf import PdfParse
    # Stub functions to force outline processing path
    monkeypatch.setattr("modules.flows.filters.catalog_filter.get_catalog_pages", lambda lst: -1)
    monkeypatch.setattr("modules.entity.dst_entity.get_page_dst", lambda lst: {0: lst})
    monkeypatch.setattr("modules.flows.filters.filter.dst_mark", lambda lst, page_dst, fi: lst)
    # contains_chinese True to increment chinese_count
    monkeypatch.setattr("modules.layout.common.contains_chinese", lambda s: True)
    # analyze_format returns (groups, True) to enable is_common_patten path
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.analyze_format", lambda outline: ({}, True))
    # validate returns non-empty so add_to_group called
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.validate_against_groups", lambda g, c, match_prefix=True: [("P", None)])
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.add_to_group", lambda g, did, c, p: None)
    # is_main_text False so we won't skip
    monkeypatch.setattr("modules.layout.outline.is_main_text", lambda dst, size: False)
    # assign calls
    monkeypatch.setattr("modules.layout.outline.add_parent", lambda lst: lst)
    monkeypatch.setattr("modules.entity.dst_entity.assign_order_to_dst", lambda lst: lst)
    # typesetting_correct to set layout
    monkeypatch.setattr("modules.layout.typesetting.typesetting_correct", lambda root, page_dst: (None, "layout2"))

    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF, rotate_page={"90": [0]}))
    # Build dsts with two text nodes to exercise outline matching and font_size counting
    dsts = [
        DST(id="r", parent="-1", order=0, dst_type=DSTType.ROOT,
            attributes=DSTAttribute(level=0, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
            content=["root"]) ,
        DST(id="h1", parent="r", order=1, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=1, position=PositionInfo(bbox=BBox(x1=2,y1=1,x2=10,y2=2)), page=1, hash="h"*32),
            content=["A "]) ,
        DST(id="p1", parent="h1", order=2, dst_type=DSTType.TEXT,
            attributes=DSTAttribute(level=10, position=PositionInfo(bbox=BBox(x1=2,y1=1,x2=10,y2=2)), page=1, hash="h"*32),
            content=["Paragraph "], font_size=12) ,
    ]
    out = PdfParse().dst_reprocess(ctx, dsts)
    assert ctx.layout == "top-bottom" and isinstance(out, list)


def test_pdf_kdc_validate_branches():
    pp = PdfParse()
    # Due to logic error at line 38 (should be "or" not "and"), we can only test valid case
    # Line 36: None triggers continue, Line 41: valid doc gets validated
    pp.kdc_data = [None, {"doc": {"tree": {"blocks": []}, "medias": [], "hyperlinks": []}}]
    lst = pp._kdc_validate()
    assert len(lst) == 1


@pytest.mark.asyncio
async def test_pdf_extract_node_block_branches(monkeypatch):
    from modules.entity.kdc_enttiy import Block, BlockType, Component, ComponentType, TextBox, Table, Para
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    calls = {"c":0, "t":0, "tb":0, "p":0}
    
    # Mock the classes as they are imported in pdf.py module
    # We need to mock them in the pdf module namespace since that's where they're used
    def mock_component_node_init(block, media_id2url, id2text):
        class MockInstance:
            def process(self, context, parent_outline_level, parent_id, index, dst_list):
                calls["c"] += 1
                return "comp_id"
        return MockInstance()
    
    def mock_textbox_node_init(block, media_id2url, id2text):
        class MockInstance:
            def process(self, context, parent_outline_level, parent_id, index, dst_list):
                calls["tb"] += 1
                return "tb_id"
        return MockInstance()
    
    def mock_table_node_init(block, media_id2url, id2text):
        class MockInstance:
            async def process(self, context, parent_outline_level, parent_id, index, dst_list):
                calls["t"] += 1
                return "tbl_id"
        return MockInstance()
    
    def mock_para_node_init(block, media_id2url, id2text):
        class MockInstance:
            def process(self, context, parent_outline_level, parent_id, index, dst_list):
                calls["p"] += 1
                return "para_id"
        return MockInstance()
    
    # Mock the imported classes in the pdf module namespace
    monkeypatch.setattr("modules.flows.dst_builder.pdf.ComponentNode", mock_component_node_init)
    monkeypatch.setattr("modules.flows.dst_builder.pdf.TextboxNode", mock_textbox_node_init)
    monkeypatch.setattr("modules.flows.dst_builder.pdf.TableNode", mock_table_node_init)
    monkeypatch.setattr("modules.flows.dst_builder.pdf.ParaNode", mock_para_node_init)
    
    pp = PdfParse()
    dst_list = []
    id2 = {}
    context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    # component with image type (only image type triggers ComponentNode)
    blk_c = Block(type=BlockType.component, component=Component(type=ComponentType.image, media_id="m1"))
    result_c = await pp._extract_node_block(context, blk_c, 1, 0, "pid", {}, dst_list, id2)
    # textbox
    blk_tb = Block(type=BlockType.textbox, textbox=TextBox(blocks=[]))
    result_tb = await pp._extract_node_block(context, blk_tb, 1, 1, "pid", {}, dst_list, id2)
    # table
    blk_t = Block(type=BlockType.table, table=Table(rows=[]))
    result_t = await pp._extract_node_block(context, blk_t, 1, 2, "pid", {}, dst_list, id2)
    # para
    blk_p = Block(type=BlockType.para, para=Para(runs=[]))
    result_p = await pp._extract_node_block(context, blk_p, 1, 3, "pid", {}, dst_list, id2)
    
    assert calls["c"]==1 and calls["tb"]==1 and calls["t"]==1 and calls["p"]==1


@pytest.mark.asyncio
async def test_pdf_extract_kdc_node_flow(monkeypatch):
    pp = PdfParse()
    call_count = 0
    context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    # stub _extract_node_block: first call adds DST and returns new id, second returns parent id
    async def stub(self, context, block, parent_outline_level, parent_id, index, media, dst_list, id2text):
        nonlocal call_count
        call_count += 1
        if call_count == 1:  # first block call
            dst_list.append(_dst_root())
            return "new_id"
        return parent_id  # subsequent calls return parent_id
    monkeypatch.setattr(PdfParse, "_extract_node_block", stub)
    from modules.entity.kdc_enttiy import Node, Block, BlockType
    node = Node(outline_level=1, blocks=[Block(type=BlockType.para), Block(type=BlockType.para)], children=[Node(blocks=[], children=[])])
    dst_list = []
    await pp._extract_kdc_node(context, node, "pid", {}, dst_list, {})
    # Should have 1 DST from first block call
    assert len(dst_list) == 1


def test_pdf_get_res_prints(capsys):
    pp = PdfParse()
    pp.get_res([_dst_root()])
    captured = capsys.readouterr()
    assert isinstance(captured.out, str)


@pytest.mark.asyncio
async def test_pdf_extract_node_block_component_not_image():
    """Test line 56-57: component exists but not ComponentType.image"""
    from modules.entity.kdc_enttiy import Block, BlockType, Component, ComponentType
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    pp = PdfParse()
    dst_list = []
    context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    # component exists but type is not image - should not enter if block (line 56-57 not executed)
    blk = Block(type=BlockType.component, component=Component(type=ComponentType.chart, media_id="m1"))
    result = await pp._extract_node_block(context, blk, 1, 0, "pid", {}, dst_list, {})
    assert result == "pid"  # dst_id should remain parent_id


@pytest.mark.asyncio
async def test_pdf_extract_node_block_component_none():
    """Test line 56: component is None"""
    from modules.entity.kdc_enttiy import Block, BlockType
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    pp = PdfParse()
    dst_list = []
    context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    # component is None - should not enter if block
    blk = Block(type=BlockType.component, component=None)
    result = await pp._extract_node_block(context, blk, 1, 0, "pid", {}, dst_list, {})
    assert result == "pid"  # dst_id should remain parent_id


@pytest.mark.asyncio
async def test_pdf_extract_kdc_node_blocks_none():
    """Test lines 78: kdc_node.blocks is None"""
    pp = PdfParse()
    from modules.entity.kdc_enttiy import Node
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    node = Node(outline_level=1, blocks=None, children=None)
    dst_list = []
    context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    await pp._extract_kdc_node(context, node, "pid", {}, dst_list, {})
    # Should complete without error, dst_list should remain empty
    assert len(dst_list) == 0


@pytest.mark.asyncio 
async def test_pdf_extract_kdc_node_children_none():
    """Test lines 91: kdc_node.children is None"""
    pp = PdfParse()
    from modules.entity.kdc_enttiy import Node
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    node = Node(outline_level=1, blocks=[], children=None)
    dst_list = []
    context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    await pp._extract_kdc_node(context, node, "pid", {}, dst_list, {})
    # Should complete without error
    assert len(dst_list) == 0


@pytest.mark.asyncio
async def test_pdf_extract_kdc_node_dst_list_empty_branch(monkeypatch):
    """Test lines 86-90: dst_list is empty, use parent_id as children_parent_id"""
    pp = PdfParse()
    # stub _extract_node_block to always return parent_id (no new DSTs added)
    async def stub_extract(self, *args, **kwargs):
        return "pid"  # always return parent_id
    monkeypatch.setattr(PdfParse, "_extract_node_block", stub_extract)
    
    from modules.entity.kdc_enttiy import Node, Block, BlockType
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    node = Node(outline_level=1, blocks=[Block(type=BlockType.para)], children=[Node(outline_level=2, blocks=[], children=[])])
    dst_list = []
    await pp._extract_kdc_node(context, node, "pid", {}, dst_list, {})
    # dst_list should remain empty since _extract_node_block always returns parent_id
    assert len(dst_list) == 0


@pytest.mark.asyncio
async def test_pdf_dst_generate_empty_kdc_list():
    """Test lines 100-101: kdc_doc_list is empty, return None"""
    pp = PdfParse()
    pp.kdc_data = [None]  # Will be filtered out by _kdc_validate
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    result = await pp.dst_generate(ctx, pp.kdc_data)
    assert result is None


def test_pdf_dst_reprocess_temp_dst_list_none(monkeypatch):
    """Test line 126: temp_dst_list is None, dst_list unchanged"""
    from modules.flows.dst_builder.pdf import PdfParse
    # typesetting_correct returns (None, layout) to test line 126
    monkeypatch.setattr("modules.layout.typesetting.typesetting_correct", lambda root, page_dst: (None, "top-bottom"))
    monkeypatch.setattr("modules.flows.filters.catalog_filter.get_catalog_pages", lambda lst: -1)
    monkeypatch.setattr("modules.entity.dst_entity.get_page_dst", lambda lst: {0: lst})
    monkeypatch.setattr("modules.flows.filters.filter.dst_mark", lambda lst, page_dst, fi: lst)
    # outline processing stubs
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.analyze_format", lambda outline: ({}, False))
    monkeypatch.setattr("modules.layout.outline.is_main_text", lambda dst, size: False)
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.validate_against_groups", lambda g, c, match_prefix=True: [])
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.add_to_group", lambda g, did, c, p: None)
    monkeypatch.setattr("modules.layout.outline.add_parent", lambda lst: lst)
    monkeypatch.setattr("modules.entity.dst_entity.assign_order_to_dst", lambda lst: lst)

    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    original_dsts = [_dst_root()]
    out = PdfParse().dst_reprocess(ctx, original_dsts)
    assert ctx.layout == "top-bottom" and out == original_dsts  # dst_list should be unchanged


def test_pdf_dst_reprocess_outline_position_and_main_text_continue(monkeypatch):
    """Test lines 175, 180, continue branches in outline processing"""
    from modules.flows.dst_builder.pdf import PdfParse
    monkeypatch.setattr("modules.flows.filters.catalog_filter.get_catalog_pages", lambda lst: -1)
    monkeypatch.setattr("modules.entity.dst_entity.get_page_dst", lambda lst: {0: lst})
    monkeypatch.setattr("modules.flows.filters.filter.dst_mark", lambda lst, page_dst, fi: lst)
    # analyze_format returns (groups, True) 
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.analyze_format", lambda outline: ({}, True))
    # is_main_text True to trigger continue (line 181-182)
    monkeypatch.setattr("modules.layout.outline.is_main_text", lambda dst, size: True)
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.validate_against_groups", lambda g, c, match_prefix=True: [])
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.add_to_group", lambda g, did, c, p: None)
    monkeypatch.setattr("modules.layout.outline.add_parent", lambda lst: lst)
    monkeypatch.setattr("modules.entity.dst_entity.assign_order_to_dst", lambda lst: lst)
    monkeypatch.setattr("modules.layout.typesetting.typesetting_correct", lambda root, page_dst: (None, "top-bottom"))

    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF))
    # Create DSTs with different dst_types to trigger continues
    r = _dst_root()
    # Non-TEXT dst to trigger 180 continue 
    table_dst = DST(id="tbl", parent="r", order=2, dst_type=DSTType.TABLE,
                    attributes=DSTAttribute(level=1, position=PositionInfo(bbox=BBox(x1=1,y1=0,x2=2,y2=1)), page=0, hash="h"*32),
                    content=["table"])
    # TEXT dst with is_main_text=True to trigger 181-182 continue
    text_dst = DST(id="txt", parent="r", order=3, dst_type=DSTType.TEXT,
                   attributes=DSTAttribute(level=1, position=PositionInfo(bbox=BBox(x1=2,y1=0,x2=3,y2=1)), page=0, hash="h"*32),
                   content=["text"])
    dsts = [r, table_dst, text_dst]
    out = PdfParse().dst_reprocess(ctx, dsts)
    assert isinstance(out, list)


def test_pdf_dst_reprocess_continue_and_rotate(monkeypatch):
    from modules.flows.dst_builder.pdf import PdfParse
    # catalog_page = 1 so page 1 items are skipped at line 155
    monkeypatch.setattr("modules.flows.filters.catalog_filter.get_catalog_pages", lambda lst: 1)
    # common stubs
    monkeypatch.setattr("modules.entity.dst_entity.get_page_dst", lambda lst: {0: lst})
    monkeypatch.setattr("modules.flows.filters.filter.dst_mark", lambda lst, page_dst, fi: lst)
    # outline analysis minimal
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.analyze_format", lambda outline: ({}, False))
    monkeypatch.setattr("modules.layout.outline.is_main_text", lambda dst, size: False)
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.validate_against_groups", lambda g, c, match_prefix=True: [])
    monkeypatch.setattr("modules.layout.outline.StringFormatValidator.add_to_group", lambda g, did, c, p: None)
    monkeypatch.setattr("modules.layout.outline.add_parent", lambda lst: lst)
    monkeypatch.setattr("modules.entity.dst_entity.assign_order_to_dst", lambda lst: lst)
    monkeypatch.setattr("modules.layout.typesetting.typesetting_correct", lambda root, page_dst: (None, "layout3"))

    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PDF, rotate_page={"90": [0]}))
    # nodes: one at page 1 to trigger continue (155), one non-text to trigger continue (157), one at page 0 to trigger rotate update (144)
    t_skip = DST(id="ts", parent="r", order=1, dst_type=DSTType.TEXT,
                 attributes=DSTAttribute(level=1, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=1, hash="h"*32),
                 content=["skip"]) 
    non_text = DST(id="img", parent="r", order=2, dst_type=DSTType.IMAGE,
                   attributes=DSTAttribute(level=10, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=2, hash="h"*32),
                   content=["u"]) 
    t_rotate = DST(id="tr", parent="r", order=3, dst_type=DSTType.TEXT,
                   attributes=DSTAttribute(level=10, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
                   content=["rotate"]) 
    r = _dst_root()
    dsts = [r, t_skip, non_text, t_rotate]
    out = PdfParse().dst_reprocess(ctx, dsts)
    # rotate applied
    tr = next(d for d in out if d.id == "tr")
    assert getattr(tr.attributes.position.bbox, "rotate", None) == "90" or getattr(tr.attributes.position.bbox, "rotate", None) == 90

