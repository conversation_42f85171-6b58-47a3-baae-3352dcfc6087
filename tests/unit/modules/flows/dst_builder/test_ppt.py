# Author: linqi
# Date: 2025/8/13

import pytest

from modules.flows.dst_builder.ppt import PptParser
from modules.pipeline.context import PipelineContext, FileInfo
from services.datamodel import FileType


def _ppt_doc():
    return {
        "doc": {
            "prop": {"slide_size": {"width": 1024, "height": 768}, "note_size": {"width": 1024, "height": 768}},
            "slide_containers": [
                {"category": "slides", "slides": [
                    {"id": 1, "name": "Title", "shape_tree": [
                        {"type": "textbox", "textbox": {"blocks": [
                            {"type": "para", "para": {"runs": [{"text": "Title"}], "prop": {"outline_level": 1}}}
                        ]}, "bounding_box": {"x1": 0, "y1": 0, "x2": 10, "y2": 10}}
                    ]}
                ]}
            ],
            "medias": [],
            "hyperlinks": []
        }
    }


@pytest.mark.asyncio
async def test_ppt_dst_generate(monkeypatch):
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {"m1": ["http://u", [100, 100]]})
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", lambda: type("D", (), {"id": "root"})())
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PPT))
    out = await PptParser().dst_generate(ctx, [_ppt_doc()])
    assert 0 in out and len(out[0]) >= 1


@pytest.mark.asyncio
async def test_ppt_generate_without_title_textbox(monkeypatch):
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", lambda: type("D", (), {"id": "root"})())
    # slide with no matching title text (so only child nodes should be added)
    doc = {
        "doc": {
            "prop": {"slide_size": {"width": 1024, "height": 768}, "note_size": {"width": 1024, "height": 768}},
            "slide_containers": [
                {"category": "slides", "slides": [
                    {"id": 1, "name": "Different", "shape_tree": [
                        {"type": "textbox", "textbox": {"blocks": [
                            {"type": "para", "para": {"runs": [{"text": "NotTitle"}], "prop": {"outline_level": 1}}}
                        ]}, "bounding_box": {"x1": 0, "y1": 0, "x2": 10, "y2": 10}}
                    ]}
                ]}
            ],
            "medias": [],
            "hyperlinks": []
        }
    }
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PPT))
    out = await PptParser().dst_generate(ctx, [doc])
    assert 0 in out


@pytest.mark.asyncio
async def test_ppt_generate_blocks_various_types(monkeypatch):
    from modules.entity.kdc_enttiy import BlockType
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", lambda: type("D", (), {"id": "root"})())
    # slide has image, formula, code, table
    doc = {
        "doc": {
            "prop": {"slide_size": {"width": 1024, "height": 768}, "note_size": {"width": 1024, "height": 768}},
            "slide_containers": [
                {"category": "slides", "slides": [
                    {"id": 1, "name": "S1", "shape_tree": [
                        {"type": BlockType.component, "component": {"type": "image", "media_id": "m1"}, "bounding_box": {"x1":0,"y1":0,"x2":1,"y2":1}},
                        {"type": BlockType.component, "component": {"type": "formula", "latex": "x=y", "media_id": "m1"}, "bounding_box": {"x1":0,"y1":0,"x2":1,"y2":1}},
                        {"type": BlockType.component, "component": {"type": "code", "text": "print(1)", "media_id": "m1"}, "bounding_box": {"x1":0,"y1":0,"x2":1,"y2":1}},
                        {"type": BlockType.table, "table": {}, "bounding_box": {"x1":0,"y1":0,"x2":1,"y2":1}},
                    ]}
                ]}
            ],
            "medias": [],
            "hyperlinks": []
        }
    }
    # table html stub and iter_blocks stub to control yielded blocks
    monkeypatch.setattr("modules.common.table_entity2html", lambda *a, **k: "<table></table>")
    async def fake_iter_blocks(context, slide, id2url, id2text):
        bbox = {"x1": 0, "y1": 0, "x2": 1, "y2": 1}
        yield {"type": "image", "level": 0, "content": ["http://u", [100, 100]], "bbox": bbox}
        yield {"type": "formula", "level": 0, "content": "x=y", "bbox": bbox}
        yield {"type": "code", "level": 0, "content": "print(1)", "bbox": bbox}
        yield {"type": "table", "level": 0, "content": "<table></table>", "bbox": bbox}
    monkeypatch.setattr("modules.flows.dst_builder.ppt.iter_blocks", fake_iter_blocks)
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PPT))
    out = await PptParser().dst_generate(ctx, [doc])
    assert 0 in out and len(out[0]) >= 1


def test_ppt_iter_slides_non_slides_category():
    """Test lines 29-32: iter_slides with non-slides category (cover continue branch)"""
    from modules.flows.dst_builder.ppt import iter_slides
    kdc_root = {
        "slide_containers": [
            {"category": "masters", "slides": [{"id": 1, "name": "Master"}]},  # Not "slides", should be skipped
            {"category": "slides", "slides": [{"id": 2, "name": "Normal"}]}   # "slides", should be yielded
        ]
    }
    slides = list(iter_slides(kdc_root))
    assert len(slides) == 1 and slides[0]["id"] == 2


@pytest.mark.asyncio
async def test_ppt_iter_blocks_non_para_continue():
    """Test line 43: iter_blocks with non-para block (cover continue branch)"""
    from modules.flows.dst_builder.ppt import iter_blocks
    from modules.entity.kdc_enttiy import Slide, TextBox, Block, BlockType, Para, Rectangle
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    
    # Create slide with textbox containing non-para block
    slide = Slide(
        id=1, name="Test",
        shape_tree=[
            Block(
                type=BlockType.textbox,
                textbox=TextBox(blocks=[
                    Block(type=BlockType.table),  # Non-para block, should trigger continue
                    Block(type=BlockType.para, para=Para(runs=[]))  # Para block, should be processed
                ]),
                bounding_box=Rectangle(x1=0, y1=0, x2=1, y2=1)
            )
        ]
    )
    context = PipelineContext(file_info=FileInfo(file_type=FileType.PPT))
    blocks = []
    async for blk in iter_blocks(context, slide, {}, {}):
        blocks.append(blk)
    assert len(blocks) == 1 and blocks[0]["type"] in ("title", "text")


@pytest.mark.asyncio  
async def test_ppt_iter_blocks_component_types():
    """Test lines 59-87: iter_blocks with different component types"""
    from modules.flows.dst_builder.ppt import iter_blocks
    from modules.entity.kdc_enttiy import Slide, Component, ComponentType, BlockType, Rectangle, Block
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    
    # Create slide with different component types
    slide = Slide(
        id=1, name="Test",
        shape_tree=[
            Block(type=BlockType.component, component=Component(type=ComponentType.image, media_id="img1"), bounding_box=Rectangle(x1=0, y1=0, x2=1, y2=1)),
            # Block(type=BlockType.component, component=Component(type=ComponentType.formula, latex="E=mc^2", media_id="form1"), bounding_box=Rectangle(x1=0, y1=0, x2=1, y2=1)),
            # Block(type=BlockType.component, component=Component(type=ComponentType.code, text="print('hello')", media_id="code1"), bounding_box=Rectangle(x1=0, y1=0, x2=1, y2=1)),
        ]
    )
    context = PipelineContext(file_info=FileInfo(file_type=FileType.PPT))
    id2url = {"img1": ["http://image.url", [100, 100]]}
    blocks = []
    async for blk in iter_blocks(context, slide, id2url, {}):
        blocks.append(blk)
    assert len(blocks) == 1
    assert blocks[0]["type"] == "image" and blocks[0]["content"] == id2url["img1"]
    # assert blocks[1]["type"] == "formula" and blocks[1]["content"] == "E=mc^2"
    # assert blocks[2]["type"] == "code" and blocks[2]["content"] == "print('hello')"


@pytest.mark.asyncio
async def test_ppt_generate_non_slides_container():
    """Test line 115: generate_presentation_dst with non-slides container (cover continue branch)"""
    from modules.entity.kdc_enttiy import SlideContainerCategory
    monkeypatch = pytest.MonkeyPatch()
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", lambda: type("D", (), {"id": "root"})())
    
    doc = {
        "doc": {
            "prop": {"slide_size": {"width": 1024, "height": 768}, "note_size": {"width": 1024, "height": 768}},
            "slide_containers": [
                {"category": "slide_masters", "slides": []},  # Non-slides category, should be skipped
                {"category": "slides", "slides": [{"id": 1, "name": "Test", "shape_tree": []}]}
            ],
            "medias": [],
            "hyperlinks": []
        }
    }
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PPT))
    out = await PptParser().dst_generate(ctx, [doc])
    assert 0 in out


@pytest.mark.asyncio
async def test_ppt_generate_empty_block_text_continue():
    """Test line 131: generate_presentation_dst with empty block text (cover continue branch)"""
    monkeypatch = pytest.MonkeyPatch()
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", lambda: type("D", (), {"id": "root"})())
    
    async def fake_iter_blocks(context, slide, id2url, id2text):
        # Return block with empty content to trigger continue at line 131
        yield {"type": "text", "level": 1, "content": "", "bbox": {"x1": 0, "y1": 0, "x2": 1, "y2": 1}}
        yield {"type": "text", "level": 1, "content": "ValidText", "bbox": {"x1": 0, "y1": 0, "x2": 1, "y2": 1}}
    monkeypatch.setattr("modules.flows.dst_builder.ppt.iter_blocks", fake_iter_blocks)
    
    doc = {
        "doc": {
            "prop": {"slide_size": {"width": 1024, "height": 768}, "note_size": {"width": 1024, "height": 768}},
            "slide_containers": [
                {"category": "slides", "slides": [{"id": 1, "name": "Test", "shape_tree": []}]}
            ],
            "medias": [],
            "hyperlinks": []
        }
    }
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PPT))
    out = await PptParser().dst_generate(ctx, [doc])
    assert 0 in out


@pytest.mark.asyncio
async def test_ppt_generate_empty_content_text_continue():
    """Test line 168: generate_presentation_dst with empty content_text for title/text (cover continue branch)"""
    monkeypatch = pytest.MonkeyPatch()
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", lambda: type("D", (), {"id": "root"})())
    
    async def fake_iter_blocks(slide, id2url, id2text):
        # First pass: return blocks for finding slide parent
        yield {"type": "title", "level": 1, "content": "Test", "bbox": {"x1": 0, "y1": 0, "x2": 1, "y2": 1}}
        # Second iteration in main loop: return empty content to trigger continue at line 168
        yield {"type": "text", "level": 1, "content": "", "bbox": {"x1": 0, "y1": 0, "x2": 1, "y2": 1}}
        yield {"type": "text", "level": 1, "content": "ValidContent", "bbox": {"x1": 0, "y1": 0, "x2": 1, "y2": 1}}
    
    call_count = 0
    async def counting_iter_blocks(context, slide, id2url, id2text):
        nonlocal call_count
        call_count += 1
        if call_count == 1:
            # First call (line 123): finding slide parent
            yield {"type": "title", "level": 1, "content": "Test", "bbox": {"x1": 0, "y1": 0, "x2": 1, "y2": 1}}
        else:
            # Second call (line 157): main processing
            yield {"type": "text", "level": 1, "content": "", "bbox": {"x1": 0, "y1": 0, "x2": 1, "y2": 1}}  # Empty, triggers continue at 168
            yield {"type": "text", "level": 1, "content": "Valid", "bbox": {"x1": 0, "y1": 0, "x2": 1, "y2": 1}}
    
    monkeypatch.setattr("modules.flows.dst_builder.ppt.iter_blocks", counting_iter_blocks)
    
    doc = {
        "doc": {
            "prop": {"slide_size": {"width": 1024, "height": 768}, "note_size": {"width": 1024, "height": 768}},
            "slide_containers": [
                {"category": "slides", "slides": [{"id": 1, "name": "Test", "shape_tree": []}]}
            ],
            "medias": [],
            "hyperlinks": []
        }
    }
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PPT))
    out = await PptParser().dst_generate(ctx, [doc])
    assert 0 in out


def test_ppt_dst_reprocess():
    """Test line 210: dst_reprocess method (empty implementation)"""
    parser = PptParser()
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.PPT))
    result = parser.dst_reprocess(ctx, [])
    assert result is None  # Should return None (empty implementation)


def test_ppt_get_res():
    """Test line 213: get_res method (empty implementation)"""
    parser = PptParser()
    from modules.entity.dst_entity import DST, DSTType, DSTAttribute, BBox, PositionInfo
    dst = DST(id="test", parent="root", order=0, dst_type=DSTType.TEXT,
              attributes=DSTAttribute(level=1, position=PositionInfo(bbox=BBox(x1=0,y1=0,x2=1,y2=1)), page=0, hash="h"*32),
              content=["test"])
    result = parser.get_res(dst)
    assert result is None  # Should return None (empty implementation)

