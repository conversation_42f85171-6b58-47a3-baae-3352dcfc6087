# Author: linqi
# Date: 2025/8/13
# Time: 19:42

import pytest

@pytest.mark.asyncio
async def test_kdc_entity_parse_nodes(monkeypatch):
    # Import node classes and kdc entity helpers
    from modules.flows.dst_builder.kdc_entity_parse import (
        ComponentNode, TextboxNode, TableNode, ParaNode, HighLightNode
    )
    from modules.entity.kdc_enttiy import (
        Block, BlockType, ComponentType, Component, Rectangle, Para, Run, RunProp, ParaProp, TextBox, Table, TableRow, TableCell, Highlight
    )
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType

    # Common mappings
    id2url = {"m1": ["http://u", [100, 100]]}
    id2text = {}
    ls = []
    parent_id = "pid"
    context = PipelineContext(file_info=FileInfo(file_type=FileType.DOCX))

    # ComponentNode with valid media and bbox
    comp = Component(type=ComponentType.image, media_id="m1")
    blk_comp = Block(type=BlockType.component, component=comp, page_index=1, id="bid_10_5", bounding_box=Rectangle(x1=0,y1=0,x2=10,y2=10))
    cid = ComponentNode(blk_comp, id2url, id2text).process(context, 1, parent_id, 0, ls)
    assert cid != parent_id and ls[-1].dst_type.value in ("image", "other")

    # TextboxNode with inner para runs, run props, and bbox
    run = Run(text="Hello", prop=RunProp(size=12, bold=True))
    inner_para = Para(runs=[run], prop=ParaProp(outline_level=1, list_string="* "))
    inner_block = Block(type=BlockType.para, para=inner_para)
    textbox = TextBox(blocks=[inner_block])
    blk_tb = Block(type=BlockType.textbox, textbox=textbox, page_index=0, id="tbox_1_2", bounding_box=Rectangle(x1=1,y1=1,x2=5,y2=5))
    tid = TextboxNode(blk_tb, id2url, id2text).process(context, 1, parent_id, 1, ls)
    assert tid != parent_id and "Hello" in "".join(ls[-1].content)

    # ParaNode with runs and props, id without underscore (numeric parse path)
    run2 = Run(text="World")
    para2 = Para(runs=[run2], prop=ParaProp(outline_level=2, list_string="- "))
    blk_para = Block(type=BlockType.para, para=para2, page_index=0, id="abc12d34", bounding_box=Rectangle(x1=2,y1=2,x2=6,y2=6))
    pid2 = ParaNode(blk_para, id2url, id2text).process(context, 2, parent_id, 2, ls)
    assert pid2 != parent_id and "World" in ls[-1].content

    # TableNode with bbox and stubbed table_entity2html
    monkeypatch.setattr("modules.common.table_entity2html", lambda *a, **k: "<table><tr><td>x</td></tr></table>")
    table = Table(rows=[TableRow(cells=[TableCell()])])
    blk_table = Block(type=BlockType.table, table=table, page_index=0, id="tbl_3_2", bounding_box=Rectangle(x1=3,y1=3,x2=7,y2=7))
    tid3 = await TableNode(blk_table, id2url, id2text).process(context, 3, parent_id, 3, ls)
    assert tid3 != parent_id and ls[-1].dst_type.value == "table"

    # HighLightNode with inner para
    h_run = Run(text="HL")
    h_para = Para(runs=[h_run])
    h_block = Block(type=BlockType.para, para=h_para)
    highlight = Highlight(blocks=[h_block])
    blk_hl = Block(type=BlockType.highlight, highlight=highlight, page_index=0, id="hl_1_1", bounding_box=Rectangle(x1=4,y1=4,x2=8,y2=8))
    hid = HighLightNode(blk_hl, id2url, id2text).process(context, 1, parent_id, 4, ls)
    assert hid != parent_id and "HL" in "".join(ls[-1].content)

    # ComponentNode: media not found -> return parent_id
    comp2 = Component(type=ComponentType.image, media_id="missing")
    blk_comp2 = Block(type=BlockType.component, component=comp2, page_index=None, id="z", bounding_box=None)
    ret = ComponentNode(blk_comp2, id2url, id2text).process(context, 1, parent_id, 5, ls)
    assert ret == parent_id

    # ComponentNode: unknown component type falls back to OTHER
    comp3 = Component(type=ComponentType.other, media_id="m1")
    blk_comp3 = Block(type=BlockType.component, component=comp3, page_index=None, id="id_1_1", bounding_box=None)
    cid3 = ComponentNode(blk_comp3, id2url, id2text).process(context, 1, parent_id, 6, ls)
    assert cid3 != parent_id and ls[-1].dst_type.value in ("other",)

    # TextboxNode: no id and no bbox -> return parent_id
    empty_tb = TextBox(blocks=[])
    blk_tb2 = Block(type=BlockType.textbox, textbox=empty_tb, page_index=None, id=None, bounding_box=None)
    ret2 = TextboxNode(blk_tb2, id2url, id2text).process(context, 1, parent_id, 7, ls)
    assert ret2 == parent_id

    # ParaNode: no runs -> return parent_id
    blk_para2 = Block(type=BlockType.para, para=Para(runs=[]), page_index=None, id="x", bounding_box=None)
    ret3 = ParaNode(blk_para2, id2url, id2text).process(context, 1, parent_id, 8, ls)
    assert ret3 == parent_id

    # ParaNode: no id and no bbox -> return parent_id
    blk_para3 = Block(type=BlockType.para, para=Para(runs=[Run(text="x")]), page_index=None, id=None, bounding_box=None)
    ret4 = ParaNode(blk_para3, id2url, id2text).process(context, 1, parent_id, 9, ls)
    assert ret4 == parent_id

    # TableNode: bbox None and id None -> return parent_id
    table2 = Table(rows=[])
    blk_table2 = Block(type=BlockType.table, table=table2, page_index=None, id=None, bounding_box=None)
    ret5 = await TableNode(blk_table2, id2url, id2text).process(context, 1, parent_id, 10, ls)
    assert ret5 == parent_id

    # TableNode: get_bounding_box walks into cell block bbox
    inner_block_with_bbox = Block(type=BlockType.para, para=Para(runs=[Run(text="x")]), bounding_box=Rectangle(x1=9,y1=9,x2=10,y2=10))
    cell_with_block_bbox = TableCell(blocks=[inner_block_with_bbox])
    row3 = TableRow(cells=[cell_with_block_bbox])
    table3 = Table(rows=[row3])
    blk_table3 = Block(type=BlockType.table, table=table3, page_index=0, id="tblx", bounding_box=None)
    # invalid id so block_coordinate None, and bbox comes from inner
    tid4 = await TableNode(blk_table3, id2url, id2text).process(context, 1, parent_id, 11, ls)
    assert tid4 != parent_id and ls[-1].attributes.position.bbox is not None

    # parse_string cover fail paths
    from modules.flows.dst_builder.kdc_entity_parse import parse_string
    # underscore but non-integers
    bid, gcp, length, ok = parse_string("id_a_b")
    assert ok is False and bid == "id_a_b"
    # no digits and no underscore
    bid2, gcp2, length2, ok2 = parse_string("NoDigitsHere")
    assert ok2 is False and bid2 == "NoDigitsHere"


@pytest.mark.asyncio
async def test_otl_traverse_covers_all(monkeypatch):
    # dummy processors for otl.traverse
    class D:
        def __init__(self, *a, **k): pass
        def process(self, *a, **k): return "nid"
    class AD:
        def __init__(self,*a,**k): pass
        async def process(self, *a, **k): return "nid"
    monkeypatch.setattr("modules.flows.dst_builder.otl.ParaNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.TextboxNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.ComponentNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.HighLightNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.TableNode", AD)

    from modules.flows.dst_builder.otl import traverse
    from modules.entity.kdc_enttiy import Node, Block, BlockType
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    # build node with all block types and a child
    blocks = [
        Block(type=BlockType.para),
        Block(type=BlockType.table),
        Block(type=BlockType.textbox),
        Block(type=BlockType.component),
        Block(type=BlockType.highlight),
    ]
    child = Node(blocks=[], children=[])
    root = Node(blocks=blocks, children=[child], outline_level=1)
    ls = []
    context = PipelineContext(file_info=FileInfo(file_type=FileType.OTL))
    # first with empty id2url so component path checks continue condition inside traverse
    await traverse(context, {}, {}, root, parent_id="p", ls=ls)
    # then with non-empty id2url to execute component branch fully
    await traverse(context, {"any": ["u", [1,1]]}, {}, root, parent_id="p", ls=ls)
    assert isinstance(ls, list)


@pytest.mark.asyncio
async def test_otl_dst_generate_minimal(monkeypatch):
    from modules.flows.dst_builder.otl import OTLParse
    # stub node processors (used by traverse)
    class D:
        def __init__(self,*a,**k): pass
        def process(self,*a,**k): return "nid"
    class AD:
        def __init__(self,*a,**k): pass
        async def process(self,*a,**k): return "nid"
    monkeypatch.setattr("modules.flows.dst_builder.otl.ParaNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.TextboxNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.ComponentNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.HighLightNode", D)
    monkeypatch.setattr("modules.flows.dst_builder.otl.TableNode", AD)
    # stub common helpers
    from modules.pipeline.context import PipelineContext, FileInfo
    from services.datamodel import FileType
    monkeypatch.setattr("modules.common.build_media_map", lambda *a, **k: {})
    monkeypatch.setattr("modules.common.build_root_dst", lambda: type("D", (), {"id": "root"})())
    monkeypatch.setattr("modules.common.build_hyperlink_map", lambda *a, **k: {})
    # minimal valid kdc doc
    doc = {"doc": {"tree": {"blocks": []}, "medias": [], "hyperlinks": []}}
    ctx = PipelineContext(file_info=FileInfo(file_type=FileType.OTL))
    out = await OTLParse().dst_generate(ctx, [doc])
    assert 0 in out


def test_parse_template_kdc_parser_and_data_process(monkeypatch):
    # test kdc_parser calls KDCRpc.get_content_by_url_or_file with file_type='otl' enabling upload_medias
    from modules.flows.dst_builder.parse_template import kdc_parser, ParseTemplate
    class DummyReq:
        def __init__(self, text): self.text = text
        def raise_for_status(self): pass
    # mock KDCRpc
    class DummyKDCRpc:
        def get_content_by_url_or_file(self, **kwargs):
            # ensure convert_options contains flag
            assert kwargs.get("convert_options", {}).get("enable_upload_medias", False) is True
            return [{"doc": {}}]
    monkeypatch.setattr("modules.flows.dst_builder.parse_template.KDCRpc", lambda: DummyKDCRpc())
    res = kdc_parser("cid", b"bytes", "name", file_type="otl")
    assert isinstance(res, list)

    # data_process with kdc_ks3_url path (requests.get)
    import json
    class DummyRequests:
        def get(self, url): return DummyReq(json.dumps({"doc": {}}))
    monkeypatch.setattr("modules.flows.dst_builder.parse_template.requests", DummyRequests())
    class DummyPT(ParseTemplate):
        def __init__(self): pass
        async def dst_generate(self, *a, **k): pass
        def dst_reprocess(self, *a, **k): pass
        def get_res(self, *a, **k): pass
    pt = DummyPT()
    out = pt.data_process(kdc_ks3_url="http://x")
    assert isinstance(out, list)
    # data_process else path using kdc_parser stub
    monkeypatch.setattr("modules.flows.dst_builder.parse_template.kdc_parser", lambda *a, **k: [1,2,3])
    out2 = pt.data_process(file_url_or_bytes=b"b", file_name="n")
    assert out2 == [1,2,3]