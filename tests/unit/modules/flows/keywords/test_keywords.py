import pytest
from unittest.mock import Mock, MagicMock, AsyncMock, patch
from modules.flows.keywords.keywords import KeywordsNode
from modules.pipeline.context import PipelineContext, FileInfo, FileType
from modules.entity.dst_entity import DST, DSTAttribute, BBox, DSTType, PositionInfo


class TestKeywordsNode:
    def create_test_context(self):
        """Create a test context with proper DST structure"""
        return PipelineContext(
            file_info=FileInfo(file_type=FileType.PDF),
            dst=[
                DST(
                    id="test_id",
                    parent="-1",
                    order=0,
                    dst_type=DSTType.TEXT,
                    attributes=DSTAttribute(
                        level=1,
                        position=PositionInfo(bbox=BBox(x1=0, y1=0, x2=100, y2=50)),
                        page=1,
                        hash="test_hash_32_chars_long_string_123456789"
                    ),
                    content=["Test content for keywords extraction"],
                    image_pixel=None,
                    mark=None
                )
            ]
        )

    @patch('modules.flows.keywords.keywords.get_tag_services')
    @patch('modules.flows.keywords.keywords.RpcFactory')
    @patch('modules.flows.keywords.keywords.callback_parse_background')
    async def test_keywords_node_process_success(self, mock_callback, mock_rpc_factory, mock_get_tag_services):
        """Test successful keywords node processing"""
        mock_get_tag_services.return_value = ["keyword1", "keyword2", "keyword3"]
        
        # Mock RPC for embedding
        mock_rpc = Mock()
        mock_response = Mock()
        mock_response.is_success.return_value = True
        mock_response.data.embedding = [0.1, 0.2, 0.3]
        mock_rpc.arequest_text_embedding = AsyncMock(return_value=mock_response)
        mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
        
        mock_callback.return_value = None

        context = self.create_test_context()
        node = KeywordsNode("keywords")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["keywords"]["keywords"] == ["keyword1", "keyword2", "keyword3"]
        assert context.handler_results["keywords"]["keywords_embedding"] == [0.1, 0.2, 0.3]
        mock_get_tag_services.assert_called_once()

    @patch('modules.flows.keywords.keywords.get_tag_services')
    @patch('modules.flows.keywords.keywords.callback_parse_background')
    async def test_keywords_node_process_embedding_disabled(self, mock_callback, mock_get_tag_services):
        """Test keywords node processing with embedding disabled"""
        mock_get_tag_services.return_value = ["keyword1", "keyword2"]
        mock_callback.return_value = None
        
        context = self.create_test_context()
        context.embed_enabled = False
        node = KeywordsNode("keywords")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["keywords"]["keywords"] == ["keyword1", "keyword2"]
        assert context.handler_results["keywords"]["keywords_embedding"] is None

    @patch('modules.flows.keywords.keywords.get_tag_services')
    @patch('modules.flows.keywords.keywords.RpcFactory')
    @patch('modules.flows.keywords.keywords.callback_parse_background')
    async def test_keywords_node_process_embedding_failure(self, mock_callback, mock_rpc_factory, mock_get_tag_services):
        """Test keywords node processing with embedding failure"""
        mock_get_tag_services.return_value = ["keyword1", "keyword2"]
        
        # Mock RPC failure
        mock_rpc = Mock()
        mock_response = Mock()
        mock_response.is_success.return_value = False
        mock_rpc.arequest_text_embedding = AsyncMock(return_value=mock_response)
        mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
        
        mock_callback.return_value = None

        context = self.create_test_context()
        node = KeywordsNode("keywords")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["keywords"]["keywords"] == ["keyword1", "keyword2"]
        assert context.handler_results["keywords"]["keywords_embedding"] == []

    @patch('modules.flows.keywords.keywords.get_tag_services')
    @patch('modules.flows.keywords.keywords.RpcFactory')
    @patch('modules.flows.keywords.keywords.callback_parse_background')
    async def test_keywords_node_process_empty_keywords(self, mock_callback, mock_rpc_factory, mock_get_tag_services):
        """Test keywords node processing with empty keywords"""
        mock_get_tag_services.return_value = []
        
        # Mock RPC for embedding
        mock_rpc = Mock()
        mock_response = Mock()
        mock_response.is_success.return_value = True
        mock_response.data.embedding = [0.1, 0.2, 0.3]
        mock_rpc.arequest_text_embedding = AsyncMock(return_value=mock_response)
        mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
        
        mock_callback.return_value = None
        
        context = self.create_test_context()
        node = KeywordsNode("keywords")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["keywords"]["keywords"] == []
        # Empty keywords should still call embedding with empty string
        mock_rpc.arequest_text_embedding.assert_called_once_with("")

    @patch('modules.flows.keywords.keywords.get_tag_services')
    @patch('modules.flows.keywords.keywords.RpcFactory')
    @patch('modules.flows.keywords.keywords.callback_parse_background')
    async def test_keywords_node_process_single_keyword(self, mock_callback, mock_rpc_factory, mock_get_tag_services):
        """Test keywords node processing with single keyword"""
        mock_get_tag_services.return_value = ["single_keyword"]
        
        # Mock RPC for embedding
        mock_rpc = Mock()
        mock_response = Mock()
        mock_response.is_success.return_value = True
        mock_response.data.embedding = [0.5]
        mock_rpc.arequest_text_embedding = AsyncMock(return_value=mock_response)
        mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
        
        mock_callback.return_value = None
        
        context = self.create_test_context()
        node = KeywordsNode("keywords")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["keywords"]["keywords"] == ["single_keyword"]
        assert context.handler_results["keywords"]["keywords_embedding"] == [0.5]
        mock_rpc.arequest_text_embedding.assert_called_once_with("single_keyword")

    @patch('modules.flows.keywords.keywords.get_tag_services')
    async def test_keywords_node_process_exception_handling(self, mock_get_tag_services):
        """Test keywords node processing with exception handling"""
        mock_get_tag_services.side_effect = Exception("Service error")

        context = self.create_test_context()
        node = KeywordsNode("keywords")

        result = await node.process(context)

        # Should return None on error (as per the actual implementation)
        assert result is None

    @patch('modules.flows.keywords.keywords.get_tag_services')
    @patch('modules.flows.keywords.keywords.RpcFactory')
    @patch('modules.flows.keywords.keywords.callback_parse_background')
    async def test_keywords_node_process_empty_dst(self, mock_callback, mock_rpc_factory, mock_get_tag_services):
        """Test keywords node processing with empty DST"""
        mock_get_tag_services.return_value = ["keyword1", "keyword2"]
        
        # Mock RPC for embedding
        mock_rpc = Mock()
        mock_response = Mock()
        mock_response.is_success.return_value = True
        mock_response.data.embedding = [0.1, 0.2, 0.3]
        mock_rpc.arequest_text_embedding = AsyncMock(return_value=mock_response)
        mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
        
        mock_callback.return_value = None
        
        context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF), dst=[])
        node = KeywordsNode("keywords")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["keywords"]["keywords"] == ["keyword1", "keyword2"]

    @patch('modules.flows.keywords.keywords.get_tag_services')
    @patch('modules.flows.keywords.keywords.callback_parse_background')
    async def test_keywords_node_process_none_dst(self, mock_callback, mock_get_tag_services):
        """Test keywords node processing with None DST"""
        mock_get_tag_services.return_value = ["keyword1", "keyword2"]
        mock_callback.return_value = None
        
        context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF), dst=None)
        node = KeywordsNode("keywords")

        result = await node.process(context)

        # Should return None when dst is None (as per the actual implementation)
        assert result is None

    @patch('modules.flows.keywords.keywords.get_tag_services')
    @patch('modules.flows.keywords.keywords.RpcFactory')
    @patch('modules.flows.keywords.keywords.callback_parse_background')
    async def test_keywords_node_process_keywords_with_special_characters(self, mock_callback, mock_rpc_factory, mock_get_tag_services):
        """Test keywords node processing with special characters in keywords"""
        mock_get_tag_services.return_value = ["keyword@123", "keyword#456", "keyword$789"]
        
        # Mock RPC for embedding
        mock_rpc = Mock()
        mock_response = Mock()
        mock_response.is_success.return_value = True
        mock_response.data.embedding = [0.1, 0.2, 0.3]
        mock_rpc.arequest_text_embedding = AsyncMock(return_value=mock_response)
        mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
        
        mock_callback.return_value = None
        
        context = self.create_test_context()
        node = KeywordsNode("keywords")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["keywords"]["keywords"] == ["keyword@123", "keyword#456", "keyword$789"]
        # Verify embedding was called with joined keywords
        mock_rpc.arequest_text_embedding.assert_called_once_with("keyword@123,keyword#456,keyword$789")

    def test_keywords_node_initialization(self):
        """Test KeywordsNode initialization"""
        node = KeywordsNode("test_name")
        assert node.name == "test_name"
