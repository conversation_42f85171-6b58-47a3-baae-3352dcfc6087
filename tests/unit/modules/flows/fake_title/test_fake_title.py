import pytest
from unittest.mock import Mock, <PERSON><PERSON>ock, AsyncMock, patch
from modules.flows.fake_title.fake_title import fake_title_services, FakeTitleNode
from modules.pipeline.context import PipelineContext, FileInfo, FileType
from modules.entity.dst_entity import DST, DSTAttribute, BBox, DSTType, PositionInfo


class TestFakeTitleServices:
    @patch('modules.flows.fake_title.fake_title.LMModel')
    async def test_fake_title_services_success(self, mock_llm_model):
        """Test successful fake title generation"""
        mock_llm_model.generate_response = AsyncMock(return_value=("ok", '"Generated Title"'))

        content = "This is a test document content."
        result = await fake_title_services(content)

        assert result == "Generated Title"
        mock_llm_model.generate_response.assert_called_once()

    @patch('modules.flows.fake_title.fake_title.LMModel')
    async def test_fake_title_services_long_content_truncation(self, mock_llm_model):
        """Test fake title generation with long content that gets truncated"""
        mock_llm_model.generate_response = AsyncMock(return_value=("ok", '"Truncated Title"'))

        # Create content longer than 5000 characters
        long_content = "A" * 6000
        result = await fake_title_services(long_content)

        assert result == "Truncated Title"
        # Verify that the content was truncated - the actual prompt is longer than expected
        call_args = mock_llm_model.generate_response.call_args
        messages = call_args[1]['messages']
        # The content should be truncated to fit within reasonable limits
        assert len(messages[1].content) < 6000

    @patch('modules.flows.fake_title.fake_title.LMModel')
    async def test_fake_title_services_llm_failure(self, mock_llm_model):
        """Test fake title generation when LLM fails"""
        mock_llm_model.generate_response = AsyncMock(return_value=("fail", "Error message"))

        content = "This is a test document content."
        result = await fake_title_services(content)

        assert result == ""

    async def test_fake_title_services_none_content(self):
        """Test fake title generation with None content"""
        result = await fake_title_services(None)
        assert result == ""

    async def test_fake_title_services_empty_content(self):
        """Test fake title generation with empty content"""
        result = await fake_title_services("")
        assert result == ""

    @patch('modules.flows.fake_title.fake_title.LMModel')
    async def test_fake_title_services_exception_handling(self, mock_llm_model):
        """Test fake title generation with exception handling"""
        mock_llm_model.generate_response = AsyncMock(side_effect=Exception("LLM service error"))

        content = "This is a test document content."
        result = await fake_title_services(content)

        assert result == ""

    @patch('modules.flows.fake_title.fake_title.LMModel')
    async def test_fake_title_services_quotes_removal(self, mock_llm_model):
        """Test fake title generation with quotes removal"""
        mock_llm_model.generate_response = AsyncMock(return_value=("ok", '"Title with extra quotes"'))

        content = "This is a test document content."
        result = await fake_title_services(content)

        assert result == "Title with extra quotes"


class TestFakeTitleNode:
    def create_test_context(self):
        """Create a test context with proper DST structure"""
        return PipelineContext(
            file_info=FileInfo(file_type=FileType.PDF),
            dst=[
                DST(
                    id="test_id",
                    parent="-1",
                    order=0,
                    dst_type=DSTType.TITLE,
                    attributes=DSTAttribute(
                        level=1,
                        position=PositionInfo(bbox=BBox(x1=0, y1=0, x2=100, y2=50)),
                        page=1,
                        hash="test_hash_32_chars_long_string_123456789"
                    ),
                    content=["Test Title"],
                    image_pixel=None,
                    mark=None
                )
            ]
        )

    @patch('modules.flows.fake_title.fake_title.fake_title_services')
    @patch('modules.flows.fake_title.fake_title.RpcFactory')
    @patch('modules.flows.fake_title.fake_title.callback_parse_background')
    async def test_fake_title_node_process_success(self, mock_callback, mock_rpc_factory, mock_fake_title_services):
        """Test successful fake title node processing"""
        mock_fake_title_services.return_value = "Generated Fake Title"
        
        # Mock RPC for embedding
        mock_rpc = Mock()
        mock_response = Mock()
        mock_response.is_success.return_value = True
        mock_response.data.embedding = [0.1, 0.2, 0.3]
        mock_rpc.arequest_text_embedding = AsyncMock(return_value=mock_response)
        mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
        
        mock_callback.return_value = None

        context = self.create_test_context()
        node = FakeTitleNode("fake_title")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["fake_title"]["fake_title"] == "Generated Fake Title"
        assert context.handler_results["fake_title"]["fake_title_embedding"] == [0.1, 0.2, 0.3]
        mock_fake_title_services.assert_called_once()

    @patch('modules.flows.fake_title.fake_title.fake_title_services')
    @patch('modules.flows.fake_title.fake_title.callback_parse_background')
    async def test_fake_title_node_process_embedding_disabled(self, mock_callback, mock_fake_title_services):
        """Test fake title node processing with embedding disabled"""
        mock_fake_title_services.return_value = "Generated Fake Title"
        mock_callback.return_value = None
        
        context = self.create_test_context()
        context.embed_enabled = False
        node = FakeTitleNode("fake_title")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["fake_title"]["fake_title"] == "Generated Fake Title"
        assert context.handler_results["fake_title"]["fake_title_embedding"] is None

    @patch('modules.flows.fake_title.fake_title.fake_title_services')
    @patch('modules.flows.fake_title.fake_title.RpcFactory')
    @patch('modules.flows.fake_title.fake_title.callback_parse_background')
    async def test_fake_title_node_process_embedding_failure(self, mock_callback, mock_rpc_factory, mock_fake_title_services):
        """Test fake title node processing with embedding failure"""
        mock_fake_title_services.return_value = "Generated Fake Title"
        
        # Mock RPC failure
        mock_rpc = Mock()
        mock_response = Mock()
        mock_response.is_success.return_value = False
        mock_rpc.arequest_text_embedding = AsyncMock(return_value=mock_response)
        mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
        
        mock_callback.return_value = None

        context = self.create_test_context()
        node = FakeTitleNode("fake_title")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["fake_title"]["fake_title"] == "Generated Fake Title"
        assert context.handler_results["fake_title"]["fake_title_embedding"] == []

    @patch('modules.flows.fake_title.fake_title.fake_title_services')
    async def test_fake_title_node_process_exception_handling(self, mock_fake_title_services):
        """Test fake title node processing with exception handling"""
        mock_fake_title_services.side_effect = Exception("Service error")

        context = self.create_test_context()
        node = FakeTitleNode("fake_title")

        result = await node.process(context)

        # Should return None on error (as per the actual implementation)
        assert result is None

    @patch('modules.flows.fake_title.fake_title.fake_title_services')
    @patch('modules.flows.fake_title.fake_title.RpcFactory')
    @patch('modules.flows.fake_title.fake_title.callback_parse_background')
    async def test_fake_title_node_process_empty_dst(self, mock_callback, mock_rpc_factory, mock_fake_title_services):
        """Test fake title node processing with empty DST"""
        mock_fake_title_services.return_value = "Generated Fake Title"
        
        # Mock RPC for embedding
        mock_rpc = Mock()
        mock_response = Mock()
        mock_response.is_success.return_value = True
        mock_response.data.embedding = [0.1, 0.2, 0.3]
        mock_rpc.arequest_text_embedding = AsyncMock(return_value=mock_response)
        mock_rpc_factory.return_value.get_normal_model_rpc.return_value = mock_rpc
        
        mock_callback.return_value = None
        
        context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF), dst=[])
        node = FakeTitleNode("fake_title")

        result = await node.process(context)

        assert result == context
        assert context.handler_results["fake_title"]["fake_title"] == "Generated Fake Title"

    @patch('modules.flows.fake_title.fake_title.fake_title_services')
    @patch('modules.flows.fake_title.fake_title.callback_parse_background')
    async def test_fake_title_node_process_none_dst(self, mock_callback, mock_fake_title_services):
        """Test fake title node processing with None DST"""
        mock_fake_title_services.return_value = "Generated Fake Title"
        mock_callback.return_value = None
        
        context = PipelineContext(file_info=FileInfo(file_type=FileType.PDF), dst=None)
        node = FakeTitleNode("fake_title")

        result = await node.process(context)

        # Should return None when dst is None (as per the actual implementation)
        assert result is None

    def test_fake_title_node_initialization(self):
        """Test FakeTitleNode initialization"""
        node = FakeTitleNode("test_name")
        assert node.name == "test_name"
