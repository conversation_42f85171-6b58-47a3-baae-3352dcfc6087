import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from typing import List

from modules.flows.summary.summary import (
    ReqSummary,
    RespSummaryData,
    RespSummary,
    summary_services,
    SummaryNode
)
from modules.pipeline.context import PipelineContext, FileInfo, FileType
from modules.entity.dst_entity import DST, BBox, DSTType, DSTAttribute, PositionInfo
from modules.entity.parse_entity import ParseRes
from modules.llm.chat_api import LMModel
from modules.llm.prompts import SUMMARY_PROMPT_WITHOUT_KEY
from modules.flows.summary.summary_generate import extract_sections
from routers.httpcode import HTTPCODE
from commons.rpc.rpc_factory import RpcFactory


class TestReqSummary:
    """测试 ReqSummary 类"""

    def test_req_summary_creation(self):
        """测试 ReqSummary 创建"""
        req = ReqSummary(content="Test content for summary")
        
        assert req.content == "Test content for summary"

    def test_req_summary_empty_content(self):
        """测试 ReqSummary 空内容"""
        req = ReqSummary(content="")
        
        assert req.content == ""


class TestRespSummaryData:
    """测试 RespSummaryData 类"""

    def test_resp_summary_data_creation(self):
        """测试 RespSummaryData 创建"""
        resp_data = RespSummaryData(
            text_summary="This is a test summary",
            high_level_key="test_key"
        )
        
        assert resp_data.text_summary == "This is a test summary"
        assert resp_data.high_level_key == "test_key"

    def test_resp_summary_data_default_values(self):
        """测试 RespSummaryData 默认值"""
        resp_data = RespSummaryData(text_summary="Test summary")
        
        assert resp_data.text_summary == "Test summary"
        assert resp_data.high_level_key == ""


class TestRespSummary:
    """测试 RespSummary 类"""

    def test_resp_summary_creation(self):
        """测试 RespSummary 创建"""
        resp_data = RespSummaryData(text_summary="Test summary")
        resp = RespSummary(code=HTTPCODE.OK, data=resp_data)
        
        assert resp.code == HTTPCODE.OK
        assert resp.data == resp_data

    def test_resp_summary_no_data(self):
        """测试 RespSummary 无数据"""
        resp = RespSummary(code=HTTPCODE.ERROR)
        
        assert resp.code == HTTPCODE.ERROR
        assert resp.data is None


class TestSummaryServices:
    """测试 summary_services 函数"""

    @pytest.mark.asyncio
    async def test_summary_services_success(self):
        """测试成功生成摘要"""
        with patch('modules.flows.summary.summary.extract_sections') as mock_extract, \
             patch('modules.flows.summary.summary.LMModel.generate_response') as mock_generate:
            
            # Setup mocks
            mock_extract.return_value = "Extracted content for summary"
            mock_generate.return_value = (True, "Generated summary text")
            
            req = ReqSummary(content="Test content for summary generation")
            result = await summary_services(req)
            
            # Verify result
            assert result.code == HTTPCODE.OK
            assert result.data is not None
            assert result.data.text_summary == "Generated summary text"
            assert result.data.high_level_key == ""
            
            # Verify calls
            mock_extract.assert_called_once_with("Test content for summary generation", section_length=300)
            mock_generate.assert_called_once()
            
            # Verify generate call arguments
            call_args = mock_generate.call_args
            assert call_args[1]['top_k'] == 0.99
            assert "Extracted content for summary" in call_args[0][0]

    @pytest.mark.asyncio
    async def test_summary_services_long_content_truncation(self):
        """测试长内容被截断"""
        with patch('modules.flows.summary.summary.extract_sections') as mock_extract, \
             patch('modules.flows.summary.summary.LMModel.generate_response') as mock_generate:
            
            mock_extract.return_value = "A" * 6000  # Longer than 5000
            mock_generate.return_value = (True, "Short summary")
            
            req = ReqSummary(content="Long content")
            result = await summary_services(req)
            
            assert result.code == HTTPCODE.OK
            assert result.data.text_summary == "Short summary"
            
            # Verify content was truncated to 5000 characters
            call_args = mock_generate.call_args
            assert len(call_args[0][0]) <= 5000 + len(SUMMARY_PROMPT_WITHOUT_KEY.format(input_text=""))

    @pytest.mark.asyncio
    async def test_summary_services_none_content(self):
        """测试None内容的情况"""
        # ReqSummary doesn't allow None content, so we test the service function directly
        result = await summary_services(MagicMock(content=None))
        
        assert result.code == HTTPCODE.OK
        assert result.data.text_summary == ""

    @pytest.mark.asyncio
    async def test_summary_services_empty_content(self):
        """测试空内容的情况"""
        req = ReqSummary(content="")
        result = await summary_services(req)
        
        assert result.code == HTTPCODE.OK
        assert result.data.text_summary == ""

    @pytest.mark.asyncio
    async def test_summary_services_exception_handling(self):
        """测试异常处理"""
        with patch('modules.flows.summary.summary.extract_sections') as mock_extract:
            mock_extract.side_effect = Exception("Extraction error")
            
            req = ReqSummary(content="Test content")
            result = await summary_services(req)
            
            assert result.code == HTTPCODE.ERROR
            assert result.data is None


class TestSummaryNode:
    """测试 SummaryNode 类"""

    def create_test_context(self):
        """创建测试用的 PipelineContext"""
        # Create a simple DST for testing
        dst = DST(
            id="test_id",
            parent="",
            order=0,
            dst_type=DSTType.TEXT,
            attributes=DSTAttribute(
                level=0,
                position=PositionInfo(bbox=BBox(x1=0, y1=0, x2=100, y2=50)),
                page=1,
                hash="test_hash_32_chars_long_string_123456789"
            ),
            content=["Test content for summary generation"]
        )
        
        context = PipelineContext(
            file_info=FileInfo(
                file_type=FileType.PDF,
                page_size=1,
                word_count=100,
                width=800,
                height=600,
                is_scan=False,
                rotate_page={}
            ),
            dst=[dst],
            embed_enabled=True,
            token="test_token",
            need_callback=True,
            return_ks3_url=True,
            callback_url="https://example.com/callback",
            parse_version="1.0"
        )
        
        return context

    @pytest.mark.asyncio
    async def test_summary_node_process_success(self):
        """测试 SummaryNode 成功处理"""
        with patch('modules.flows.summary.summary.summary_services') as mock_summary, \
             patch('modules.flows.summary.summary.RpcFactory') as mock_rpc_factory, \
             patch('modules.flows.summary.summary.callback_parse_background') as mock_callback:
            
            # Setup mocks
            mock_summary_response = MagicMock()
            mock_summary_response.code = HTTPCODE.OK
            mock_summary_response.data = RespSummaryData(text_summary="Generated summary")
            mock_summary.return_value = mock_summary_response
            
            mock_rpc_instance = MagicMock()
            mock_rpc_factory.return_value = mock_rpc_instance
            mock_normal_model_rpc = AsyncMock()
            mock_rpc_instance.get_normal_model_rpc.return_value = mock_normal_model_rpc
            
            # Mock embedding response
            mock_embedding_response = MagicMock()
            mock_embedding_response.is_success.return_value = True
            mock_embedding_response.data.embedding = [0.1, 0.2, 0.3]
            mock_normal_model_rpc.arequest_text_embedding.return_value = mock_embedding_response
            
            mock_callback.return_value = None
            
            # Create node and context
            node = SummaryNode("summary")
            context = self.create_test_context()
            
            # Process
            result = await node.process(context)
            
            # Verify results
            assert result == context
            assert "summary" in result.handler_results
            assert result.handler_results["summary"]["summary"] == "Generated summary"
            assert result.handler_results["summary"]["summary_embedding"] == [0.1, 0.2, 0.3]
            
            # Verify calls
            mock_summary.assert_called_once()
            mock_normal_model_rpc.arequest_text_embedding.assert_called_once_with("Generated summary")
            mock_callback.assert_called_once()

    @pytest.mark.asyncio
    async def test_summary_node_process_embedding_disabled(self):
        """测试禁用嵌入的情况"""
        with patch('modules.flows.summary.summary.summary_services') as mock_summary, \
             patch('modules.flows.summary.summary.callback_parse_background') as mock_callback:
            
            mock_summary_response = MagicMock()
            mock_summary_response.code = HTTPCODE.OK
            mock_summary_response.data = RespSummaryData(text_summary="Generated summary")
            mock_summary.return_value = mock_summary_response
            
            mock_callback.return_value = None
            
            # Create node and context with embedding disabled
            node = SummaryNode("summary")
            context = self.create_test_context()
            context.embed_enabled = False
            
            # Process
            result = await node.process(context)
            
            # Verify results
            assert result == context
            assert result.handler_results["summary"]["summary"] == "Generated summary"
            assert result.handler_results["summary"]["summary_embedding"] is None

    @pytest.mark.asyncio
    async def test_summary_node_process_embedding_failure(self):
        """测试嵌入请求失败的情况"""
        with patch('modules.flows.summary.summary.summary_services') as mock_summary, \
             patch('modules.flows.summary.summary.RpcFactory') as mock_rpc_factory, \
             patch('modules.flows.summary.summary.callback_parse_background') as mock_callback:
            
            mock_summary_response = MagicMock()
            mock_summary_response.code = HTTPCODE.OK
            mock_summary_response.data = RespSummaryData(text_summary="Generated summary")
            mock_summary.return_value = mock_summary_response
            
            mock_rpc_instance = MagicMock()
            mock_rpc_factory.return_value = mock_rpc_instance
            mock_normal_model_rpc = AsyncMock()
            mock_rpc_instance.get_normal_model_rpc.return_value = mock_normal_model_rpc
            
            # Mock embedding failure
            mock_embedding_response = MagicMock()
            mock_embedding_response.is_success.return_value = False
            mock_normal_model_rpc.arequest_text_embedding.return_value = mock_embedding_response
            
            mock_callback.return_value = None
            
            # Create node and context
            node = SummaryNode("summary")
            context = self.create_test_context()
            
            # Process
            result = await node.process(context)
            
            # Verify results
            assert result == context
            assert result.handler_results["summary"]["summary_embedding"] == []

    @pytest.mark.asyncio
    async def test_summary_node_process_empty_summary(self):
        """测试空摘要的情况"""
        with patch('modules.flows.summary.summary.summary_services') as mock_summary, \
             patch('modules.flows.summary.summary.RpcFactory') as mock_rpc_factory, \
             patch('modules.flows.summary.summary.callback_parse_background') as mock_callback:
            
            mock_summary_response = MagicMock()
            mock_summary_response.code = HTTPCODE.OK
            mock_summary_response.data = RespSummaryData(text_summary="")
            mock_summary.return_value = mock_summary_response
            
            mock_rpc_instance = MagicMock()
            mock_rpc_factory.return_value = mock_rpc_instance
            mock_normal_model_rpc = AsyncMock()
            mock_rpc_instance.get_normal_model_rpc.return_value = mock_normal_model_rpc
            
            # Mock embedding response
            mock_embedding_response = MagicMock()
            mock_embedding_response.is_success.return_value = True
            mock_embedding_response.data.embedding = [0.1, 0.2, 0.3]
            mock_normal_model_rpc.arequest_text_embedding.return_value = mock_embedding_response
            
            mock_callback.return_value = None
            
            # Create node and context
            node = SummaryNode("summary")
            context = self.create_test_context()
            
            # Process
            result = await node.process(context)
            
            # Verify results
            assert result == context
            assert result.handler_results["summary"]["summary"] == ""
            assert result.handler_results["summary"]["summary_embedding"] == [0.1, 0.2, 0.3]
            
            # Verify embedding was called with empty string
            mock_normal_model_rpc.arequest_text_embedding.assert_called_once_with("")

    @pytest.mark.asyncio
    async def test_summary_node_process_exception_handling(self):
        """测试异常处理"""
        with patch('modules.flows.summary.summary.summary_services') as mock_summary:
            mock_summary.side_effect = Exception("Service error")
            
            # Create node and context
            node = SummaryNode("summary")
            context = self.create_test_context()
            
            # Process should not raise exception
            result = await node.process(context)
            
            # Should return None on error (as per the actual implementation)
            assert result is None

    @pytest.mark.asyncio
    async def test_summary_node_process_empty_dst(self):
        """测试空DST的情况"""
        with patch('modules.flows.summary.summary.summary_services') as mock_summary, \
             patch('modules.flows.summary.summary.RpcFactory') as mock_rpc_factory, \
             patch('modules.flows.summary.summary.callback_parse_background') as mock_callback:
            
            mock_summary_response = MagicMock()
            mock_summary_response.code = HTTPCODE.OK
            mock_summary_response.data = RespSummaryData(text_summary="Generated summary")
            mock_summary.return_value = mock_summary_response
            
            # Mock RPC for embedding
            mock_rpc_instance = MagicMock()
            mock_rpc_factory.return_value = mock_rpc_instance
            mock_normal_model_rpc = AsyncMock()
            mock_rpc_instance.get_normal_model_rpc.return_value = mock_normal_model_rpc
            
            # Mock embedding response
            mock_embedding_response = MagicMock()
            mock_embedding_response.is_success.return_value = True
            mock_embedding_response.data.embedding = [0.1, 0.2, 0.3]
            mock_normal_model_rpc.arequest_text_embedding.return_value = mock_embedding_response
            
            mock_callback.return_value = None
            
            # Create node and context with empty DST
            node = SummaryNode("summary")
            context = self.create_test_context()
            context.dst = []
            
            # Process
            result = await node.process(context)
            
            # Verify results
            assert result == context
            assert result.handler_results["summary"]["summary"] == "Generated summary"

    @pytest.mark.asyncio
    async def test_summary_node_process_none_dst(self):
        """测试None DST的情况"""
        with patch('modules.flows.summary.summary.summary_services') as mock_summary, \
             patch('modules.flows.summary.summary.callback_parse_background') as mock_callback:
            
            mock_summary_response = MagicMock()
            mock_summary_response.code = HTTPCODE.OK
            mock_summary_response.data = RespSummaryData(text_summary="Generated summary")
            mock_summary.return_value = mock_summary_response
            
            mock_callback.return_value = None
            
            # Create node and context with None DST
            node = SummaryNode("summary")
            context = self.create_test_context()
            context.dst = None
            
            # Process
            result = await node.process(context)
            
            # Should return None when dst is None (as per the actual implementation)
            assert result is None

    def test_summary_node_initialization(self):
        """测试 SummaryNode 初始化"""
        node = SummaryNode("test_name")
        
        assert node.name == "test_name"
        assert hasattr(node, 'process')
