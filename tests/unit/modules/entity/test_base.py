import pytest
from modules.entity.base import FileURL


class TestFileURL:
    def test_file_url_init(self):
        """Test FileURL initialization"""
        url = "https://example.com/file.pdf"
        file_url = FileURL(url)
        assert file_url == url
        assert file_url.url == url

    def test_file_url_validate_valid_url(self):
        """Test FileURL validation with valid URL"""
        url = "https://example.com/file.pdf"
        file_url = FileURL(url)
        # Should not raise an exception
        file_url.validate()

    def test_file_url_validate_invalid_url(self):
        """Test FileURL validation with invalid URL"""
        url = "invalid-url"
        file_url = FileURL(url)
        with pytest.raises(ValueError, match="Invalid file URL"):
            file_url.validate()

    def test_file_url_validate_empty_url(self):
        """Test FileURL validation with empty URL"""
        url = ""
        file_url = FileURL(url)
        with pytest.raises(ValueError, match="Invalid file URL"):
            file_url.validate()

    def test_file_url_validate_none_url(self):
        """Test FileURL validation with None URL"""
        # 由于 FileURL 继承自 str，不能直接传递 None
        # 我们测试一个无效的 URL 字符串
        url = "None"
        file_url = FileURL(url)
        with pytest.raises(ValueError, match="Invalid file URL"):
            file_url.validate()

    def test_file_url_validate_http_url(self):
        """Test FileURL validation with HTTP URL"""
        url = "http://example.com/file.pdf"
        file_url = FileURL(url)
        # Should not raise an exception
        file_url.validate()

    def test_file_url_validate_https_url_with_query(self):
        """Test FileURL validation with HTTPS URL containing query parameters"""
        url = "https://example.com/file.pdf?param=value"
        file_url = FileURL(url)
        # Should not raise an exception
        file_url.validate()
