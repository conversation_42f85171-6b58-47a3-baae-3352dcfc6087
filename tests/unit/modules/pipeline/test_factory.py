"""
modules.pipeline.factory 模块的测试
"""
import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 将项目根目录添加到 Python 路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 在导入之前模拟环境变量和模块
os.environ.setdefault('SERVICE_ID', 'test_service_id')
os.environ.setdefault('SERVICE_SECRET', 'test_service_secret')
os.environ.setdefault('SERVICE_KEY', 'test_service_key')
os.environ.setdefault('LOG_LEVEL', 'INFO')
os.environ.setdefault('aidocs_dst_ak', 'test_ak')
os.environ.setdefault('aidocs_dst_sk', 'test_sk')
os.environ.setdefault('conf_env', 'dev')

# 模拟复杂依赖
with patch.dict('sys.modules', {
    'conf': <PERSON><PERSON>(),
    'conf.setting_dev': <PERSON><PERSON>(),
    'conf.setting': <PERSON><PERSON>(),
    'commons.auth.auth_route': Mock(),
    'commons.auth.dmc_checkauth': Mock(),
    'commons.auth.auth_rpc': Mock(),
    'commons.tools.cams_decrypt': Mock(),
    'commons.logger.business_log': Mock(),
    'commons.monitor.prom': Mock(),
    'prometheus_client': Mock(),
    'modules.flows.uni_parser.uniparse_handler': Mock(),
    'modules.flows.dst_builder.dst_handler': Mock(),
    'modules.flows.dst_builder.dst_merge': Mock(),
    'modules.flows.chunk.chunk_handler': Mock(),
    'modules.flows.crop.crop': Mock(),
    'modules.flows.summary.summary': Mock(),
    'modules.flows.fake_title.fake_title': Mock(),
    'modules.flows.keywords.keywords': Mock(),
    'modules.flows.chunk_postprocess.chunk_postprocess_handler': Mock(),
    'modules.flows.dst_enhance.dst_enhance_handler': Mock(),
    'modules.flows.image_desc.desc_handler': Mock(),
    'modules.flows.chunk_recall.chunk_recall_handler': Mock(),
    'modules.flows.pre_check.pre_check_handler': Mock()
}):
    from modules.pipeline.factory import PipelineFactory
    from modules.pipeline.base import Pipeline, ParallelGroupHandler
    from services.datamodel import ReqGeneralParse, ParseTarget, FileType


class TestPipelineFactory:
    """PipelineFactory 类的测试"""

    def setup_method(self):
        """设置测试夹具"""
        self.factory = PipelineFactory()
    
    def test_create_document_pipeline_without_chunk_target(self):
        """测试创建没有块目标的文档管道"""
        # 创建没有块目标的请求
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.summary, ParseTarget.fake_title, ParseTarget.keywords],
            need_img_desc=True
        )

        pipeline = self.factory.create_document_pipeline("test_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "test_pipeline"
        assert len(pipeline.handlers) > 0

        # Basic verification - pipeline should be created successfully
        # Since no chunk target is specified, it should use the non-chunk path
        assert len(pipeline.handlers) >= 3  # At least chunk_recall, parallel_group, chunk_post_process
    
    def test_create_document_pipeline_with_screenshot_target(self):
        """测试创建带有截图目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.screenshot],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("test_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "test_pipeline"
        assert len(pipeline.handlers) > 0

        # Basic verification - pipeline should be created successfully
        # Screenshot target without chunk should use the non-chunk path
        assert len(pipeline.handlers) >= 2  # At least chunk_recall and chunk_post_process
    
    def test_create_document_pipeline_with_chunk_target(self):
        """测试创建带有块目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("test_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "test_pipeline"
        assert len(pipeline.handlers) > 0

        # Chunk target should use the full processing pipeline
        assert len(pipeline.handlers) >= 3  # Should have multiple handlers for full processing
    
    def test_create_document_pipeline_with_chunk_and_screenshot(self):
        """测试创建同时带有块和截图目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk, ParseTarget.screenshot],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("test_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "test_pipeline"
        assert len(pipeline.handlers) > 0

        # Chunk target should use the full processing pipeline
        # Should have PreCheck, UniParse, DocumentParser, DSTMerge, and sub-pipeline handler
        assert len(pipeline.handlers) >= 4
    
    def test_create_document_pipeline_with_img_desc(self):
        """测试创建带有图像描述的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk, ParseTarget.screenshot],
            need_img_desc=True
        )

        pipeline = self.factory.create_document_pipeline("test_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "test_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have handlers for chunk processing with image description
        assert len(pipeline.handlers) >= 4
    
    def test_create_document_pipeline_with_summary_only(self):
        """测试创建仅带有摘要目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.summary],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("test_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "test_pipeline"
        assert len(pipeline.handlers) > 0

        # Summary target without chunk should use the non-chunk path
        assert len(pipeline.handlers) >= 3  # chunk_recall, parallel_group, chunk_post_process
    
    def test_create_document_pipeline_empty_targets(self):
        """测试创建带有空解析目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("test_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "test_pipeline"
        assert len(pipeline.handlers) > 0

        # Empty targets should still create a valid pipeline
        assert len(pipeline.handlers) >= 1


    def test_create_document_pipeline_all_targets(self):
        """测试创建带有所有解析目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[
                ParseTarget.chunk, ParseTarget.screenshot,
                ParseTarget.summary, ParseTarget.fake_title, ParseTarget.keywords
            ],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("all_targets_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "all_targets_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have multiple handlers for all targets with chunk processing
        assert len(pipeline.handlers) >= 4

    def test_create_document_pipeline_invalid_request(self):
        """测试使用 None 请求创建文档管道"""
        with pytest.raises(AttributeError):
            self.factory.create_document_pipeline("test", None)

    def test_create_document_pipeline_no_targets_no_img_desc(self):
        """测试创建没有目标和图像描述的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("minimal_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "minimal_pipeline"
        assert len(pipeline.handlers) > 0

        # Minimal pipeline should still have basic handlers
        assert len(pipeline.handlers) >= 1
        # Should have minimal handlers
        assert len(pipeline.handlers) >= 1

    def test_create_document_pipeline_with_chunk_and_img_desc_only(self):
        """测试创建只有chunk和img_desc目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk, ParseTarget.img_desc],
            need_img_desc=True
        )

        pipeline = self.factory.create_document_pipeline("chunk_img_desc_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "chunk_img_desc_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have handlers for chunk processing with image description
        assert len(pipeline.handlers) >= 4

    def test_create_document_pipeline_with_chunk_and_screenshot_and_img_desc(self):
        """测试创建同时带有chunk、screenshot和img_desc目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk, ParseTarget.screenshot, ParseTarget.img_desc],
            need_img_desc=True
        )

        pipeline = self.factory.create_document_pipeline("complex_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "complex_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have complex pipeline with sub-pipelines
        assert len(pipeline.handlers) >= 4

    def test_create_document_pipeline_with_chunk_and_summary(self):
        """测试创建带有chunk和summary目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk, ParseTarget.summary],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("chunk_summary_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "chunk_summary_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have handlers for chunk processing with summary
        assert len(pipeline.handlers) >= 4

    def test_create_document_pipeline_with_chunk_and_fake_title(self):
        """测试创建带有chunk和fake_title目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk, ParseTarget.fake_title],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("chunk_fake_title_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "chunk_fake_title_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have handlers for chunk processing with fake title
        assert len(pipeline.handlers) >= 4

    def test_create_document_pipeline_with_chunk_and_keywords(self):
        """测试创建带有chunk和keywords目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk, ParseTarget.keywords],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("chunk_keywords_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "chunk_keywords_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have handlers for chunk processing with keywords
        assert len(pipeline.handlers) >= 4

    def test_create_document_pipeline_with_chunk_and_screenshot_only(self):
        """测试创建只有chunk和screenshot目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk, ParseTarget.screenshot],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("chunk_screenshot_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "chunk_screenshot_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have handlers for chunk processing with screenshot
        assert len(pipeline.handlers) >= 4

    def test_create_document_pipeline_with_chunk_only(self):
        """测试创建只有chunk目标的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("chunk_only_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "chunk_only_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have handlers for chunk processing only
        assert len(pipeline.handlers) >= 4

    def test_create_document_pipeline_with_empty_parse_target(self):
        """测试创建带有空parse_target的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("empty_target_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "empty_target_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have minimal handlers for empty targets
        assert len(pipeline.handlers) >= 1

    def test_create_document_pipeline_with_none_parse_target(self):
        """测试创建带有None parse_target的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=None,
            need_img_desc=False
        )

        # This should raise an error because parse_target cannot be None
        with pytest.raises(TypeError, match="'NoneType' object is not iterable"):
            self.factory.create_document_pipeline("none_target_pipeline", request)

    def test_create_document_pipeline_with_screenshot_only_no_chunk(self):
        """测试创建只有screenshot目标且没有chunk的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.screenshot],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("screenshot_only_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "screenshot_only_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have handlers for screenshot processing without chunk
        # This should trigger the non-chunk path and cover line 36
        assert len(pipeline.handlers) >= 3

    def test_create_document_pipeline_with_chunk_and_no_parse_target(self):
        """测试创建带有chunk目标但没有parse_target的文档管道"""
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[],
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("chunk_no_target_pipeline", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "chunk_no_target_pipeline"
        assert len(pipeline.handlers) > 0

        # Should have handlers for chunk processing without additional targets
        assert len(pipeline.handlers) >= 3

    def test_create_document_pipeline_with_chunk_and_screenshot_not_both(self):
        """测试创建带有chunk和screenshot目标但不满足both条件的文档管道"""
        # This test should trigger the else branch that covers line 100
        request = ReqGeneralParse(
            file_name="test.pdf",
            parse_target=[ParseTarget.chunk],  # Only chunk, not both chunk and screenshot
            need_img_desc=False
        )

        pipeline = self.factory.create_document_pipeline("chunk_only_pipeline_else_branch", request)

        assert isinstance(pipeline, Pipeline)
        assert pipeline.name == "chunk_only_pipeline_else_branch"
        assert len(pipeline.handlers) > 0

        # Should have handlers for chunk processing in else branch
        assert len(pipeline.handlers) >= 4
