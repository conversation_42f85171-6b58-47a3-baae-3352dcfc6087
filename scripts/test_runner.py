#!/usr/bin/env python3
"""
InsightAI 项目的测试运行器脚本

此脚本提供了一个全面的测试运行器，具有以下功能：
1. 按目录、特定测试或所有测试运行测试
2. 生成覆盖率报告
3. Bash 自动补全支持
4. 帮助和使用信息
"""

import os
import sys
import argparse
import subprocess
import glob
from pathlib import Path
from typing import List, Optional


class TestRunner:
    """具有各种测试执行选项的测试运行器类"""

    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.tests_dir = self.project_root / "tests"
        
    def run_tests(self,
                  test_path: Optional[str] = None,
                  coverage: bool = False,
                  verbose: bool = False,
                  pattern: Optional[str] = None) -> int:
        """
        使用指定选项运行测试

        Args:
            test_path: 要运行的特定测试路径
            coverage: 是否生成覆盖率报告
            verbose: 详细输出
            pattern: 要匹配的测试模式

        Returns:
            退出代码（0表示成功，非零表示失败）
        """
        cmd = ["python", "-m", "pytest"]
        
        # 添加测试路径
        if test_path:
            if not os.path.exists(test_path):
                print(f"错误：测试路径 '{test_path}' 不存在")
                return 1
            cmd.append(test_path)
        else:
            cmd.append(str(self.tests_dir))

        # 添加详细标志
        if verbose:
            cmd.append("-v")

        # 添加模式匹配
        if pattern:
            cmd.extend(["-k", pattern])

        # 添加覆盖率选项
        if coverage:
            cmd.extend([
                "--cov=modules",
                "--cov=commons", 
                "--cov=services",
                "--cov=routers",
                "--cov-report=html:htmlcov",
                "--cov-report=term-missing",
                "--cov-report=xml:coverage.xml"
            ])
        
        print(f"运行命令：{' '.join(cmd)}")

        try:
            result = subprocess.run(cmd, cwd=self.project_root)
            return result.returncode
        except KeyboardInterrupt:
            print("\n测试执行被用户中断")
            return 130
        except Exception as e:
            print(f"运行测试时出错：{e}")
            return 1

    def list_tests(self, directory: Optional[str] = None) -> List[str]:
        """列出可用的测试"""
        if directory:
            search_path = self.tests_dir / directory
        else:
            search_path = self.tests_dir
            
        if not search_path.exists():
            return []
            
        test_files = []
        for pattern in ["**/test_*.py", "**/test*.py"]:
            test_files.extend(glob.glob(str(search_path / pattern), recursive=True))
        
        # 使路径相对于项目根目录
        relative_paths = []
        for test_file in test_files:
            rel_path = os.path.relpath(test_file, self.project_root)
            relative_paths.append(rel_path)

        return sorted(relative_paths)

    def generate_completion_script(self) -> str:
        """生成 bash 自动补全脚本"""
        completion_script = '''#!/bin/bash
# test_runner.py 的 Bash 自动补全

_test_runner_completion() {
    local cur prev opts
    COMPREPLY=()
    cur="${COMP_WORDS[COMP_CWORD]}"
    prev="${COMP_WORDS[COMP_CWORD-1]}"
    
    # 主要命令
    opts="run list coverage help completion"

    # 测试目录
    test_dirs="unit integration unit/pipeline unit/auth unit/llm unit/chunk unit/storage unit/ocr unit/rpc"
    
    case "${prev}" in
        test_runner.py|run)
            case "${cur}" in
                --*)
                    COMPREPLY=( $(compgen -W "--directory --test --pattern --verbose --coverage" -- ${cur}) )
                    return 0
                    ;;
                *)
                    # 使用测试目录和文件进行补全
                    local test_files=$(python3 scripts/test_runner.py list 2>/dev/null | grep -E "test_.*\\.py$" | sed 's|tests/||')
                    COMPREPLY=( $(compgen -W "${test_dirs} ${test_files}" -- ${cur}) )
                    return 0
                    ;;
            esac
            ;;
        --directory|-d)
            COMPREPLY=( $(compgen -W "${test_dirs}" -- ${cur}) )
            return 0
            ;;
        --test|-t)
            local test_files=$(python3 scripts/test_runner.py list 2>/dev/null | grep -E "test_.*\\.py$")
            COMPREPLY=( $(compgen -W "${test_files}" -- ${cur}) )
            return 0
            ;;
        list)
            COMPREPLY=( $(compgen -W "${test_dirs}" -- ${cur}) )
            return 0
            ;;
        *)
            COMPREPLY=( $(compgen -W "${opts}" -- ${cur}) )
            return 0
            ;;
    esac
}

complete -F _test_runner_completion test_runner.py
complete -F _test_runner_completion python3\ scripts/test_runner.py

# 安装此自动补全：
# 1. 将此脚本保存到 ~/.bash_completion.d/test_runner
# 2. 加载它：source ~/.bash_completion.d/test_runner
# 3. 或添加到 ~/.bashrc：source ~/.bash_completion.d/test_runner
'''
        return completion_script


def main():
    """主入口点"""
    parser = argparse.ArgumentParser(
        description="InsightAI 项目的测试运行器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例：
  %(prog)s run                                    # 运行所有测试
  %(prog)s run --directory unit/pipeline         # 运行管道测试
  %(prog)s run --test tests/unit/pipeline/test_context.py  # 运行特定测试
  %(prog)s run --pattern "test_context"          # 运行匹配模式的测试
  %(prog)s run --coverage                        # 运行并生成覆盖率
  %(prog)s list                                  # 列出所有测试
  %(prog)s list unit/pipeline                    # 列出管道测试
  %(prog)s coverage                              # 仅生成覆盖率报告
  %(prog)s completion                            # 生成 bash 自动补全
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 运行命令
    run_parser = subparsers.add_parser("run", help="运行测试")
    run_parser.add_argument("-d", "--directory", help="要运行的测试目录")
    run_parser.add_argument("-t", "--test", help="要运行的特定测试文件")
    run_parser.add_argument("-p", "--pattern", help="要匹配的测试模式")
    run_parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    run_parser.add_argument("-c", "--coverage", action="store_true", help="生成覆盖率报告")

    # 列表命令
    list_parser = subparsers.add_parser("list", help="列出可用测试")
    list_parser.add_argument("directory", nargs="?", help="要列出测试的目录")

    # 覆盖率命令
    coverage_parser = subparsers.add_parser("coverage", help="生成覆盖率报告")

    # 自动补全命令
    completion_parser = subparsers.add_parser("completion", help="生成 bash 自动补全脚本")

    # 帮助命令
    help_parser = subparsers.add_parser("help", help="显示帮助信息")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.command == "run" or args.command is None:
        # 确定测试路径
        test_path = None
        if args.test:
            test_path = args.test
        elif args.directory:
            test_path = str(runner.tests_dir / args.directory)

        return runner.run_tests(
            test_path=test_path,
            coverage=args.coverage,
            verbose=args.verbose,
            pattern=args.pattern
        )

    elif args.command == "list":
        tests = runner.list_tests(args.directory)
        if tests:
            for test in tests:
                print(test)
        else:
            print("未找到测试")
        return 0

    elif args.command == "coverage":
        return runner.run_tests(coverage=True)

    elif args.command == "completion":
        print(runner.generate_completion_script())
        return 0

    elif args.command == "help":
        parser.print_help()
        return 0

    else:
        parser.print_help()
        return 1


if __name__ == "__main__":
    sys.exit(main())
