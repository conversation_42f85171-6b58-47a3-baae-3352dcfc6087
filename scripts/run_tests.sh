#!/bin/bash

# 设置测试所需的环境变量
export SERVICE_ID=test_service_id
export SERVICE_KEY=test_service_key
export AK=test_ak
export SK=test_sk
export SK_SALT=test_sk_salt
export conf_env=test
export LOG_LEVEL=INFO
export MONITOR_NAME=test-monitor
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=""
export REDIS_DB=0
export MYSQL_HOST=localhost
export MYSQL_PORT=3306
export MYSQL_USER=test
export MYSQL_PASSWORD=test
export MYSQL_DATABASE=test
export KAFKA_BOOTSTRAP_SERVERS=localhost:9092
export PARSE_VERSION=v1.0.0-test
export TRACE_VERBOSE=0
export PROMETHEUS_GATEWAY=localhost:9091
export PROMETHEUS_JOB_NAME_PREFIX=test
export PROMETHEUS_USERNAME=""
export PROMETHEUS_PASSWORD=""
export WPS365_API_HOST=https://test-api.wps.cn
export WPS365_POOL_MAX=10
export WPS365_OPENAPI_HOST=https://test-openapi.wps.cn
export WPS365_OPENAPI_AK=test_openapi_ak
export WPS365_OPENAPI_SK=test_openapi_sk
export WPS365_WOA_CUSTOM_AK=test_woa_ak
export STORE_HOST=https://test-store.wps.cn
export STORE_AK=test_store_ak
export STORE_SK=test_store_sk
export STORE_BUCKET=test-bucket
export LLM_GATEWAY_HOST=https://test-llm.wps.cn
export LLM_GATEWAY_AK=test_llm_ak
export LLM_GATEWAY_SK=test_llm_sk
export OCR_MODEL_HOST=https://test-ocr.wps.cn
export RECALL_CHUNK_HOST=https://test-recall.wps.cn
export INSIGHT_HOST=https://test-insight.wps.cn
export QSEARCH_HOST=https://test-qsearch.wps.cn
export WPS365_KDC_HOST=https://test-kdc.wps.cn
export DRIVE_V5_RPC_HOST=https://test-drive.wps.cn
export DMC_HOST=https://test-dmc.wps.cn
export aidocs_dst_ak=test_aidocs_ak
export aidocs_dst_sk=test_aidocs_sk
export insight_ai_host=http://test-insight-ai.wps.cn
export insight_ai_auth_platform=dmc
export step_fake_title_host=http://test-step.wps.cn
export step_keyword_host=http://test-step.wps.cn
export step_chunk_host=http://test-step.wps.cn
export step_summary_host=http://test-step.wps.cn
export step_screen_shot_host=http://test-step.wps.cn
export insight_host=http://test-insight.wps.cn
export insight_sig_type=wps2
export recall_chunk_host=http://test-recall.wps.cn
export recall_chunk_sig_type=wps2
export qsearch_host=http://test-qsearch.wps.cn
export qsearch_auth_platform=dmc

# 运行测试
python -m pytest "$@"
