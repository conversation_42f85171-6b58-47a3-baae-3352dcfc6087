# InsightAI 测试框架

这是 InsightAI 项目的测试框架，使用 pytest 作为测试运行器，提供了完整的单元测试和集成测试支持。

## 目录结构

```
tests/
├── unit/                    # 单元测试
│   ├── pipeline/           # Pipeline测试
│   │   ├── test_context.py
│   │   ├── test_factory.py
│   │   └── test_handlers.py
│   ├── auth/               # 认证测试
│   ├── llm/                # LLM网关测试
│   ├── chunk/              # 分块测试
│   ├── storage/            # 存储测试
│   ├── ocr/                # OCR测试
│   └── rpc/                # RPC测试
├── integration/            # 集成测试
│   ├── test_parse_pipeline.py
│   ├── test_kafka_flow.py
│   └── test_end_to_end.py
└── fixtures/               # 测试数据
    ├── documents/
    ├── kdc_responses/
    └── mock_configs/
```

## 快速开始

### 1. 安装依赖

确保已安装必要的测试依赖：

```bash
pip install pytest pytest-cov pytest-asyncio
```

### 2. 运行测试

使用我们提供的测试运行脚本：

```bash
# 运行所有测试
python scripts/test_runner.py run

# 运行特定目录的测试
python scripts/test_runner.py run --directory unit/pipeline

# 运行特定测试文件
python scripts/test_runner.py run --test tests/unit/pipeline/test_context.py

# 运行匹配模式的测试
python scripts/test_runner.py run --pattern "test_context"

# 运行测试并生成覆盖率报告
python scripts/test_runner.py run --coverage

# 详细输出
python scripts/test_runner.py run --verbose
```

### 3. 查看测试列表

```bash
# 列出所有测试
python scripts/test_runner.py list

# 列出特定目录的测试
python scripts/test_runner.py list unit/pipeline
```

### 4. 生成覆盖率报告

```bash
# 只生成覆盖率报告
python scripts/test_runner.py coverage
```

覆盖率报告将生成在：
- HTML报告：`htmlcov/index.html`
- XML报告：`coverage.xml`
- 终端输出：显示缺失的行号

### 5. Bash 自动补全

生成并安装 bash 自动补全：

```bash
# 生成补全脚本
python scripts/test_runner.py completion > ~/.bash_completion.d/test_runner

# 加载补全
source ~/.bash_completion.d/test_runner

# 或者添加到 ~/.bashrc
echo "source ~/.bash_completion.d/test_runner" >> ~/.bashrc
```

## 测试编写指南

### 单元测试示例

```python
import pytest
from unittest.mock import Mock, patch

class TestMyClass:
    """测试 MyClass 类"""
    
    def test_method_success(self):
        """测试方法成功情况"""
        # Arrange
        obj = MyClass()
        
        # Act
        result = obj.method()
        
        # Assert
        assert result == expected_value
    
    @pytest.mark.asyncio
    async def test_async_method(self):
        """测试异步方法"""
        obj = MyClass()
        result = await obj.async_method()
        assert result is not None
    
    @patch('module.dependency')
    def test_with_mock(self, mock_dependency):
        """使用 mock 的测试"""
        mock_dependency.return_value = "mocked"
        obj = MyClass()
        result = obj.method_with_dependency()
        assert result == "mocked"
```

### 测试标记

使用 pytest 标记来分类测试：

```python
@pytest.mark.unit
def test_unit_function():
    pass

@pytest.mark.integration
def test_integration_function():
    pass

@pytest.mark.slow
def test_slow_function():
    pass

@pytest.mark.skip(reason="暂时跳过")
def test_skipped_function():
    pass
```

### 测试数据和 Fixtures

将测试数据放在 `tests/fixtures/` 目录下：

```python
@pytest.fixture
def sample_data():
    """提供测试数据"""
    return {"key": "value"}

def test_with_fixture(sample_data):
    assert sample_data["key"] == "value"
```

## 配置

### pytest.ini

项目根目录的 `pytest.ini` 文件包含了 pytest 的配置：

```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts = --strict-markers --strict-config --verbose
```

### 环境变量

测试运行时会自动设置必要的环境变量，避免配置问题。

## 最佳实践

1. **测试命名**：使用描述性的测试名称，说明测试的场景
2. **测试隔离**：每个测试应该独立，不依赖其他测试的状态
3. **Mock 使用**：对外部依赖使用 mock，确保测试的可靠性
4. **断言清晰**：使用清晰的断言，便于理解测试失败的原因
5. **测试覆盖**：编写测试时考虑边界情况和异常情况

## 故障排除

### 常见问题

1. **导入错误**：确保项目路径在 Python 路径中
2. **环境变量**：某些测试可能需要特定的环境变量
3. **依赖问题**：确保所有测试依赖都已安装

### 调试测试

```bash
# 运行单个测试并显示详细输出
python -m pytest tests/unit/pipeline/test_context.py::TestKDCInput::test_kdc_input_creation -v -s

# 在测试失败时进入调试器
python -m pytest --pdb

# 显示最慢的 10 个测试
python -m pytest --durations=10
```

## 持续集成

这个测试框架可以很容易地集成到 CI/CD 流水线中：

```bash
# CI 脚本示例
python scripts/test_runner.py run --coverage
# 检查覆盖率阈值
# 上传覆盖率报告
```

## 贡献

在添加新功能时，请确保：

1. 为新代码编写相应的单元测试
2. 测试覆盖率不低于现有水平
3. 所有测试都能通过
4. 遵循项目的测试编写规范
