#!/bin/bash

CUR_DIR=$(dirname $(readlink -f $0))
# source ${CUR_DIR}/../../aidocs-api-prod.conf
# source ${CUR_DIR}/../../aidocs-api.conf
# source ${CUR_DIR}/../../aidocs-dst-server-prod.conf
source ${CUR_DIR}/../../aidocs-dst-server-gray.conf
source ${CUR_DIR}/../../aidocs-dst-server.conf

export http_proxy=http://localhost:18899
export https_proxy=http://localhost:18899

export REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt
source /data/deploy/pyvenvs/pydst/bin/activate

set -x
# pip install -r requirements.txt  -i https://pypi.tuna.tsinghua.edu.cn/simple 

cp /data/projects/kingsoft/personal/workspace/patches/tritonclient/2.57.0/http/_client.py /data/deploy/pyvenvs/pydst/lib/python3.10/site-packages/tritonclient/http/_client.py
cp /data/projects/kingsoft/personal/workspace/patches/tritonclient/2.57.0/http/aio/__init__.py /data/deploy/pyvenvs/pydst/lib/python3.10/site-packages/tritonclient/http/aio/__init__.py

# export PYTHONPATH=$(pwd):${PYTHONPATH}

# 全局标志防止重复清理
CLEANUP_DONE=false

# 捕获信号并优雅退出
cleanup() {
    # 防止重复执行
    if [ "$CLEANUP_DONE" = true ]; then
        echo "Cleanup already in progress, exiting..."
        exit 0
    fi
    CLEANUP_DONE=true
    
    echo "Received exit signal, shutting down gracefully..."
    if [[ -n $PID ]]; then
        # 检查进程是否还在运行
        if kill -0 $PID 2>/dev/null; then
            echo "Killing process $PID..."
            kill -TERM $PID 2>/dev/null || true
            
            # 等待最多3秒让进程优雅退出
            for i in {1..3}; do
                if ! kill -0 $PID 2>/dev/null; then
                    echo "Process $PID exited gracefully"
                    break
                fi
                echo "Waiting for process $PID to exit... ($i/3)"
                sleep 1
            done
            
            # 如果还在运行，强制终止
            if kill -0 $PID 2>/dev/null; then
                echo "Force killing process $PID..."
                kill -9 $PID 2>/dev/null || true
                sleep 1
            fi
        else
            echo "Process $PID already exited"
        fi
    fi
    echo "Shutdown complete."
    exit 0
}

trap cleanup SIGINT SIGTERM

# 创建日志目录
mkdir -p log

# 生成带时间戳的日志文件名
# LOG_FILE="log/app_$(date +%Y%m%d_%H%M%S).log"
LOG_FILE="log/app.log"


# 启动应用
echo "Cleaning up old processes..."
python cleanup_processes.py
echo "Starting application..."
echo "Logs will be written to: $LOG_FILE"
python app.py
# python app.py > "$LOG_FILE" 2>&1 &
PID=$!

echo "Application started with PID: $PID"
echo "Press Ctrl+C to stop the application gracefully."

# 等待进程结束，或者用户中断
echo "Waiting for application to finish (PID: $PID)..."
wait $PID 2>/dev/null || true
echo "Application process has exited."
