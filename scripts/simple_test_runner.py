#!/usr/bin/env python3
"""
不需要 pytest 的简单测试运行器
"""

import sys
import os
import importlib.util
import traceback
import asyncio
from pathlib import Path

# 将项目根目录添加到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_test_function(test_func, test_name):
    """运行单个测试函数"""
    try:
        if asyncio.iscoroutinefunction(test_func):
            # 运行异步测试
            asyncio.run(test_func())
        else:
            # 运行同步测试
            test_func()
        print(f"✓ {test_name}")
        return True
    except Exception as e:
        print(f"✗ {test_name}: {str(e)}")
        traceback.print_exc()
        return False


def discover_and_run_tests(test_file_path):
    """发现并运行文件中的测试"""
    print(f"\n在 {test_file_path} 中运行测试")
    print("=" * 50)

    # 加载测试模块
    spec = importlib.util.spec_from_file_location("test_module", test_file_path)
    test_module = importlib.util.module_from_spec(spec)

    try:
        spec.loader.exec_module(test_module)
    except Exception as e:
        print(f"加载测试模块失败：{e}")
        traceback.print_exc()
        return 0, 1

    # 查找测试函数和类
    passed = 0
    failed = 0
    
    for name in dir(test_module):
        obj = getattr(test_module, name)
        
        # 测试函数
        if name.startswith('test_') and callable(obj):
            if run_test_function(obj, name):
                passed += 1
            else:
                failed += 1

        # 测试类
        elif name.startswith('Test') and isinstance(obj, type):
            # 创建测试类实例
            try:
                test_instance = obj()

                # 如果存在则运行设置
                if hasattr(test_instance, 'setup_method'):
                    test_instance.setup_method()

                # 查找并运行测试方法
                for method_name in dir(test_instance):
                    if method_name.startswith('test_') and callable(getattr(test_instance, method_name)):
                        method = getattr(test_instance, method_name)
                        test_name = f"{name}.{method_name}"
                        if run_test_function(method, test_name):
                            passed += 1
                        else:
                            failed += 1

                # 如果存在则运行清理
                if hasattr(test_instance, 'teardown_method'):
                    test_instance.teardown_method()

            except Exception as e:
                print(f"✗ 实例化 {name} 失败：{e}")
                failed += 1
    
    return passed, failed


def main():
    """主入口点"""
    if len(sys.argv) < 2:
        print("用法：python simple_test_runner.py <测试文件或目录>")
        sys.exit(1)

    test_path = Path(sys.argv[1])

    if not test_path.exists():
        # 尝试相对于项目根目录
        test_path = project_root / test_path
        if not test_path.exists():
            print(f"未找到测试路径：{sys.argv[1]}")
            sys.exit(1)

    total_passed = 0
    total_failed = 0

    if test_path.is_file():
        # 单个测试文件
        passed, failed = discover_and_run_tests(test_path)
        total_passed += passed
        total_failed += failed
    else:
        # 目录 - 查找所有测试文件
        test_files = list(test_path.glob("**/test_*.py"))
        if not test_files:
            print(f"在 {test_path} 中未找到测试文件")
            sys.exit(1)

        for test_file in test_files:
            passed, failed = discover_and_run_tests(test_file)
            total_passed += passed
            total_failed += failed

    print("\n" + "=" * 50)
    print(f"总计：{total_passed + total_failed} 个测试")
    print(f"通过：{total_passed}")
    print(f"失败：{total_failed}")

    if total_failed > 0:
        sys.exit(1)
    else:
        print("所有测试都通过了！")
        sys.exit(0)


if __name__ == "__main__":
    main()
