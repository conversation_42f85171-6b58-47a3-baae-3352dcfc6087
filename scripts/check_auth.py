#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鉴权配置诊断脚本
用于检查DMC鉴权相关的环境变量和连接状态
"""

import os
import sys
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_environment_variables():
    """检查必要的环境变量"""
    print("🔍 检查环境变量...")
    
    required_vars = {
        'SERVICE_ID': '服务ID',
        'SERVICE_KEY': '服务密钥',
    }
    
    optional_vars = {
        'DMC_HOST': 'DMC主机地址',
        'AUTH_PLATFORM': '鉴权平台',
    }
    
    missing_vars = []
    
    print("\n📋 必需的环境变量:")
    for var, desc in required_vars.items():
        value = os.environ.get(var)
        if value:
            print(f"  ✅ {var} ({desc}): {value[:8]}*** (已设置)")
        else:
            print(f"  ❌ {var} ({desc}): 未设置")
            missing_vars.append(var)
    
    print("\n📋 可选的环境变量:")
    for var, desc in optional_vars.items():
        value = os.environ.get(var)
        if value:
            print(f"  ✅ {var} ({desc}): {value}")
        else:
            default_values = {
                'DMC_HOST': 'https://dmc.wps.cn',
                'AUTH_PLATFORM': '0 (dmc)'
            }
            default = default_values.get(var, '未知')
            print(f"  ⚠️  {var} ({desc}): 未设置 (默认: {default})")
    
    return missing_vars

def test_dmc_connection():
    """测试DMC连接"""
    print("\n🌐 测试DMC连接...")
    
    try:
        # 添加当前目录到Python路径
        sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        from commons.auth.auth_rpc import AuthRequest, SigVerType
        
        host = os.environ.get('DMC_HOST', 'https://dmc.wps.cn')
        ak = os.environ.get('SERVICE_ID')
        sk = os.environ.get('SERVICE_KEY')
        
        if not ak or not sk:
            print("  ❌ SERVICE_ID 或 SERVICE_KEY 未设置，无法测试连接")
            return False
        
        print(f"  📡 连接到: {host}")
        print(f"  🔑 使用AK: {ak[:8]}***")
        
        auth_rpc = AuthRequest(host, ak, sk, SigVerType.wps2)
        code, text = auth_rpc.call("POST", f"/acl/v2/init?v=v2")
        
        print(f"  📊 响应码: {code}")
        print(f"  📝 响应内容: {text}")
        
        if code == 200:
            try:
                result = json.loads(text)
                if result.get("result") == "ok":
                    print("  ✅ DMC连接成功!")
                    return True
                else:
                    print(f"  ❌ DMC初始化失败: {result}")
                    return False
            except json.JSONDecodeError as e:
                print(f"  ❌ 响应格式错误: {e}")
                return False
        else:
            print(f"  ❌ DMC连接失败，HTTP状态码: {code}")
            return False
            
    except ImportError as e:
        print(f"  ❌ 导入模块失败: {e}")
        print("  💡 请确保在项目根目录运行此脚本")
        return False
    except Exception as e:
        print(f"  ❌ 连接测试失败: {e}")
        return False

def check_network():
    """检查网络连接"""
    print("\n🌍 检查网络连接...")
    
    import socket
    import urllib.parse
    
    host = os.environ.get('DMC_HOST', 'https://dmc.wps.cn')
    parsed = urllib.parse.urlparse(host)
    hostname = parsed.hostname
    port = parsed.port or (443 if parsed.scheme == 'https' else 80)
    
    try:
        print(f"  📡 测试连接到 {hostname}:{port}")
        sock = socket.create_connection((hostname, port), timeout=10)
        sock.close()
        print(f"  ✅ 网络连接正常")
        return True
    except Exception as e:
        print(f"  ❌ 网络连接失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔐 DMC鉴权配置诊断工具")
    print("=" * 60)
    
    # 检查环境变量
    missing_vars = check_environment_variables()
    
    # 检查网络连接
    network_ok = check_network()
    
    # 测试DMC连接
    dmc_ok = False
    if not missing_vars and network_ok:
        dmc_ok = test_dmc_connection()
    else:
        print("\n⚠️  跳过DMC连接测试 (缺少必要的环境变量或网络连接失败)")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 诊断结果总结:")
    print("=" * 60)
    
    if missing_vars:
        print(f"❌ 缺少必要的环境变量: {', '.join(missing_vars)}")
    else:
        print("✅ 所有必要的环境变量已设置")
    
    if network_ok:
        print("✅ 网络连接正常")
    else:
        print("❌ 网络连接异常")
    
    if dmc_ok:
        print("✅ DMC鉴权服务连接正常")
    else:
        print("❌ DMC鉴权服务连接异常")
    
    print("\n💡 建议:")
    if missing_vars:
        print("1. 设置缺少的环境变量")
        print("   export SERVICE_ID='your_service_id'")
        print("   export SERVICE_KEY='your_service_key'")
    
    if not network_ok:
        print("2. 检查网络连接和防火墙设置")
        print("3. 确认DMC_HOST配置是否正确")
    
    if not dmc_ok and not missing_vars and network_ok:
        print("4. 检查SERVICE_ID和SERVICE_KEY是否正确")
        print("5. 联系管理员确认鉴权服务状态")
    
    print("\n🚀 如果所有检查都通过，请重新启动应用")

if __name__ == "__main__":
    main() 