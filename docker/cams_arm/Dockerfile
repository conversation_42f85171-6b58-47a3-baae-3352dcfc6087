FROM hub-mirror.wps.cn/kna/aidocs_dst_server-base-cams-arm:2509b_v1 as builder

LABEL maintainer="<EMAIL>"

ARG MODEL_BUILD_KEY

WORKDIR /app
USER root
RUN chown -R kna:kna /app
USER kna:kna

RUN mkdir aidocs_dst_server

COPY --chown=kna:kna yundata-aigc-InsightAI-aidocs_dst_server ./aidocs_dst_server

WORKDIR /app/aidocs_dst_server
RUN sed -i "s/MODEL_BUILD_KEY/${MODEL_BUILD_KEY}/g" /app/aidocs_dst_server/commons/crypt/aes_crypt.py

# 部分系统会检测unix换行符，需转换
RUN dos2unix docker/start.sh
RUN rm /app/aidocs_dst_server/docker/Dockerfile* && rm /app/aidocs_dst_server/docker/cams_x86/Dockerfile*

###########################################################################

FROM hub-mirror.wps.cn/kna/aidocs_dst_server-base-cams-arm:2509b_v1

LABEL maintainer="<EMAIL>"

WORKDIR /app
USER root
RUN chown -R kna:kna /app
RUN yum update -y && yum install -y cairo ImageMagick

USER kna:kna

COPY --chown=kna:kna --from=builder /app/aidocs_dst_server ./aidocs_dst_server

# 添加执行权限
RUN chmod +x /app/aidocs_dst_server/docker/start.sh

COPY --chown=kna:kna --from=builder /app/aidocs_dst_server/requirements_pri.txt ./requirements.txt
RUN /usr/local/bin/pip3 install -r ./requirements.txt --index https://mirrors.wps.cn/pypi/simple/ --trusted-host mirrors.wps.cn

RUN rm -rf /app/requirements.txt

WORKDIR /app

EXPOSE 8080

ENTRYPOINT ["/usr/bin/tini", "--"]

CMD ["/bin/bash", "/app/aidocs_dst_server/docker/start.sh"]
